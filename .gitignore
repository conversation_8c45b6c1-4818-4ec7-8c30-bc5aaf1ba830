# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Build output
/dist
/build
/out
/.next
/.nuxt

# Local environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDEs and editors
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# System files
.DS_Store
Thumbs.db

# Test coverage
/coverage
/lcov-report

# Next.js
.next/
out/

# Nuxt.js
.nuxt/
dist/

# Gatsby
.cache/

# SvelteKit
.svelte-kit/

# Vue
.vuepress/dist

# Temporary files
*.tmp
*.temp
*~

# Misc
.env.development
.env.production
.env.test
.env.staging
.env.*.local
.env.local
.env.development.local
.env.production.local
.env.test.local
.env.staging.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
pid.[0-9]*.log
pid.[0-9]*.log.[0-9]*
pids
*.pid
*.seed
*.pid.lock
.npm
.eslintcache
.stylelintcache
.cache
.cache-loader
.parcel-cache
.cache/
.nyc_output
.grunt
.lock-wscript
build/
coverage/
node_modules/
jspm_packages/
web_modules/
*.tsbuildinfo
.npmrc
.yarnrc
.pnpmfile.cjs
.npmignore
.yarnclean
.yarn-integrity
.pnpm-lock.yaml
.npm-shrinkwrap.json
.yarn-metadata.json
