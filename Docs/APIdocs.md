# API 文档 - Alphane.ai

**最后更新时间:** 2025-05-17

**版本:** 2.0

**说明:** 本文档基于原始 API 文档进行整理，主要面向 APP 端开发需求。删除了部分对 APP 无用或网页端特有的接口，调整了结构，补充了说明，并根据项目计划设定了 APP 端的功能实现优先级。

**优先级定义:**
*   **P0:** MVP 版本最核心功能，必须实现。
*   **P1:** MVP 版本重要功能，或后续版本早期核心功能。
*   **P2:** 后续版本重要功能，完善用户体验。
*   **P3:** 后续版本可选功能，或优先级较低的功能。
*   **APP无用:** 主要用于网页端或内部管理，APP 端不需要调用。
*   **待定:** 功能不明确或需要进一步确认。

**通用返回格式:**
大部分 API 在成功时返回 HTTP `200 OK` 状态码，响应体结构如下 JSON：
```json
{
  "code": 200,     // HTTP 状态码，200 表示成功
  "message": "string", // 成功或错误信息
  "data": {},     // 具体的业务数据，结构因接口而异
  "detail": null  // 更详细的错误信息或上下文，通常在出错时提供
}
```
对于错误情况，API 会返回相应的 HTTP 错误状态码 (如 `400`, `401`, `403`, `404`, `409`, `422`, `500` 等)，响应体结构通常如下 (具体字段可能因错误类型而异):
```json
{
  "code": <HTTP_ERROR_CODE>, // 例如 400, 401, 404
  "message": "string",       // 错误描述
  "error": "string",         // 错误的简短标识 (可选)
  "detail": {}               // 详细错误信息，例如字段校验失败详情 (可选)
}
```
本文档后续将针对每个接口列出主要的成功和错误 HTTP 状态码。

---

## 一、认证 (Authentication - auth)

本模块包含用户注册、登录、密码管理、邮箱管理及第三方登录等功能。

### 1.1. 一次性验证码 (OTP)

#### 1.1.1. 发送验证码 (Send OTP)
-   **Method:** `POST`
-   **Endpoint:** `/auth/send-otp`
-   **Description:** 发送一次性验证码 (OTP) 到用户邮箱或手机。
-   **APP端优先级:** P0
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名  | 类型         | 是否必需 | 说明                                                                 |
    | :------ | :----------- | :------- | :------------------------------------------------------------------- |
    | `email` | `string`     | 否       | 邮箱地址                                                             |
    | `phone` | `string`     | 否       | 手机号码 (与 `email` 二选一)                                           |
    | `type`  | `string`     | 是       | 验证码类型：`signin` (注册/登录/重置邮箱)，`recovery` (重置密码) |
    | `captcha`| `string`    | 是       | 人机验证 Token (例如 Google Recaptcha v2)                            |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "OTP sent successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: OTP 发送成功。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数无效 (例如 `type` 不支持，或 `email` 和 `phone` 均未提供)。
      *   `422 Unprocessable Entity`: 参数格式错误 (例如邮箱/手机号格式不正确，`captcha` token 验证失败)。
      *   `429 Too Many Requests`: 短时间内请求次数过多。
      *   `503 Service Unavailable`: 短信或邮件服务暂时不可用。
### 1.2. 登录与注册 (Login & Registration)

#### 1.2.1. 登录/注册 (Login/Register)
-   **Method:** `POST`
-   **Endpoint:** `/auth/login`
-   **Description:** 使用密码或 OTP 进行登录。如果用户不存在且使用 OTP 方式，通常会同时完成注册。
-   **APP端优先级:** P0
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名       | 类型         | 是否必需 | 说明                                                                 |
    | :----------- | :----------- | :------- | :------------------------------------------------------------------- |
    | `provider`   | `string`     | 是       | 认证方式：`password` 或 `otp`                                         |
    | `email`      | `string`     | 否       | 邮箱地址                                                             |
    | `phone`      | `string`     | 否       | 手机号码 (与 `email` 二选一)                                           |
    | `password`   | `string`     | 否       | 密码 (当 `provider` 为 `password` 时必需)                             |
    | `otp`        | `string`     | 否       | 一次性验证码 (当 `provider` 为 `otp` 时必需)                           |
    | `invitor_id` | `string`     | 否       | 邀请者 ID (可选)                                                      |
    | `captcha`    | `string`     | 是       | Google Recaptcha v2 的 token                                         |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200, // 或 201 Created (如果是新用户注册成功)
            "message": "Login successful.", // 或 "Registration successful."
            "data": {
                "session": {
                    "access_token": "string",  // JWT 访问令牌
                    "refresh_token": "string" // JWT 刷新令牌
                },
                "user": { // 用户信息对象，其详细结构请参考 `/user/me` 接口的返回。后续将根据 `/user/me` 的更新而同步更新此处的示例。
                    "_id": "string",
                    "name": "string",
                    "uid": "string",
                    "email": "<EMAIL>",
                    "is_alphane_pass": false
                    // ... 其他用户核心字段，确保与 /user/me 一致
                },
                "is_new_user": false,
                "is_new_user_uid": "0"
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 登录成功。
        *   `201 Created`: 通过 OTP 方式注册新用户并登录成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数缺失或无效 (例如 `provider` 不支持, `captcha` 验证失败)。
        *   `401 Unauthorized`: 认证失败 (例如密码错误, OTP 错误或已过期)。
        *   `404 Not Found`: 当 `provider` 为 `password` 时，用户不存在。
        *   `409 Conflict`: 当 `provider` 为 `otp` 尝试注册时，邮箱或手机号已被占用。
        *   `422 Unprocessable Entity`: 提供的参数格式不正确 (例如 `email` 格式错误)。
    -   **注意:** 用户对象的详细结构请参考 `/user/me` 接口的返回。
### 1.3. 密码管理 (Password Management)

#### 1.3.1. 重置密码 (Change Password)
-   **Method:** `POST`
-   **Endpoint:** `/auth/change-password`
-   **Description:** 用户通过 OTP 验证后设置新的密码。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型         | 是否必需 | 说明                                                               |
    | :-------- | :----------- | :------- | :----------------------------------------------------------------- |
    | `email`   | `string`     | 否       | 邮箱地址                                                           |
    | `phone`   | `string`     | 否       | 手机号码 (与 `email` 二选一)                                         |
    | `otp`     | `string`     | 是       | 通过 `/auth/send-otp` (type=`recovery`) 获取的 OTP                  |
    | `password`| `string`     | 是       | 用户设置的新密码                                                     |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "Password changed successfully.",
        "data": null, // 或可返回新的 session 和 user 信息，同登录接口
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 密码修改成功。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数无效 (例如新密码不符合强度要求，OTP 未提供)。
      *   `401 Unauthorized`: OTP 验证失败 (OTP 错误或已过期)。
      *   `404 Not Found`: 邮箱或手机号对应的用户不存在。
      *   `422 Unprocessable Entity`: 邮箱或手机号格式不正确。
### 1.4. 邮箱管理 (Email Management)

#### 1.4.1. 步骤 1: 请求更换邮箱 (Initiate Email Change)
-   **Method:** `POST`
-   **Endpoint:** `/auth/change-email`
-   **Description:** 用户发起更换绑定邮箱的请求。需要先使用旧邮箱获取类型为 `signin` 的 OTP。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名     | 类型     | 是否必需 | 说明                               |
    | :--------- | :------- | :------- | :--------------------------------- |
    | `email`    | `string` | 是       | 当前绑定的旧邮箱                   |
    | `new_email`| `string` | 是       | 希望绑定的新邮箱                   |
    | `otp`      | `string` | 是       | 旧邮箱收到的类型为 `signin` 的 OTP |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "Email change initiated. Please verify your new email.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 更换邮箱请求已受理，验证邮件已发送至新邮箱。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数无效 (例如 `new_email` 格式错误)。
      *   `401 Unauthorized`: 旧邮箱的 OTP 验证失败。
      *   `403 Forbidden`: 当前用户无权修改此邮箱 (例如尝试修改非本人邮箱，或邮箱已锁定)。
      *   `404 Not Found`: 当前绑定的旧邮箱对应的用户不存在。
      *   `409 Conflict`: 新邮箱地址 (`new_email`) 已被其他用户占用。
      *   `422 Unprocessable Entity`: 邮箱格式不正确。
  -   **后续操作:** 调用成功后，系统会向 `new_email` 发送一个验证OTP，用户需使用该 OTP 调用步骤2的接口。
#### 1.4.2. 步骤 2: 验证新邮箱 (Verify New Email)
-   **Method:** `POST`
-   **Endpoint:** `/auth/verify-new-email`
-   **Description:** 验证发送到新邮箱的 OTP，完成邮箱更换。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名   | 类型     | 是否必需 | 说明             |
    | :------- | :------- | :------- | :--------------- |
    | `email`  | `string` | 是       | 新邮箱地址       |
    | `otp`    | `string` | 是       | 新邮箱收到的 OTP |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "New email verified successfully.",
        "data": null, // 建议返回更新后的 User 对象或 Session 信息
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 新邮箱验证成功，邮箱已成功更换。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数无效 (例如 `otp` 未提供)。
      *   `401 Unauthorized`: 新邮箱的 OTP 验证失败 (OTP 错误或已过期)。
      *   `404 Not Found`: 新邮箱地址未找到待验证记录，或用户不存在。
      *   `422 Unprocessable Entity`: 新邮箱格式不正确。
### 1.5. 第三方登录 (Social Login)

#### 1.5.1. Google 登录 (Google Login)
-   **Method:** `POST`
-   **Endpoint:** `/auth/google-login`
-   **Description:** 使用 Google OAuth 2.0 的 ID Token 进行登录或注册。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (如果APP支持Google登录)
-   **请求参数 (Body - JSON):**
    | 参数名      | 类型     | 是否必需 | 说明                   |
    | :---------- | :------- | :------- | :--------------------- |
    | `credential`| `string` | 是       | Google 返回的 ID token |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200, // 或 201 Created (如果是新用户通过 Google 注册)
            "message": "Google login successful.",
            "data": { // 同 /auth/login 接口返回结构
                "session": {
                    "access_token": "string",
                    "refresh_token": "string"
                },
                "user": { /* ... */ },
                "is_new_user": false
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: Google 登录成功。
        *   `201 Created`: 通过 Google 注册新用户并登录成功。
    -   **错误状态码:**
        *   `400 Bad Request`: `credential` (ID token) 未提供或无效。
        *   `401 Unauthorized`: Google ID token 验证失败 (例如 token 过期、签名无效、发行者不匹配)。
        *   `502 Bad Gateway`: 与 Google OAuth 服务通信失败。
        *   `503 Service Unavailable`: 后端依赖的认证服务暂时不可用。
---
## 二、用户 (User - user)

本模块包含用户个人资料管理、用户发现、奖励与交易、订阅及屏蔽等功能。

### 2.1. 用户资料 (User Profile)

#### 2.1.1. 获取当前用户信息 (Get Current User Info)
-   **Method:** `GET`
-   **Endpoint:** `/user/me`
-   **Description:** 获取当前登录用户的详细个人信息。
-   **APP端优先级:** P0
-   **APP端是否需要:** 是
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "User info retrieved successfully.",
            "data": {
                "avatar": "avatar_url",         // 头像
                "sign": "这个用户很懒,没有写签名",// 个性签名 (用于社区内的展示)
                "regions": "JP",                // 国家和地区 "CN","JP","US" 账号的地区影响每日奖励的刷新时间
                "gender": "other",              // 性别: "male", "female", "other"
                "_id": "string",                // 用户唯一ID (数据库ID)
                "name": "UserName",             // 用户昵称
                "uid": "string",                // 可以作为用户被搜索的条件，可读性高，默认数字递增，高级用户可以修改 (如 2000001)
                "email": "<EMAIL>",    // 用户邮箱 (仅 /user/me 返回)
                "bio": "我是猫娘",               // 用户默认全局Roleplay中使用的人设
                "birthday": "1990-01-01",       // 用户生日 (YYYY-MM-DD) (P2)
                "age_verification_status": "not_verified", // 年龄验证状态: "not_verified", "pending", "verified" (P2/P3)

                "follow_count": 0,              // 关注的角色数量
                "subscribe_count": 0,           // 该用户订阅/关注的其他用户数量
                "subscriber_count": 0,          // 该用户的订阅者/粉丝数量
                "character_count": 0,           // 创建的角色数量
                "story_count": 0,               // 创建的故事线数量

                // 货币与代币 (P0/P1)
                "alphane_dust_balance": 1000,   // Alphane 曦光微尘 (基础活跃代币) 余额
                "endora_crystal_balance": 100,  // Endora 心悦晶石 (进阶活跃与付费代币) 余额
                "serotile_fragment_balance": 50,// Serotile 忆境拼图碎片 余额
                "oxytol_dew_balance": 20,       // Oxytol 羁绊之露 余额

                // 激励与留存系统 (P0/P1)
                "streak_current_days": 15,      // 当前连续互动天数
                "streak_freeze_cards": 2,       // 拥有的 "Streak Freeze卡" 数量
                "daily_interaction_quota": {    // 免费用户每日互动配额信息
                    "fast_req_remaining": 5,
                    "fast_req_total": 10,
                    "slow_req_remaining": 100,
                    "slow_req_total": 50,
                },
                "displayed_honor_badges": [     // 用户选择展示的荣誉徽章列表
                    {"badge_id": "badge001", "name": "初见倾心", "icon_url": "url_to_badge_icon1"},
                    {"badge_id": "badge007", "name": "连续打卡达人", "icon_url": "url_to_badge_icon2"}
                ],
                "owned_honor_badges": [     // 用户拥有的头像框列表 (P1)
                    {"badge_id": "badge001", "name": "初见倾心", "icon_url": "url_to_badge_icon1"},
                    {"badge_id": "badge007", "name": "连续打卡达人", "icon_url": "url_to_badge_icon2"},
                    {"badge_id": "alphanepass", "name": "小月卡头像框", "icon_url": "url_to_badge_icon3"},
                    {"badge_id": "alphanediamond", "name": "大月卡头像框", "icon_url": "url_to_badge_icon4"},
                ],
                "displayed_avatar_frame": {"frame_id": "frame_default", "name": "默认头像框", "image_url": "url_to_default_frame.png"},
                "owned_avatar_frames": [     // 用户拥有的头像框列表 (P1)
                    {"frame_id": "frame_default", "name": "默认头像框", "image_url": "url_to_default_frame.png", "source": "系统默认", "description": "简洁的默认头像框"},
                    {"frame_id": "frame_vip", "name": "VIP专属框", "image_url": "url_to_vip_frame.png", "source": "VIP特权", "description": "彰显尊贵VIP身份"}
                ],

                // 创作者与社区 (P2)
                "is_verified_creator": false,   // 是否为认证创作者
                "creator_certification_level": "none", // 创作者认证等级: "none", "new_star", "elite", "master"
                "platform_contribution_points": 1200, // 平台贡献积分

                // 邀请系统 (P1)
                "invitor_id": "string",         // 邀请者的用户ID
                "invitation_code": "MYINVITECODE123", // 用户自己的邀请码

                // 会员信息 (P0)
                "alphane_pass_details": {
                    "is_active": false,
                    "expires_at": "2025-08-24T14:15:22.123Z",
                    "next_reward_at": "2025-07-25T00:00:00.000Z",
                },
                "alphane_diamond_details": {
                    "is_active": false,
                    "expires_at": "2025-08-24T14:15:22.123Z",
                    "next_reward_at": "2025-07-25T00:00:00.000Z",
                },
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 用户信息获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `404 Not Found`: 用户不存在 (理论上 /user/me 不会发生，因为是当前用户)。
#### 2.1.2. 修改当前用户信息 (Modify Current User Info)
-   **Method:** `POST`
-   **Endpoint:** `/user/modify`
-   **Description:** 修改当前登录用户的个人信息，如昵称、头像、性别、简介。不传入的字段不会被修改。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - `multipart/form-data`):**
| 参数名                         | 类型         | 是否必需 | 说明                                   |
| :----------------------------- | :----------- | :------- | :------------------------------------- |
| `name`                         | `string`     | 否       | 新的用户昵称                           |
| `sign`                         | `string`     | 否       | 新的用户签名                           |
| `bio`                          | `string`     | 否       | 新的个人全局人设                       |
| `gender`                       | `string`     | 否       | 新的性别: `male`, `female`, `other`   |
| `avatar`                       | `UploadFile` | 否       | 新的头像图片文件                       |
| `birthday`                     | `string`     | 否       | 生日 (YYYY-MM-DD)                     |
| `uid`                          | `string`     | 否       | UID普通用户不允许修改，默认就数字递增    |
| `displayed_honor_badges_config`| `List[string]`| 否      | 要展示的徽章ID列表 (e.g., ["badge001", "badge007"]) |
| `avatar_frame_id`              | `string`     | 否       | 要装备的头像框ID (用户必须拥有该头像框)  |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "User info modified successfully.",
        "data": { // 建议返回更新后的 User 对象，以便前端同步
          "name": "NewUserName",
          "sign": "Updated sign",
          "bio": "Updated bio",
          "gender": "other",
          "avatar": "avatar_url",
          "birthday": "YYYY-MM-DD",
          "uid": "updated_uid",
          "current_avatar_frame_id": {"frame_id": "frame_default", "name": "默认头像框", "image_url": "url_to_default_frame.png"},   // 用户选择展示的头像框
          "displayed_honor_badges": [                                                                                               // 用户选择展示的荣誉徽章列表
                    {"badge_id": "badge001", "name": "初见倾心", "icon_url": "url_to_badge_icon1"},
                    {"badge_id": "badge007", "name": "连续打卡达人", "icon_url": "url_to_badge_icon2"}
                ],
        },
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 用户信息修改成功。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数无效 (例如 `name` 包含非法字符, `gender` 值不在允许范围内, 头像文件过大或格式不支持)。
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `413 Payload Too Large`: 上传的头像文件过大。
      *   `422 Unprocessable Entity`: 参数校验失败 (例如 `name` 长度不符合要求)。
### 2.2. 用户发现 (User Discovery)

#### 2.2.1. 根据ID获取用户信息 (Get User by Database ID)
-   **Method:** `GET`
-   **Endpoint:** `/user/{user_id}`
-   **Description:** 根据用户的数据库ID (`_id`) 获取其公开的个人信息。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用于查看他人主页)
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明             |
    | :-------- | :------- | :------- | :------------- |
    | `user_id` | `string` | 是       | 用户的数据库ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "User info retrieved successfully.",
            "data": { // 公开的用户信息结构，不含email, token, invitor_id等敏感信息
                "sign": "",
                "gender": "other",
                "_id": "string",
                "avatar": "avatar_url",
                "name": "AnotherUser",
                "birthday": "YYYY-MM-DD",
                "uid": "string",
                "follow_count": 0,
                "subscribe_count": 0,
                "subscriber_count": 0,
                "character_count": 0,
                "story_count": 0,
                "is_subscribed": false, // 当前登录用户是否订阅了该用户
                "is_verified_creator": false,
                "current_avatar_frame_id": {"frame_id": "frame_default", "name": "默认头像框", "image_url": "url_to_default_frame.png"},   // 用户选择展示的头像框
                "displayed_honor_badges": [                                                                                               // 用户选择展示的荣誉徽章列表
                    {"badge_id": "badge001", "name": "初见倾心", "icon_url": "url_to_badge_icon1"},
                    {"badge_id": "badge007", "name": "连续打卡达人", "icon_url": "url_to_badge_icon2"}
                ],
                "alphane_pass_details": {
                    "is_active": false,
                },
                "alphane_diamond_details": {
                    "is_active": false,
                },
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 用户信息获取成功。
    -   **错误状态码:**
        *   `404 Not Found`: 指定 `user_id` 的用户不存在。
#### 2.2.2. 根据UID获取用户信息 (Get User by Unique ID)
-   **Method:** `GET`
-   **Endpoint:** `/user/uid/{uid}`
-   **Description:** 根据用户自定义的唯一标识符 (`uid`) 获取其公开的个人信息。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用于通过UID精确查找并查看他人主页)
-   **请求参数 (Path):**
    | 参数名 | 类型     | 是否必需 | 说明                 |
    | :----- | :------- | :------- | :------------------- |
    | `uid`  | `string` | 是       | 用户的自定义唯一标识符 |
-   **返回示例 (成功):** (同 `/user/{user_id}` 返回结构)
-   **成功状态码:**
    *   `200 OK`: 用户信息获取成功。
-   **错误状态码:**
    *   `404 Not Found`: 指定 `uid` 的用户不存在。

### 2.3. 用户配额 (User Quotas)

#### 2.3.1. 获取用户请求配额 (Get User Request Quotas)
-   **Method:** `GET`
-   **Endpoint:** `/user/quotas`
-   **Description:** 获取当前登录用户各类请求的剩余次数及总次数。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "User quotas retrieved successfully.",
        "data": {
            "fast_requests": {
                "remaining": 5,
                "total": 10,
                "resets_at": "2025-05-19T00:00:00Z" // 下次重置时间
            },
            "slow_requests": {
                "remaining": 48,
                "total": 50,
                "resets_at": "2025-05-19T00:00:00Z" // 下次重置时间
            }
            // 未来可能加入其他类型的配额，例如图片生成次数等
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 配额信息获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

#### 2.2.3. 搜索用户 (Search Users)
-   **Method:** `GET`
-   **Endpoint:** `/user/search`
-   **Description:** 根据关键词搜索用户 (可能匹配用户昵称 `name` 和自定义ID `uid`)。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型     | 是否必需 | 说明                 |
    | :------ | :------- | :------- | :------------------- |
    | `query` | `string` | 是       | 搜索关键词           |
    | `num`   | `int`    | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int`    | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Users searched successfully.",
            "data": [
                { // 公开的用户信息对象列表,(同 `/user/{user_id}` 返回结构)

                }
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 用户搜索成功 (即使结果为空列表)。
    -   **错误状态码:**
        *   `400 Bad Request`: 搜索参数 (`query`) 无效或缺失，或分页参数 (`num`, `offset`) 无效。
#### 2.2.4. 获取所有用户ID (Get All User IDs)
-   **Method:** `GET`
-   **Endpoint:** `/user/all`
-   **Description:** 获取系统中所有用户的ID列表。
-   **APP端优先级:** P3 / APP无用
-   **APP端是否需要:** 否 (通常为后台管理功能)
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "All user IDs retrieved.",
            "data": [
                "string", // 用户ID (_id) 列表
                "string"
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 用户ID列表获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 未登录或 Token 无效。
        *   `403 Forbidden`: 当前用户无权限执行此操作 (通常为管理员接口)。
### 2.3. 奖励与交易 (Rewards & Transactions)

#### 2.3.1. 领取每日登录奖励 (Claim Daily Login Rewards)
-   **Method:** `POST`
-   **Endpoint:** `/user/daily-login-rewards`
-   **Description:** 用户每日登录领取的组合奖励 (可能包含多种代币)。
-   **APP端优先级:** P0
-   **APP端是否需要:** 是
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Daily login rewards claimed successfully.",
        "data": {
           "rewards_claimed": [
               {"type": "alphane_dust", "amount": 50, "name": "曦光微尘"},
               {"type": "endora_crystal", "amount": 5, "name": "心悦晶石"},
               {"type": "serotile_fragment", "amount": 1, "name": "忆境拼图碎片"}
           ],
           "next_claimable_at": "2025-07-26T00:00:00.000Z" // 下次可领取时间
        },
        "detail": null
    }
    ```
    -   **成功状态码:**
        *   `200 OK`: 奖励领取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    
    #### 2.3.3. 领取连续互动里程碑奖励 (Claim Streak Milestone Reward)
    -   **Method:** `POST`
    -   **Endpoint:** `/user/streak/claim-milestone`
    -   **Description:** 用户领取连续互动 (Streak) 达到特定里程碑的奖励。
    -   **APP端优先级:** P0
    -   **APP端是否需要:** 是
    -   **请求参数 (Body - JSON):**
        | 参数名           | 类型  | 是否必需 | 说明                                      |
        | :--------------- | :---- | :------- | :---------------------------------------- |
        | `milestone_days` | `int` | 是       | 里程碑天数 (e.g., 3, 7, 15, 30, 60, 100等) |
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Streak milestone reward claimed successfully.",
            "data": {
                "rewards_claimed": [
                    {"type": "alphane_dust", "amount": 100, "name": "曦光微尘"},
                    {"type": "endora_crystal", "amount": 10, "name": "心悦晶石"},
                    {"type": "item", "item_id": "streak_badge_30d", "name": "30天连续互动徽章"}
                ],
                "next_milestone_days": 60 // (可选) 下一个里程碑天数
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 里程碑奖励领取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: `milestone_days` 无效或未达到。
        *   `401 Unauthorized`: 用户未登录。
        *   `409 Conflict`: 该里程碑奖励已领取。
    
    #### 2.3.4. 使用连续互动冻结卡 (Use Streak Freeze Card)
    -   **Method:** `POST`
    -   **Endpoint:** `/user/streak/use-freeze`
    -   **Description:** 用户使用一张 "Streak Freeze卡" 来冻结一次中断的连续互动记录。
    -   **APP端优先级:** P0
    -   **APP端是否需要:** 是
    -   **请求参数:** 无
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Streak freeze card used successfully.",
            "data": {
                "streak_current_days": 15, // 使用卡后当前的连续天数
                "streak_freeze_cards_remaining": 1 // 剩余冻结卡数量
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 冻结卡使用成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 没有可用的冻结卡，或当前Streak未中断无需使用。
        *   `401 Unauthorized`: 用户未登录。
    
    #### 2.3.5. 使用连续互动修复机会 (Use Streak Repair)
    -   **Method:** `POST`
    -   **Endpoint:** `/user/streak/repair`
    -   **Description:** 用户使用免费的Streak修复机会 (通常为大月卡特权)。
    -   **APP端优先级:** P1
    -   **APP端是否需要:** 是
    -   **请求参数:** 无
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Streak repaired successfully.",
            "data": {
                "streak_current_days": 16, // 修复后当前的连续天数
                "streak_repair_chances_remaining": 0 // 剩余修复机会
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: Streak修复成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 没有可用的修复机会，或当前Streak未中断无需修复。
        *   `401 Unauthorized`: 用户未登录。
        *   `403 Forbidden`: 用户无此权限 (例如非大月卡会员)。
    
    #### 2.3.6. 获取用户成就列表 (Get User Achievements)
    -   **Method:** `GET`
    -   **Endpoint:** `/user/achievements`
    -   **Description:** 获取当前用户的成就列表、完成状态及已获得的徽章。
    -   **APP端优先级:** P1
    -   **APP端是否需要:** 是
    -   **请求参数 (Query):**
        | 参数名     | 类型  | 是否必需 | 说明                |
        | :--------- | :---- | :------- | :------------------ |
        | `page`     | `int` | 否       | 页码 (默认: 1)       |
        | `page_size`| `int` | 否       | 每页数量 (默认: 20) |
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Achievements retrieved successfully.",
            "data": [
                {
                    "achievement_id": "ach001",
                    "name": "首次问候",
                    "description": "与AI完成首次对话。",
                    "status": "unlocked", // "locked", "unlocked", "reward_claimed"
                    "unlocked_at": "2025-05-16T10:00:00Z",
                    "badge_details": {
                        "badge_id": "badge_first_greet",
                        "name": "问候者",
                        "icon_url": "url_to_badge_icon",
                        "rarity": "bronze"
                    },
                    "rewards": [ // 完成此成就可领取的奖励
                        {"type": "alphane_dust", "amount": 10, "name": "曦光微尘"}
                    ],
                    "is_reward_claimed": true
                }
            ],
            "meta": {"page": 1, "page_size": 20, "total_items": 50, "total_pages": 3},
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 成就列表获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录。
    
    #### 2.3.7. 获取用户荣耀战令状态 (Get User Battle Pass Status)
    -   **Method:** `GET`
    -   **Endpoint:** `/user/battle-pass`
    -   **Description:** 获取当前赛季/月度用户的荣耀战令状态，包括等级、经验、免费/付费轨道的奖励状态。
    -   **APP端优先级:** P1
    -   **APP端是否需要:** 是
    -   **请求参数:** 无
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Battle pass status retrieved.",
            "data": {
                "season_id": "season_2025_05",
                "season_name": "五月迷情赛季",
                "season_ends_at": "2025-05-31T23:59:59Z",
                "current_level": 15,
                "current_xp": 500,
                "xp_to_next_level": 1000,
                "is_paid_pass_active": true, // 用户是否激活了付费进阶通行证
                "free_rewards_by_level": [
                    {"level": 1, "item_name": "少量曦光微尘", "item_icon_url": "url", "is_claimed": true},
                    {"level": 2, "item_name": "战令币x5", "item_icon_url": "url", "is_claimed": false}
                ],
                "paid_rewards_by_level": [
                    {"level": 1, "item_name": "专属头像框-迷情", "item_icon_url": "url", "is_claimed": true},
                    {"level": 2, "item_name": "大量心悦晶石", "item_icon_url": "url", "is_claimed": false}
                ],
                "battle_pass_coin_balance": 50 // 用户当前拥有的战令币数量
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 战令状态获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录。
        *   `404 Not Found`: 当前无有效赛季或战令信息。
    
    #### 2.3.8. 领取荣耀战令奖励 (Claim Battle Pass Reward)
    -   **Method:** `POST`
    -   **Endpoint:** `/user/battle-pass/claim-reward`
    -   **Description:** 用户领取荣耀战令中已达到等级的特定奖励。
    -   **APP端优先级:** P1
    -   **APP端是否需要:** 是
    -   **请求参数 (Body - JSON):**
        | 参数名                 | 类型    | 是否必需 | 说明                                       |
        | :--------------------- | :------ | :------- | :----------------------------------------- |
        | `level`                | `int`   | 是       | 要领取的奖励所属等级                       |
        | `track_type`           | `string`| 是       | 奖励轨道: "free" 或 "paid"                 |
        | `reward_index_in_level`| `int`   | 否       | 如果同等级同轨道有多个奖励，指定是第几个 (从0开始) |
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Battle pass reward claimed successfully.",
            "data": {
                "claimed_reward_name": "专属头像框-迷情",
                "updated_battle_pass_coin_balance": 50 // (如果奖励包含战令币)
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 战令奖励领取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数无效，或该等级奖励不可领取/已领取。
        *   `401 Unauthorized`: 用户未登录。
        *   `403 Forbidden`: 用户未激活付费轨道却尝试领取付费奖励。
    
    #### 2.3.9. 购买荣耀战令等级 (Buy Battle Pass Levels)
    -   **Method:** `POST`
    -   **Endpoint:** `/user/battle-pass/buy-levels`
    -   **Description:** 用户使用核心付费代币 (如Endora) 购买荣耀战令等级。
    -   **APP端优先级:** P1
    -   **APP端是否需要:** 是
    -   **请求参数 (Body - JSON):**
        | 参数名          | 类型  | 是否必需 | 说明             |
        | :-------------- | :---- | :------- | :--------------- |
        | `levels_to_buy` | `int` | 是       | 希望购买的等级数量 |
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Battle pass levels purchased successfully.",
            "data": {
                "new_level": 20,
                "new_xp": 0,
                "cost_amount": 100,
                "remaining_endora_balance": 50
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 战令等级购买成功。
    -   **错误状态码:**
        *   `400 Bad Request`: `levels_to_buy` 无效或超出可购买上限。
        *   `401 Unauthorized`: 用户未登录。
        *   `402 Payment Required`: 用户核心付费代币余额不足。
    
    #### 2.3.10. 获取用户任务列表 (Get User Tasks)
    -   **Method:** `GET`
    -   **Endpoint:** `/user/tasks`
    -   **Description:** 获取用户当前可接/进行中的日常、周常、月常任务列表。
    -   **APP端优先级:** P0 (日常任务), P1 (周常/月常)
    -   **APP端是否需要:** 是
    -   **请求参数 (Query):**
        | 参数名     | 类型     | 是否必需 | 说明                                       |
        | :--------- | :------- | :------- | :----------------------------------------- |
        | `type`     | `string` | 否       | 任务类型: "daily", "weekly", "monthly", "all" (默认 "all") |
        | `page`     | `int`    | 否       | 页码 (默认: 1)                              |
        | `page_size`| `int`    | 否       | 每页数量 (默认: 20)                         |
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Tasks retrieved successfully.",
            "data": [
                {
                    "task_id": "daily_chat_3_chars",
                    "title": "话题探索者",
                    "description": "与3个不同AI角色各探讨1个不同主题。",
                    "type": "daily", // "daily", "weekly", "monthly", "event"
                    "status": "active", // "active", "completed", "reward_claimed"
                    "progress_current": 1,
                    "progress_target": 3,
                    "rewards": [
                        {"type": "alphane_dust", "amount": 20, "name": "曦光微尘"},
                        {"type": "battle_pass_xp", "amount": 10, "name": "战令经验"}
                    ],
                    "expires_at": "2025-05-17T23:59:59Z" // (可选) 任务过期时间
                }
            ],
            "meta": {"page": 1, "page_size": 20, "total_items": 15, "total_pages": 1},
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 任务列表获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录。
    
    #### 2.3.11. 领取任务奖励 (Claim Task Reward)
    -   **Method:** `POST`
    -   **Endpoint:** `/user/tasks/{task_id}/claim-reward`
    -   **Description:** 用户领取已完成任务的奖励 (部分任务可能自动发放奖励，此API用于手动领取)。
    -   **APP端优先级:** P0/P1
    -   **APP端是否需要:** 是
    -   **请求参数 (Path):**
        | 参数名   | 类型     | 是否必需 | 说明     |
        | :------- | :------- | :------- | :------- |
        | `task_id`| `string` | 是       | 任务ID   |
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Task reward claimed successfully.",
            "data": {
                "rewards_granted": [
                    {"type": "alphane_dust", "amount": 20, "name": "曦光微尘"},
                    {"type": "battle_pass_xp", "amount": 10, "name": "战令经验"}
                ],
                "alphane_dust_balance": 1020
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 任务奖励领取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 任务未完成或奖励已领取。
        *   `401 Unauthorized`: 用户未登录。
        *   `404 Not Found`: `task_id` 不存在。
    
    #### 2.3.12. 领取离线收益 (Claim Offline Earnings)
    -   **Method:** `POST` (改为POST，因为是执行一个操作)
    -   **Endpoint:** `/user/offline-earnings/claim`
    -   **Description:** 用户领取“放置Play收菜”的离线收益。
    -   **APP端优先级:** P1
    -   **APP端是否需要:** 是
    -   **请求参数:** 无
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Offline earnings claimed successfully.",
            "data": {
                "rewards_claimed": [
                    {"type": "alphane_dust", "amount": 100, "name": "曦光微尘"},
                    {"type": "endora_crystal", "amount": 1, "name": "心悦晶石"}
                ],
                "last_claimed_at": "2025-05-17T08:00:00Z", // 上次领取时间
                "next_claimable_at": "2025-05-18T08:00:00Z" // (可选) 下次可积累满额的时间
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 离线收益领取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录。
        *   `409 Conflict`: 当前无离线收益可领取或领取过于频繁。
        *   `403 Forbidden`: 不符合领取条件。
        *   `409 Conflict`: 今日奖励已领取。
    #### 2.3.13. 获取用户余额 (Get User Balance)
    -   **Method:** `GET`
    -   **Endpoint:** `/user/my_balance`
    -   **Description:** 获取当前登录用户的各种货币和代币余额。
    -   **APP端优先级:** P0
    -   **APP端是否需要:** 是
    -   **请求参数:** 无
    -   **返回示例 (成功):**
        ```json
        {
            "code": 200,
            "message": "Balance retrieved successfully.",
            "data": {
            "alphane_dust_balance": 1000,       // Alphane 曦光微尘 (基础活跃代币)
            "endora_crystal_balance": 100,    // Endora 心悦晶石 (进阶活跃与付费代币)
            "serotile_fragment_balance": 50,  // Serotile 忆境拼图碎片 (收集与探索代币)
            "oxytol_dew_balance": 20,         // Oxytol 羁绊之露 (角色羁绊成长代币)
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 余额获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

    #### 2.3.14. 获取用户交易记录 (Get User Transaction History)
    -   **Method:** `GET`
    -   **Endpoint:** `/user/transaction`
    -   **Description:** 获取当前登录用户的虚拟货币 (Alphane) 交易记录，包括获取和消耗。
    -   **APP端优先级:** P2
    -   **APP端是否需要:** 是 (用户查看消费明细)
    -   **请求参数 (Query):**
        | 参数名     | 类型  | 是否必需 | 说明                |
        | :--------- | :---- | :------- | :------------------ |
        | `page`     | `int` | 否       | 页码 (默认: 1)       |
        | `page_size`| `int` | 否       | 每页数量 (默认: 20) |
    -   **返回示例 (成功):**
        ```json
        {
        {
            "code": 200,
            "message": "Transaction history retrieved.",
            "data": [
                {
                    "amount": -10,                  // 交易金额 (负数为支出, 正数为收入)
                    "description": "Generate Image", // 交易描述
                    "timestamp": "2025-08-24T14:15:22.123Z" // 交易时间
                },
                {
                    "amount": 50,
                    "description": "Daily Reward",
                    "timestamp": "2025-08-23T10:00:00.000Z"
                }
            ],
            "meta": { // 可选，用于分页信息
                "page": 1,
                "page_size": 20,
                "total_items": 100,
                "total_pages": 5
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 交易记录获取成功 (即使结果为空列表)。
    -   **错误状态码:**
        *   `400 Bad Request`: 分页参数 (`page`, `page_size`) 无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

### 2.4. 订阅与关注 (Subscriptions & Following)
**注意:** 此处的“订阅(Subscribe)”指的是用户之间的关注关系，区别于 APP 的付费订阅会员。

#### 2.4.1. 切换用户关注状态 (Toggle User Subscription/Follow)
-   **Method:** `POST`
-   **Endpoint:** `/user/toggle_subscribe`
-   **Description:** 关注或取消关注指定的用户。
-   **APP端优先级:** P2 (社区功能)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名   | 类型     | 是否必需 | 说明               |
    | :------- | :------- | :------- | :----------------- |
    | `user_id`| `string` | 是       | 目标用户的ID (`_id`) |
-   **返回示例 (成功):**
    ```json
    {
    {
        "code": 200,
        "message": "Subscription status updated.",
        "data": {
            "is_subscribed": true // true 表示操作后为已关注, false 表示操作后为未关注
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 关注/取关操作成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `user_id` 未提供或无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 目标用户 (`user_id`) 不存在。
#### 2.4.2. 获取用户关注的列表 (Get User's Following List)
-   **Method:** `GET`
-   **Endpoint:** `/user/subscribed/{user_id}`
-   **Description:** 获取指定用户所关注的其他用户列表。
-   **APP端优先级:** P2 (社区功能)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明           |
    | :-------- | :------- | :------- | :------------- |
    | `user_id` | `string` | 是       | 目标用户的ID   |
-   **请求参数 (Query):**
    | 参数名  | 类型     | 是否必需 | 说明                      |
    | :------ | :------- | :------- | :------------------------ |
    | `query` | `string` | 否       | 按昵称/UID 过滤关注列表   |
    | `num`   | `int`    | 否       | 返回数量 (默认: 10)        |
    | `offset`| `int`    | 否       | 偏移量 (默认: 0)          |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Subscribed list retrieved.",
            "data": [
                { // 公开的用户信息对象列表
                    "_id": "string",
                    "name": "FollowedUser",
                    "uid": "string",
                    "image_url": "url",
                    "is_subscribed": true // 表示当前登录用户是否也关注了列表中的这个用户
                }
            ],
            "meta": { // 可选，用于分页信息
                "query": "search_term",
                "num": 10,
                "offset": 0,
                "total_items": 50,
                "total_pages": 5
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 关注列表获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 分页参数 (`num`, `offset`) 或查询参数 (`query`) 无效。
        *   `404 Not Found`: 指定 `user_id` 的用户不存在。
#### 2.4.3. 获取用户的粉丝列表 (Get User's Subscribers List)
-   **Method:** `GET`
-   **Endpoint:** `/user/subscribers/{user_id}`
-   **Description:** 获取关注了指定用户的其他用户列表 (即粉丝列表)。
-   **APP端优先级:** P2 (社区功能)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明           |
    | :-------- | :------- | :------- | :------------- |
    | `user_id` | `string` | 是       | 目标用户的ID   |
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Subscribers list retrieved.",
            "data": [
                { // 公开的用户信息对象列表
                    "_id": "string",
                    "name": "SubscriberUser",
                    "uid": "string",
                    "image_url": "url",
                    "is_subscribed": false // 表示当前登录用户是否关注了列表中的这个粉丝用户
                }
            ],
            "meta": { // 可选，用于分页信息
                "num": 10,
                "offset": 0,
                "total_items": 25,
                "total_pages": 3
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 粉丝列表获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
        *   `404 Not Found`: 指定 `user_id` 的用户不存在。
        
### 2.5. 用户屏蔽 (User Blocking)

#### 2.5.1. 获取已屏蔽用户列表 (Get Blocked Users List)
-   **Method:** `GET`
-   **Endpoint:** `/user/block`
-   **Description:** 获取当前登录用户已屏蔽的用户列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Blocked users retrieved.",
            "data": [
                { // 被屏蔽用户的信息 (通常只包含必要识别信息)
                    "_id": "string",
                    "name": "BlockedUser",
                    "uid": "string",
                    "image_url": "url"
                }
            ],
            "meta": { // 可选，用于分页信息
                "num": 10,
                "offset": 0,
                "total_items": 5,
                "total_pages": 1
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 已屏蔽用户列表获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
#### 2.5.2. 屏蔽用户 (Block User)
-   **Method:** `POST`
-   **Endpoint:** `/user/block`
-   **Description:** 将指定用户加入当前登录用户的屏蔽列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名   | 类型     | 是否必需 | 说明                 |
    | :------- | :------- | :------- | :------------------- |
    | `user_id`| `string` | 是       | 要屏蔽的目标用户ID   |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 204 No Content 如果不返回任何内容体
        "message": "User blocked successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 用户屏蔽成功。
      *   `204 No Content`: 用户屏蔽成功，且响应体为空。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数 `user_id` 未提供或无效。
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `404 Not Found`: 目标用户 (`user_id`) 不存在。
      *   `409 Conflict`: 该用户已被屏蔽。
#### 2.5.3. 取消屏蔽用户 (Unblock User)
-   **Method:** `DELETE`
-   **Endpoint:** `/user/block`
-   **Description:** 将指定用户从当前登录用户的屏蔽列表中移除。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名   | 类型     | 是否必需 | 说明                   |
    | :------- | :------- | :------- | :--------------------- |
    | `user_id`| `string` | 是       | 要取消屏蔽的目标用户ID |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 204 No Content
        "message": "User unblocked successfully.",
        "data": null,
        "detail": null
-   **成功状态码:**
      *   `200 OK`: 用户取消屏蔽成功。
      *   `204 No Content`: 用户取消屏蔽成功，且响应体为空。
-   **错误状态码:**
      *   `400 Bad Request`: 请求参数 `user_id` 未提供或无效。
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `404 Not Found`: 目标用户 (`user_id`) 不存在，或未在当前用户的屏蔽列表中。

### 2.6. 创作者与社区荣誉 (Creator Economy & Community Honors) - (P2/P3)

#### 2.6.1. 获取创作者/角色排行榜 (Get Creator/Character Leaderboard)
-   **Method:** `GET`
-   **Endpoint:** `/creator/leaderboard`
-   **Description:** 获取创作者或角色卡的排行榜。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名     | 类型     | 是否必需 | 说明                                                                 |
    | :--------- | :------- | :------- | :------------------------------------------------------------------- |
    | `type`     | `string` | 是       | 排行榜类型: "creator_activity", "character_hotness", "designer_points" |
    | `period`   | `string` | 否       | 时间周期: "weekly", "monthly", "all_time" (默认 "weekly")            |
    | `page`     | `int`    | 否       | 页码 (默认: 1)                                                        |
    | `page_size`| `int`    | 否       | 每页数量 (默认: 10)                                                   |
-   **返回示例 (成功 - 角色热度榜):**
    ```json
    {
        "code": 200,
        "message": "Leaderboard retrieved successfully.",
        "data": [
            {
                "rank": 1,
                "character_id": "char123",
                "character_name": "热门角色A",
                "creator_name": "创作者X",
                "score": 15000, // 热度值或积分
                "character_avatar_url": "url"
            }
        ],
        "meta": {"page": 1, "page_size": 10, "total_items": 100, "total_pages": 10},
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 排行榜获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: `type` 或 `period` 参数无效。

#### 2.6.2. 获取创作者仪表盘数据 (Get Creator Dashboard)
-   **Method:** `GET`
-   **Endpoint:** `/user/creator/dashboard`
-   **Description:** 创作者获取自己的创作数据统计、预估收入、粉丝增长等信息 (仅限大月卡创作者)。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Creator dashboard data retrieved.",
        "data": {
            "total_characters_created": 5,
            "total_character_interactions": 12050,
            "total_character_followers": 800,
            "estimated_monthly_earnings_currency": "USD",
            "estimated_monthly_earnings_amount": 550.75,
            "platform_contribution_points": 2500,
            "recent_activities": [
                {"timestamp": "ts", "message": "您的角色 'AI女友小爱' 获得了100次新互动。"}
            ]
            // ...更多统计数据
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 创作者仪表盘数据获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录。
    *   `403 Forbidden`: 用户非大月卡创作者或无权限访问。

#### 2.6.3. 平台贡献积分兑换奖励 (Redeem Contribution Points)
-   **Method:** `POST`
-   **Endpoint:** `/user/contribution-points/redeem`
-   **Description:** 用户使用平台贡献积分兑换稀有设计资源、平台特权等。
-   **APP端优先级:** P3
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名             | 类型     | 是否必需 | 说明             |
    | :----------------- | :------- | :------- | :--------------- |
    | `item_to_redeem_id`| `string` | 是       | 要兑换的奖励物品ID |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Reward redeemed successfully with contribution points.",
        "data": {
            "redeemed_item_name": "限定版角色背景模板",
            "remaining_contribution_points": 2000
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 贡献积分兑换成功。
-   **错误状态码:**
    *   `400 Bad Request`: `item_to_redeem_id` 无效或积分不足。
    *   `401 Unauthorized`: 用户未登录。
    *   `404 Not Found`: 奖励物品不存在。

#### 2.6.4. 获取用户邀请详情 (Get User Invitation Details)
-   **Method:** `GET`
-   **Endpoint:** `/user/invitation-details`
-   **Description:** 用户查看自己的邀请码、邀请成功记录及已获得的邀请奖励。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名     | 类型  | 是否必需 | 说明                |
    | :--------- | :---- | :------- | :------------------ |
    | `page`     | `int` | 否       | 邀请记录页码 (默认: 1) |
    | `page_size`| `int` | 否       | 每页数量 (默认: 10)  |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Invitation details retrieved.",
        "data": {
            "invitation_code": "MYINVITECODE123",
            "total_invited_users": 5,
            "total_rewards_earned": [
                {"type": "alphane_dust", "amount": 250, "name": "曦光微尘"},
                {"type": "alphane_pass_trial_days", "amount": 15, "name": "小月卡体验天数"}
            ],
            "invitation_records": [
                {
                    "invited_user_name": "NewUser1",
                    "invited_at": "2025-05-10T10:00:00Z",
                    "reward_received": {"type": "alphane_dust", "amount": 50}
                }
            ],
            "pagination": {"page": 1, "page_size": 10, "total_items": 5, "total_pages": 1}
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 邀请详情获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录。

### 2.7. 头像框 (Avatar Frames) - (P1)

本子模块包含与用户头像框相关的功能，如获取可用的头像框列表、用户装备头像框等。

#### 2.7.1. 获取所有可用头像框列表 (List All Available Avatar Frames)
-   **Method:** `GET`
-   **Endpoint:** `/user/avatar-frames/available`
-   **Description:** 获取系统中所有可供用户获取或购买的头像框列表。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用于商店展示、奖励预览、个人装扮选择等)
-   **请求参数 (Query):**
    | 参数名     | 类型     | 是否必需 | 说明                                                                 |
    | :--------- | :------- | :------- | :------------------------------------------------------------------- |
    | `category` | `string` | 否       | 分类筛选 (例如: "event", "vip", "svip", "achievement", "gacha", "system_default") |
    | `sort_by`  | `string` | 否       | 排序依据 (例如: "newest", "price_asc", "price_desc", "rarity", "name") |
    | `num`      | `int`    | 否       | 返回数量 (默认: 20)                                                   |
    | `offset`   | `int`    | 否       | 偏移量 (默认: 0)                                                      |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Available avatar frames retrieved successfully.",
        "data": [
            {
                "frame_id": "frame_event_dragon",
                "name": "龙年限定框",
                "description": "参与龙年春节活动即可获得这款酷炫的龙头头像框！",
                "image_url": "https://cdn.example.com/avatar_frames/dragon_event.png",
                "category": "event",
                "rarity": "SSR", // "N", "R", "SR", "SSR", "UR"
                "source_description": "龙年春节活动限定", // 获取途径描述
                "unlock_condition": "完成龙年春节系列任务", // 解锁条件 (如果适用)
                "price": null, // {"currency_type": "endora_crystal", "amount": 100}
                "is_time_limited": true, // 是否限时获取
                "available_until": "2025-02-28T23:59:59Z", // 限时获取截止日期 (如果适用)
                "tags": ["限定", "节日", "动态效果"] // 其他标签
            },
            {
                "frame_id": "frame_vip_gold",
                "name": "黄金VIP框",
                "description": "尊贵的黄金VIP会员专属头像框。",
                "image_url": "https://cdn.example.com/avatar_frames/vip_gold.png",
                "category": "vip",
                "rarity": "SR",
                "source_description": "黄金VIP会员特权",
                "unlock_condition": "成为黄金VIP会员",
                "price": null, // 示例: {"currency_type": "alphane_dust", "amount": 5000}
                "is_time_limited": false,
                "available_until": null,
                "tags": ["VIP", "尊贵"]
            }
        ],
        "meta": {
            "num": 20,
            "offset": 0,
            "total_items": 50,
            "total_pages": 3
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 可用头像框列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `category` 或 `sort_by` 无效，或分页参数无效。
      }
      ```

#### 2.7.2. 购买头像框 (Buy Avatar Frame)
-   **Method:** `POST`
-   **Endpoint:** `/user/avatar-frames/buy`
-   **Description:** 用户使用 Endora 心悦晶石或其他指定货币购买一个可用的头像框。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名           | 类型     | 是否必需 | 说明                                                                 |
    | :--------------- | :------- | :------- | :------------------------------------------------------------------- |
    | `frame_id`       | `string` | 是       | 要购买的头像框ID。                                                     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Avatar frame purchased successfully.",
        "data": {
            "owned_avatar_frames": [ // 用户当前拥有的所有头像框列表 (更新后)
                {"frame_id": "frame_default", "name": "默认头像框", "image_url": "url_to_default_frame.png", "source": "系统默认", "description": "简洁的默认头像框"},
                {"frame_id": "frame_vip", "name": "VIP专属框", "image_url": "url_to_vip_frame.png", "source": "VIP特权", "description": "彰显尊贵VIP身份"},
                {"frame_id": "frame_event_dragon", "name": "龙年限定框", "image_url": "url_to_dragon_event.png", "source": "购买", "description": "参与龙年春节活动即可获得这款酷炫的龙头头像框！"}
            ],
            "alphane_dust_balance": 10000, //剩余的alphane
            "endora_crystal_balance": 100, //剩余的endora
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 头像框购买成功，用户已拥有该头像框。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效 (例如 `frame_id` 未提供，或 `expected_price` 与服务器价格不符)。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `402 Payment Required`: 用户 Endora 心悦晶石余额不足，或 `payment_token` 支付失败。
    *   `404 Not Found`: 指定的 `frame_id` 头像框不存在或不可购买。
    *   `409 Conflict`: 用户已拥有该头像框。
    *   `422 Unprocessable Entity`: 头像框已下架或不满足其他购买条件。
---
## 三、角色 (Character - character)

本模块包含与AI角色相关的所有操作，如创建、获取、修改、删除、搜索、推荐、评论以及用户与角色的互动（如关注）。

### 3.1. 角色管理 (Character Management)

#### 3.1.1. 创建角色 (Create Character)
-   **Method:** `POST`
-   **Endpoint:** `/character/create`
-   **Description:** 用户创建一个新的AI角色。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - `multipart/form-data`):**
参数名                        | 类型                  | 是否必需 | 说明                                                                                                                               |
:---------------------------- | :-------------------- | :------- | :--------------------------------------------------------------------------------------------------------------------------------- |
`name`                        | `string`              | 是       | 角色的名字 (例如："温柔学姐")                                                                  |
`description`                 | `string`              | 是       | 角色的详细描述 (例如："一位总是耐心倾听你烦恼的邻家大姐姐。")                                     |
`setting`                     | `string`              | 是       | 角色的世界观、背景等设定 (例如："她住在充满魔法的奇幻小镇，经营着一家神秘的草药店。")               |
`background_story`            | `string`              | 否       | 角色的背景故事 (更详细的叙事性背景，可选)。                                                     |
`greeting_message`            | `string`              | 是       | 角色问候语 (例如，在角色列表中或非对话场景下展示的招呼语："你好呀，今天过得怎么样？")               |
`opening_message_templates`   | `string`              | 是       | 开场白模板列表 (JSON字符串, 格式: `[{"template_id": "unique_id_1", "content": "你好，我是小爱，很高兴认识你！", "priority": 1, "conditions": null}]`)。至少包含一个。 |
`sample_conversations`        | `string`              | 否       | 对话示例 (JSON字符串, 格式: `[{"role": "user", "text": "你好"}, {"role": "ai", "text": "你好呀！"}]`)。用于展示角色对话风格。                 |
`personality_tags`            | `string`              | 是       | 性格标签 (JSON字符串, 格式: `[{"tag_name": "温柔", "weight": 0.8}, {"tag_name": "傲娇", "weight": 0.5}]`)。至少包含一个。                  |
`content_tags`                | `string`              | 是       | 内容标签 (逗号分隔, e.g., "日常,校园,奇幻")。用于分类和搜索。                                    |
`interaction_style_tags`      | `string`              | 否       | 互动风格标签 (逗号分隔, e.g., "empathetic,formal,playful")。                                   |
`voice_ids`                   | `string`              | 否       | 角色使用的语音ID (逗号分隔, e.g., "voice_id1,voice_id2")。                                     |
`gender`                      | `string`              | 是       | 性别: `male`, `female`, `other`。                                                            |
`visibility`                  | `string`              | 否       | 可见性: `public` (公开), `unlisted` (不公开列出但链接可访问), `private` (私密)。                |
`initial_memories_text`       | `string`              | 否       | 初始记忆文本 (用于角色核心设定或短期记忆，例如："我最喜欢的颜色是蓝色。我害怕打雷。")。             |
`knowledge_files`             | `List[UploadFile]`    | 否       | 知识库文件 (txt, pdf, md, docx)。用于增强角色特定领域知识。                                     |
`image`                       | `UploadFile`          | 是       | 角色立绘图片 (全身或半身像)。                                                                  |
`avatar`                      | `UploadFile`          | 是       | 角色头像图片。                                                                                |
`custom_prompt_prefix`        | `string`              | 否       | 自定义Prompt前缀 (高级功能，用于在每次请求AI前附加特定指令)。                                     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 201, // HTTP 201 Created
        "message": "Character created successfully.",
        "data": {
            "character_id": "string", // 新创建的角色ID
            "default_story_id": "string", // (可选) 如果系统自动为新角色创建了一个默认故事线，则返回其ID
            "version": 1,             // 初始版本号
            "status": "active"        // 角色状态，例如 "active", "draft"
        },
        "detail": null
    }
    ```
    -   **成功状态码:**
        *   `201 Created`: 角色创建成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数无效或缺失 (例如 `name` 为空, `opening_message_templates` 格式错误, 上传文件类型不支持等)。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `413 Payload Too Large`: 上传的图片或知识库文件过大。
        *   `422 Unprocessable Entity`: 语义错误，例如标签格式不正确，或者 `initial_memories_text` 过长。
    -   **注意:** 角色创建时，系统可能会自动创建一个默认的 Story (故事线) 并关联到该角色。
#### 3.1.2. 根据ID获取角色信息 (Get Character by ID)
-   **Method:** `GET`
-   **Endpoint:** `/character/{character_id}`
-   **Description:** 根据角色ID获取单个AI角色的详细公开信息。
-   **APP端优先级:** P0
-   **APP端是否需要:** 是 (进入角色详情页或开始聊天前获取信息)
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Character details retrieved.",
        "data": {
            "_id": "string",                        // 角色ID
            "name": "CharacterName",                // 角色名
            "description": "Character description.",    // 角色描述
            "setting": "Character setting details.",    // 角色设定
            "background_story": "Detailed background story of the character.", // 角色背景故事
            "greeting_message": "Hello, I am CharacterName.", // 角色问候语
            "opening_message_templates_summary": [ // 开场白模板摘要列表 (源自创建时的 `opening_message_templates`, 完整模板内容可能在编辑时获取)
                {"template_id": "tpl001", "short_desc": "通用问候"},
                {"template_id": "tpl002", "short_desc": "特定场景问候"}
            ],
            "sample_conversations": [               // 对话示例 (同创建时的 `sample_conversations`)
                {"role": "user", "text": "你好"},
                {"role": "ai", "text": "你好呀！有什么可以帮你的吗？"}
            ],
            "personality_tags": [                   // 性格标签列表 (源自创建时的 `personality_tags` JSON字符串)
                {"tag_name": "温柔", "weight": 0.8},
                {"tag_name": "傲娇", "weight": 0.5}
            ],
            "content_tags": ["日常", "校园"],        // 内容标签列表 (源自创建时的 `content_tags` 逗号分隔字符串)
            "interaction_style_tags": ["empathetic", "playful"], // 互动风格标签列表 (源自创建时的 `interaction_style_tags` 逗号分隔字符串)
            "voice_ids": ["voice_id1", "voice_id2"],// 使用的语音ID列表 (源自创建时的 `voice_ids` 逗号分隔字符串)
            "gender": "female",                     // 性别 (同创建时的 `gender`)
            "visibility": "public",                 // 可见性 (同创建时的 `visibility`)
            "initial_memories_summary": "喜欢蓝色，害怕打雷。", // 初始记忆文本摘要 (源自创建时的 `initial_memories_text`)
            "knowledge_files_info": [               // 知识库文件信息列表 (源自创建时的 `knowledge_files`)
                {"file_id": "file001", "file_name": "world_lore.pdf", "file_type": "pdf", "size_kb": 1024}
            ],
            "image_url": "url",                     // 角色立绘图片 (对应创建时的 `image`)
            "image_url": "URL",
            "avatar_url": "url",                    // 角色头像图片 (对应创建时的 `avatar`)
            "image_url": "url",
            "user_id": "string",                    // 创建者用户ID
            "user_name": "CreatorName",             // 创建者用户名
            "follower_count": 105,                  // 角色粉丝数 (原 follower)
            "chat_count": 1200,                     // 与该角色聊天次数/热度 (原 chat)
            "created_at": "2025-08-24T14:15:22Z",
            "last_updated_at": "2025-08-25T10:30:00Z",
            "version": 2,                           // 角色版本号
            "status": "active",                     // 角色状态: "active", "archived", "draft"
            "custom_prompt_prefix_enabled": false,  // 是否启用了自定义Prompt前缀 (关联创建时的 `custom_prompt_prefix`)
            "have_followed": false,                 // 当前登录用户是否已关注该角色
            "bond_level": 5,                        // (可选) 当前登录用户与该角色的羁绊等级
            "bond_exp": 120,                        // (可选) 当前登录用户与该角色的羁绊经验值
            "is_official": false,                   // 是否为官方认证角色
            "is_featured": true                     // 是否为精选推荐角色
        },
        "detail": null
    }
    ```
    -   **成功状态码:**
        *   `200 OK`: 角色信息获取成功。
    -   **错误状态码:**
        *   `404 Not Found`: 指定 `character_id` 的角色不存在。
    -   **注意:** 返回结构已大幅更新以符合V2设计，并与 `3.1.1 创建角色` 的字段保持对应。`opening` 字段已替换为 `greeting_message` 和 `opening_message_templates_summary`。`tags` 已细化。`knowledges` 已更名为 `knowledge_files_info`。
#### 3.1.3. 修改角色信息 (Update Character)
-   **Method:** `PATCH`
-   **Endpoint:** `/character/{character_id}`
-   **Description:** 修改现有AI角色的信息 (通常仅限角色创建者)。传入需要修改的字段，所有字段均为可选。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **请求参数 (Body - `multipart/form-data`):** (所有参数均为可选。如提供，则更新对应字段。新增 `version_notes`, `delete_knowledge_file_ids`, `ai_model_id`, `temperature` 用于特定修改操作)
参数名                        | 类型                  | 说明 (参考 `3.1.1 创建角色` 的详细定义和示例，除非另有说明)                                                                                   |
:---------------------------- | :-------------------- | :--------------------------------------------------------------------------------------------------------------------------------- |
`name`                        | `string`              | 角色的名字 (例如："温柔学姐")                                                     |
`description`                 | `string`              | 角色的详细描述 (例如："一位总是耐心倾听你烦恼的邻家大姐姐。")                        |
`setting`                     | `string`              | 角色的世界观、背景等设定 (例如："她住在充满魔法的奇幻小镇，经营着一家神秘的草药店。")  |
`background_story`            | `string`              | 角色的背景故事 (更详细的叙事性背景)。                                              |
`greeting_message`            | `string`              | 角色问候语 (例如："你好呀，今天过得怎么样？")                                      |
`opening_message_templates`   | `string`              | 开场白模板列表 (JSON字符串, 格式: `[{"template_id": "unique_id_1", "content": "你好，我是小爱！", "priority": 1, "conditions": null}]`)。更新时会替换整个列表。 |
`sample_conversations`        | `string`              | 对话示例 (JSON字符串, 格式: `[{"role": "user", "text": "你好"}, {"role": "ai", "text": "你好呀！"}]`)。更新时会替换整个列表。                 |
`personality_tags`            | `string`              | 性格标签 (JSON字符串, 格式: `[{"tag_name": "温柔", "weight": 0.8}, {"tag_name": "傲娇", "weight": 0.5}]`)。更新时会替换整个列表。                  |
`content_tags`                | `string`              | 内容标签 (逗号分隔, e.g., "日常,校园,奇幻")。更新时会替换整个列表。                  |
`interaction_style_tags`      | `string`              | 互动风格标签 (逗号分隔, e.g., "empathetic,formal,playful")。更新时会替换整个列表。  |
`voice_ids`                   | `string`              | 角色使用的语音ID (逗号分隔, e.g., "voice_id1,voice_id2")。更新时会替换整个列表。     |
`gender`                      | `string`              | 性别: `male`, `female`, `other`。                                                |
`visibility`                  | `string`              | 可见性: `public` (公开), `unlisted` (不公开列出但链接可访问), `private` (私密)。    |
`initial_memories_text`       | `string`              | 初始记忆文本 (例如："我最喜欢的颜色是蓝色。我害怕打雷。")。                           |
`knowledge_files`             | `List[UploadFile]`    | 新增的知识库文件 (txt, pdf, md, docx)。                                           |
`delete_knowledge_file_ids`   | `string`              | (修改时特有) 要删除的知识库文件ID列表 (逗号分隔)。                                  |
`image`                       | `UploadFile`          | 角色立绘图片 (全身或半身像)。提供新图片会替换旧的。                                  |
`avatar`                      | `UploadFile`          | 角色头像图片。提供新图片会替换旧的。                                                |
`custom_prompt_prefix`        | `string`              | 自定义Prompt前缀。传空字符串可清空。 (高级功能，用于在每次请求AI前附加特定指令)        |
`version_notes`               | `string`              | (修改时特有) 版本更新备注 (例如："优化了性格标签，增加了新的开场白")。                 |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Character updated successfully.",
        "data": { // 更新后的角色对象，结构同 GET /character/{character_id} 的 data 字段
            "_id": "string",
            "name": "UpdatedCharacterName",
            "description": "Updated description.",
            "version": 3, // 版本号可能已增加
            "last_updated_at": "2025-08-26T11:00:00Z"
            // ... 其他字段
        },
        "detail": null
    }
    ```
    -   **成功状态码:**
        *   `200 OK`: 角色信息修改成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数无效 (例如 `name` 为空, 标签格式错误)。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `403 Forbidden`: 当前用户无权限修改该角色 (例如非角色创建者)。
        *   `404 Not Found`: 指定 `character_id` 的角色不存在。
        *   `413 Payload Too Large`: 上传的图片或知识库文件过大。
        *   `422 Unprocessable Entity`: 语义错误 (例如 `version_notes` 过长)。
#### 3.1.4. AI辅助生成角色基础信息 (AI Generate Character Profile)
-   **Method:** `POST`
-   **Endpoint:** `/character/ai-generate-profile`
-   **Description:** 用户提供主题、关键词或简要描述，AI辅助生成角色的基础信息，如名称、描述、性格标签、问候语等。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名          | 类型     | 是否必需 | 说明                                                                 |
    | :-------------- | :------- | :------- | :------------------------------------------------------------------- |
    | `theme`         | `string` | 否       | 角色主题 (例如："奇幻冒险", "校园恋爱", "赛博朋克")                      |
    | `keywords`      | `string` | 否       | 关键词 (逗号分隔, 例如："法师,冷静,古老")                               |
    | `user_preference`| `string`| 否       | 用户偏好描述 (例如："我想要一个有点神秘感的女性角色")                     |
    | `num_suggestions`| `int`   | 否       | 希望AI生成的建议数量 (默认: 1)                                         |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "AI generated character profile suggestions successfully.",
        "data": [ // 返回一个或多个建议的角色配置对象
            {
                "suggested_name": "艾拉·星语者",
                "suggested_description": "一位掌握古老星辰魔法的神秘女巫，常常在月夜下低语着不为人知的咒语。",
                "suggested_greeting_message": "你好，迷途的旅人，星辰指引你来到我的面前。",
                "suggested_personality_tags": [
                    {"tag_name": "神秘", "weight": 0.9},
                    {"tag_name": "智慧", "weight": 0.7},
                    {"tag_name": "优雅", "weight": 0.6}
                ],
                "suggested_content_tags": ["奇幻", "魔法", "女巫"],
                "suggested_gender": "female"
            }
        ],
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: AI辅助生成角色基础信息成功。
-   **错误状态码:**
    *   `400 Bad Request`: 输入参数不足或无效。
    *   `401 Unauthorized`: 用户未登录。
    *   `422 Unprocessable Entity`: 无法根据输入生成有效信息。
    *   `503 Service Unavailable`: AI服务暂时不可用。

#### 3.1.5. AI辅助生成角色开场白 (AI Generate Character Opening Messages)
-   **Method:** `POST`
-   **Endpoint:** `/character/ai-generate-opening`
-   **Description:** 用户提供角色基础信息（如名称、描述、性格），AI辅助生成若干开场白模板。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名             | 类型     | 是否必需 | 说明                                                                 |
    | :----------------- | :------- | :------- | :------------------------------------------------------------------- |
    | `character_name`   | `string` | 是       | 角色名称                                                             |
    | `character_desc`   | `string` | 是       | 角色描述                                                             |
    | `personality_tags_json`| `string`| 是    | 角色性格标签 (JSON字符串, 格式: `[{"tag_name": "温柔", "weight": 0.8}]`) |
    | `num_templates`    | `int`    | 否       | 希望生成的开场白模板数量 (默认: 3)                                     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "AI generated opening message templates successfully.",
        "data": {
            "opening_message_templates": [
                {"template_id": "ai_tpl_001", "content": "你好呀，我是[角色名]，很高兴能和你聊天！今天想聊点什么呢？", "priority": 1, "conditions": null},
                {"template_id": "ai_tpl_002", "content": "嗯？是你呀，[用户昵称]。找[角色名]有什么事吗？", "priority": 2, "conditions": {"user_relationship": "known"}},
                {"template_id": "ai_tpl_003", "content": "嘘...小声点，我是[角色名]。你看起来不像坏人，我们可以聊聊。", "priority": 1, "conditions": {"setting": "stealth"}}
            ]
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: AI辅助生成开场白成功。
-   **错误状态码:**
    *   `400 Bad Request`: 输入参数不足或无效。
    *   `401 Unauthorized`: 用户未登录。
    *   `422 Unprocessable Entity`: 无法根据输入生成有效开场白。
    *   `503 Service Unavailable`: AI服务暂时不可用。

#### 3.1.6. AI辅助生成角色故事背景/钩子 (AI Generate Character Story Hook)
-   **Method:** `POST`
-   **Endpoint:** `/character/ai-generate-story-hook`
-   **Description:** 用户提供角色基础信息，AI辅助生成角色背景故事的片段或有趣的剧情钩子。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名             | 类型     | 是否必需 | 说明                                                                 |
    | :----------------- | :------- | :------- | :------------------------------------------------------------------- |
    | `character_name`   | `string` | 是       | 角色名称                                                             |
    | `character_desc`   | `string` | 是       | 角色描述                                                             |
    | `personality_tags_json`| `string`| 是    | 角色性格标签 (JSON字符串)                                             |
    | `content_tags_csv` | `string` | 否       | 角色内容标签 (逗号分隔, e.g., "奇幻,冒险")                             |
    | `num_suggestions`  | `int`    | 否       | 希望生成的背景/钩子建议数量 (默认: 1)                                  |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "AI generated story hooks successfully.",
        "data": [
            {
                "suggested_background_story_snippet": "艾拉出生在一个被遗忘的星辰部落，她的族人世代守护着连接现实与梦境的古老通道。然而，一股黑暗势力正试图侵蚀这条通道...",
                "suggested_plot_hook": "一天，艾拉发现她脖子上的星月吊坠发出了异样的光芒，似乎在指引她去寻找失落已久的“梦境之心”。"
            }
        ],
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: AI辅助生成故事背景/钩子成功。
-   **错误状态码:**
    *   `400 Bad Request`: 输入参数不足或无效。
    *   `401 Unauthorized`: 用户未登录。
    *   `422 Unprocessable Entity`: 无法根据输入生成有效内容。
    *   `503 Service Unavailable`: AI服务暂时不可用。

#### 3.1.7. 删除角色 (Delete Character)
-   **Method:** `DELETE`
-   **Endpoint:** `/character/{character_id}`
-   **Description:** 删除指定的AI角色 (通常仅限角色创建者)。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 204 No Content
        "message": "Character deleted successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 角色删除成功。
      *   `204 No Content`: 角色删除成功，且响应体为空。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `403 Forbidden`: 当前用户无权限删除该角色 (例如非角色创建者)。
      *   `404 Not Found`: 指定 `character_id` 的角色不存在。

### 3.2. 角色发现 (Character Discovery)
本节包含与角色模板的创建、发现和使用相关的功能。

#### 3.2.1. 获取公开的角色模板列表 (Get Public Character Templates)
-   **Method:** `GET`
-   **Endpoint:** `/character/templates`
-   **Description:** 获取公开可用的角色模板列表，支持筛选和分页。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
参数名      | 类型     | 是否必需 | 说明                                                     |
:---------- | :------- | :------- | :------------------------------------------------------- |
`category`  | `string` | 否       | 模板分类 (例如："fantasy", "sci-fi", "official", "community") |
`sort_by`   | `string` | 否       | 排序依据 (例如："popularity", "newest", "usage_count")    |
`num`       | `int`    | 否       | 返回数量 (默认: 10)                                       |
`offset`    | `int`    | 否       | 偏移量 (默认: 0)                                          |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Character templates retrieved successfully.",
        "data": [
            {
                "template_id": "tpl_char_001",
                "name": "英勇的骑士模板",
                "description": "一个基础的英勇骑士角色模板，包含常用设定和性格。",
                "creator_name": "官方团队",
                "tags": ["奇幻", "骑士", "官方推荐"],
                "usage_count": 1500,
                "preview_image_url": "url_to_template_preview_image.jpg",
                "base_character_profile": { // 模板包含的基础角色信息摘要
                    "greeting_message": "为了荣誉！",
                    "personality_tags_summary": ["勇敢", "正直"]
                }
            }
        ],
        "meta": {"page": 1, "page_size": 10, "total_items": 50, "total_pages": 5},
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 角色模板列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效。

#### 3.2.2. 用户将已有角色保存为模板 (Save Character as Template)
-   **Method:** `POST`
-   **Endpoint:** `/character/templates/create-from-character/{character_id}`
-   **Description:** 用户将自己创建的某个角色保存为一个新的角色模板，可选择是否公开。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明             |
:------------- | :------- | :------- | :--------------- |
`character_id` | `string` | 是       | 源角色ID         |
-   **请求参数 (Body - JSON):**
参数名           | 类型     | 是否必需 | 说明                                   |
:--------------- | :------- | :------- | :------------------------------------- |
`template_name`  | `string` | 是       | 新模板的名称                           |
`template_desc`  | `string` | 是       | 新模板的描述                           |
`template_tags`  | `string` | 否       | 新模板的标签 (逗号分隔)                  |
`is_public`      | `boolean`| 否       | 是否将此模板公开 (默认: `false`)         |
-   **返回示例 (成功):**
    ```json
    {
        "code": 201,
        "message": "Character saved as template successfully.",
        "data": {
            "template_id": "new_tpl_char_002",
            "name": "我的自定义学姐模板",
            "is_public": false
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `201 Created`: 角色成功保存为模板。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效。
    *   `401 Unauthorized`: 用户未登录。
    *   `403 Forbidden`: 用户无权操作此角色 (例如非角色创建者)。
    *   `404 Not Found`: 源角色 `character_id` 不存在。
    *   `409 Conflict`: 模板名称已存在 (如果模板名称需要唯一)。

#### 3.2.3. 用户使用模板创建新角色 (Create Character from Template)
-   **Method:** `POST`
-   **Endpoint:** `/character/create-from-template/{template_id}`
-   **Description:** 用户选择一个角色模板，并基于此模板快速创建一个新的角色草稿或直接创建角色。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名       | 类型     | 是否必需 | 说明         |
:----------- | :------- | :------- | :----------- |
`template_id`| `string` | 是       | 角色模板ID   |
-   **请求参数 (Body - JSON):** (可选，用于覆盖模板中的默认值或补充信息)
参数名      | 类型     | 是否必需 | 说明                               |
:---------- | :------- | :------- | :--------------------------------- |
`name`      | `string` | 否       | 新角色的名称 (如果想覆盖模板的名称)    |
`visibility`| `string` | 否       | 新角色的可见性 (默认: `public`)      |
`custom_data`| `object`| 否       | 其他用户想在模板基础上自定义的字段   |
-   **返回示例 (成功 - 创建了角色草稿):**
    ```json
    {
        "code": 201,
        "message": "Character draft created from template. Please review and finalize.",
        "data": {
            "draft_id": "draft_char_123", // 返回新创建的角色草稿ID，引导用户去编辑
            "message": "角色草稿已基于模板创建，请前往草稿箱编辑并发布。"
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `201 Created`: 基于模板成功创建角色草稿或角色。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效。
    *   `401 Unauthorized`: 用户未登录。
    *   `404 Not Found`: `template_id` 不存在。

### 3.3. 角色羁绊 (Character Bond)
本节定义用户与AI角色之间的羁绊（好感度）系统相关的API。

#### 3.3.1. 获取用户与角色的羁绊详情 (Get Character Bond Details)
-   **Method:** `GET`
-   **Endpoint:** `/character/{character_id}/bond-details`
-   **Description:** 获取当前登录用户与指定AI角色的羁绊等级、经验值、以及下一个等级所需经验等信息。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Character bond details retrieved.",
        "data": {
            "character_id": "char001",
            "user_id": "user123",
            "bond_level": 5,
            "bond_name": "亲密无间", // 当前羁绊等级名称
            "current_exp": 1250,
            "exp_to_next_level": 2000, // 达到下一级所需的总经验
            "exp_needed_for_next_level": 750, // 升到下一级还需要的经验
            "next_level_name": "心有灵犀", // (可选) 下一级羁绊名称
            "unlocked_features": [ // (可选) 当前羁绊等级已解锁的特性或内容
                "special_dialogue_pack_1",
                "exclusive_avatar_frame"
            ]
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 羁绊详情获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录。
    *   `404 Not Found`: 角色不存在，或用户与该角色无羁绊记录。

#### 3.3.2. 用户向角色赠送礼物 (Gift to Character)
-   **Method:** `POST`
-   **Endpoint:** `/character/{character_id}/gift`
-   **Description:** 用户消耗特定虚拟货币或道具，向AI角色赠送礼物，以提升羁绊经验值。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **请求参数 (Body - JSON):**
参数名      | 类型     | 是否必需 | 说明                                       |
:---------- | :------- | :------- | :----------------------------------------- |
`item_id`   | `string` | 是       | 赠送的礼物物品ID (来自用户背包或可购买列表)    |
`quantity`  | `int`    | 是       | 赠送数量 (通常为1)                         |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Gift sent successfully. Bond experience increased.",
        "data": {
            "character_id": "char001",
            "exp_gained": 100,
            "new_bond_level": 5, // (可选) 如果升级了，返回新等级
            "new_current_exp": 1350,
            "remaining_item_quantity": 0, // (可选) 如果礼物是消耗品，返回剩余数量
            "triggered_special_dialogue_id": "dialogue_gift_response_001" // (可选) 如果触发了特殊感谢对话
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 礼物赠送成功。
-   **错误状态码:**
    *   `400 Bad Request`: `item_id` 无效或 `quantity` 不正确。
    *   `401 Unauthorized`: 用户未登录。
    *   `402 Payment Required`: 用户虚拟货币/礼物道具不足。
    *   `404 Not Found`: 角色或礼物物品不存在。
    *   `422 Unprocessable Entity`: 此角色不可赠送此礼物，或已达每日赠送上限。

#### 3.3.3. 获取角色羁绊奖励列表 (Get Character Bond Rewards List)
-   **Method:** `GET`
-   **Endpoint:** `/character/{character_id}/bond-rewards`
-   **Description:** 获取指定角色在不同羁绊等级下可解锁的奖励内容列表。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Character bond rewards list retrieved.",
        "data": [
            {
                "bond_level_required": 3,
                "reward_id": "bond_reward_001",
                "reward_name": "专属问候语音",
                "reward_description": "解锁[角色名]的特别问候语音。",
                "reward_type": "voice_unlock", // "voice_unlock", "story_unlock", "item_grant", "avatar_frame"
                "reward_details": {"voice_id": "special_greeting_voice_003"},
                "is_claimed_by_user": true // 当前用户是否已领取此奖励
            },
            {
                "bond_level_required": 4,
                "reward_id": "bond_reward_002",
                "reward_name": "忆境拼图*3",
                "reward_description": "忆境拼图*3",
                "reward_type": "serotile", // "voice_unlock", "story_unlock", "item_grant", "avatar_frame"
                "reward_details": {"serotile": 3},
                "is_claimed_by_user": true // 当前用户是否已领取此奖励
            },
            {
                "bond_level_required": 5,
                "reward_id": "bond_reward_003",
                "reward_name": "秘密故事线：[角色名]的日记",
                "reward_description": "解锁关于[角色名]过去的秘密故事线。",
                "reward_type": "story_unlock",
                "reward_details": {"story_id": "secret_story_char001_diary"},
                "is_claimed_by_user": false
            }
        ],
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 羁绊奖励列表获取成功。
-   **错误状态码:**
    *   `404 Not Found`: 角色不存在。

#### 3.3.4. 用户领取羁绊等级奖励 (Claim Character Bond Reward)
-   **Method:** `POST`
-   **Endpoint:** `/character/{character_id}/claim-bond-reward`
-   **Description:** 用户领取已达成的特定羁绊等级对应的奖励。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **请求参数 (Body - JSON):**
参数名      | 类型     | 是否必需 | 说明         |
:---------- | :------- | :------- | :----------- |
`reward_id` | `string` | 是       | 要领取的奖励ID |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Bond reward claimed successfully.",
        "data": {
            "reward_id_claimed": "bond_reward_002",
            "reward_name_claimed": "秘密故事线：[角色名]的日记",
            "unlock_message": "您已成功解锁新的故事线，快去看看吧！" // (可选) 引导信息
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 羁绊奖励领取成功。
-   **错误状态码:**
    *   `400 Bad Request`: `reward_id` 无效。
    *   `401 Unauthorized`: 用户未登录。
    *   `403 Forbidden`: 用户未达到领取该奖励的羁绊等级，或奖励已被领取。
    *   `404 Not Found`: 角色或奖励不存在。

### 3.4. 角色发现 (Character Discovery)

#### 3.4.1. 搜索角色 (Search Characters)
-   **Method:** `GET`
-   **Endpoint:** `/character/search`
-   **Description:** 根据关键词搜索公开的角色。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
参数名  | 类型     | 是否必需 | 说明                 |
:------ | :------- | :------- | :------------------- |
`query` | `string` | 是       | 搜索关键词            |
`num`   | `int`    | 否       | 返回数量 (默认: 10)   |
`offset`| `int`    | 否       | 分页 (默认: 0)        |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Characters searched successfully.",
            "data": [ // 角色对象列表 (简略信息)
                {
                    "_id": "string",
                    "name": "SearchedCharacter",
                    "description": "Brief description...",
                    "tags": ["tagA", "tagB"],
                    "follower": 0,
                    "image_url": "url",
                    "chat": 0, // 热度/聊天次数
                    "have_followed": false // 当前用户是否关注
                }
            ],
            "meta": { // 可选，用于分页信息
                "query": "search_term",
                "num": 10,
                "offset": 0,
                "total_items": 100,
                "total_pages": 10
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 角色搜索成功 (即使结果为空列表)。
    -   **错误状态码:**
        *   `400 Bad Request`: 搜索参数 (`query`) 或分页参数 (`num`, `offset`) 无效。
#### 3.4.2. 获取推荐角色 (Recommend Characters)
-   **Method:** `GET`
-   **Endpoint:** `/character/recommend`
-   **Description:** 获取系统推荐的AI角色列表。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用于首页或发现页推荐)
-   **请求参数 (Query):**
参数名 | 类型  | 是否必需 | 说明                 |
:----- | :---- | :------- | :------------------- |
`num`   | `int`    | 否       | 推荐数量 (默认: 10)  |
`offset`| `int`    | 否       | 分页 (默认: 0)       |
-   **返回示例 (成功):** (结构同 `/character/search` 返回的列表项，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 推荐角色获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`) 无效。

#### 3.4.3. 获取相似角色 (Get Similar Characters)
-   **Method:** `GET`
-   **Endpoint:** `/character/similar/{character_id}`
-   **Description:** 获取与指定角色相似的其他AI角色列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用于角色详情页的相关推荐)
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明               |
:------------- | :------- | :------- | :----------------- |
`character_id` | `string` | 是       | 参考的角色ID       |
-   **返回示例 (成功):** (结构同 `/character/search` 返回的列表项，可能不包含 `meta` 或 `meta` 中 `query` 为空)
-   **成功状态码:**
    *   `200 OK`: 相似角色获取成功。
-   **错误状态码:**
    *   `404 Not Found`: 参考的角色 (`character_id`) 不存在。

#### 3.4.4. 获取所有角色ID (Get All Character IDs)
-   **Method:** `GET`
-   **Endpoint:** `/character/all`
-   **Description:** 获取系统中所有公开角色的ID及更新时间列表。
-   **APP端优先级:** P3 / APP无用
-   **APP端是否需要:** 否 (通常用于数据同步或后台管理)
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "All character IDs retrieved.",
        "data": [
            {
                "character_id": "string",
                "updated_at": "2025-08-24T14:15:22.123Z"
            }
        ],
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 所有角色ID获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 当前用户无权限执行此操作 (通常为管理员接口)。
### 3.5. 角色评论 (Character Comments)
**说明:** 角色评论是社区功能，整体优先级 P2。

#### 3.5.1. 发表评论 (Create Comment)
-   **Method:** `POST`
-   **Endpoint:** `/character/comment`
-   **Description:** 用户对AI角色或其关联的Story (故事线/原碎片) 发表评论。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
参数名         | 类型     | 是否必需 | 说明                                         |
:------------- | :------- | :------- | :------------------------------------------- |
`character_id` | `string` | 是       | 评论所属的角色ID                             |
`story_id`     | `string` | 否       | 评论关联的故事线ID (如果评论是针对特定故事线)  |
`content`      | `string` | 是       | 评论内容                                     |
`father_id`    | `string` | 否       | 回复的父评论ID (如果此评论是回复另一条评论)    |
`root_id`      | `string` | 否       | 回复的根评论ID (二级回复时，指向一级评论ID)    |
-   **返回示例 (成功):**
    ```json
    {
        "code": 201, // HTTP 201 Created
        "message": "Comment created successfully.",
        "data": { // 新创建的评论对象
            "_id": "string",              // 评论ID
            "character_id": "string",     // 评论所属的角色ID
            "story_id": "string",         // 评论关联的故事线ID (可选)
            "user_id": "string",          // 发表评论的用户ID
            "user_name": "CommenterName", // 发表评论的用户名 (建议使用 user_info 对象包含更完整的用户信息)
            "user_avatar_url": "url",     // 评论者头像文件直链
            "content": "This is a great character!",
            "father_id": null,            // 回复的父评论ID (可选)
            "root_id": null,              // 回复的根评论ID (可选)
            "created_at": "2025-08-25T10:00:00.000Z", // 评论创建时间
            "updated_at": "2025-08-25T10:00:00.000Z", // 评论最后更新时间
            "likes_count": 0,             // 点赞数
            "replies_count": 0,           // 回复数
            "is_liked_by_me": false,      // 当前登录用户是否已点赞该评论
            "is_pinned": false,           // 是否被置顶
            "replies": []                 // 子评论列表 (通常在获取单条评论详情或指定层级时填充)
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `201 Created`: 评论发表成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效或缺失 (例如 `content` 为空)。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 用户被禁言或无权限在此处评论。
    *   `404 Not Found`: 目标角色 (`character_id`)、故事线 (`story_id`)、父评论 (`father_id`) 或根评论 (`root_id`) 不存在。
    *   `422 Unprocessable Entity`: 评论内容不符合规范 (例如包含敏感词，长度超限等)。

#### 3.5.2. 获取角色最热评论 (Get Hottest Comments)
-   **Method:** `GET`
-   **Endpoint:** `/character/comment/hottest/{character_id}`
-   **Description:** 获取指定角色下按热度排序的评论列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **请求参数 (Query):**
参数名  | 类型  | 是否必需 | 说明                 |
:------ | :---- | :------- | :------------------- |
`num`   | `int` | 否       | 返回数量 (默认: 10)   |
`offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回评论对象列表，每个评论对象的结构同 `3.3.1. 发表评论` 成功响应中的 `data` 字段。包含 `meta` 分页信息)
    ```json
    {
        "code": 200,
        "message": "Hottest comments retrieved.",
        "data": [
            // ... 评论对象列表 ...
        ],
        "meta": {
            "num": 10,
            "offset": 0,
            "total_items": 50,
            "total_pages": 5
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 最热评论获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `404 Not Found`: 指定 `character_id` 的角色不存在。

#### 3.5.3. 获取角色最新评论 (Get Latest Comments)
-   **Method:** `GET`
-   **Endpoint:** `/character/comment/latest/{character_id}`
-   **Description:** 获取指定角色下按时间倒序的评论列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path & Query):** 同 `3.3.2. 获取角色最热评论`
-   **返回示例 (成功):** (返回评论对象列表，结构同 `3.3.2. 获取角色最热评论` 的成功响应)
-   **成功状态码:**
    *   `200 OK`: 最新评论获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `404 Not Found`: 指定 `character_id` 的角色不存在。

#### 3.5.4. 获取角色评论数量 (Get Comment Count)
-   **Method:** `GET`
-   **Endpoint:** `/character/count/comment/{character_id}`
-   **Description:** 获取指定AI角色的评论总数。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用于在角色详情页展示)
-   **请求参数 (Path):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Comment count retrieved.",
            "data": {
              "count": 123 // 评论总数
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 评论数量获取成功。
    -   **错误状态码:**
        *   `404 Not Found`: 指定 `character_id` 的角色不存在。
#### 3.5.5. 切换评论点赞状态 (Toggle Comment Like)
-   **Method:** `POST`
-   **Endpoint:** `/character/comment/toggle_like`
-   **Description:** 对指定评论进行点赞或取消点赞。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
参数名      | 类型     | 是否必需 | 说明     |
:---------- | :------- | :------- | :------- |
`comment_id`| `string` | 是       | 评论ID    |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Comment like status toggled.",
            "data": {
                "is_liked_by_me": true, // 操作后当前用户是否为点赞状态
                "likes_count": 15     // 操作后该评论的总点赞数
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 评论点赞状态切换成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `comment_id` 未提供或无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `404 Not Found`: 指定 `comment_id` 的评论不存在。
#### 3.5.6. 切换评论置顶状态 (Toggle Comment Pin)
-   **Method:** `POST`
-   **Endpoint:** `/character/comment/toggle_pin`
-   **Description:** 置顶或取消置顶评论 (通常仅限角色创建者或管理员操作)。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (角色创建者管理评论)
-   **请求参数 (Body - JSON):**
参数名      | 类型     | 是否必需 | 说明     |
:---------- | :------- | :------- | :------- |
`comment_id`| `string` | 是       | 评论ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Comment pin status toggled.",
            "data": {
                "is_pinned": true // 操作后是否为置顶状态
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 评论置顶状态切换成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `comment_id` 未提供或无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `403 Forbidden`: 当前用户无权限操作 (例如非角色创建者或管理员)。
        *   `404 Not Found`: 指定 `comment_id` 的评论不存在。
#### 3.5.7. 删除评论 (Delete Comment)
-   **Method:** `DELETE`
-   **Endpoint:** `/character/comment/{comment_id}`
-   **Description:** 删除指定评论 (通常仅限评论发布者或角色创建者/管理员)。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名      | 类型     | 是否必需 | 说明     |
:---------- | :------- | :------- | :------- |
`comment_id`| `string` | 是       | 评论ID   |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 204 No Content
        "message": "Comment deleted successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 评论删除成功。
      *   `204 No Content`: 评论删除成功，且响应体为空。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `403 Forbidden`: 当前用户无权限删除该评论 (例如非评论发布者或角色管理员)。
      *   `404 Not Found`: 指定 `comment_id` 的评论不存在。
### 3.6. 用户与角色互动 (User-Character Interactions)

#### 3.6.1. 切换角色关注状态 (Toggle Follow Character)
-   **Method:** `POST`
-   **Endpoint:** `/character/follow`
-   **Description:** 当前登录用户关注或取消关注指定的AI角色。
-   **APP端优先级:** P1 (核心用户互动)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
参数名         | 类型     | 是否必需 | 说明     |
:------------- | :------- | :------- | :------- |
`character_id` | `string` | 是       | 角色ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Follow status updated.",
            "data": {
                "have_followed": true, // 操作后当前用户是否为关注状态 (统一为 have_followed)
                "follower_count": 101 // 操作后角色的总粉丝数
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 角色关注状态更新成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `character_id` 未提供或无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `404 Not Found`: 指定 `character_id` 的角色不存在。
#### 3.6.2. 获取用户关注的所有角色 (Get User Followed Characters)
-   **Method:** `GET`
-   **Endpoint:** `/character/user/follow/{user_id}`
-   **Description:** 获取指定用户（可以是其他用户）所关注的所有AI角色列表。
-   **APP端优先级:** P2 (查看他人主页的关注列表)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名    | 类型     | 是否必需 | 说明     |
:-------- | :------- | :------- | :------- |
`user_id` | `string` | 是       | 用户ID   |
-   **请求参数 (Query):**
参数名  | 类型  | 是否必需 | 说明                 |
:------ | :---- | :------- | :------------------- |
`num`   | `int` | 否       | 返回数量 (默认: 10)   |
`offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回角色对象列表，结构类似 `/character/search` 的列表项，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 用户关注的角色列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `404 Not Found`: 指定 `user_id` 的用户不存在。

#### 3.6.3. 获取用户创建的公开角色 (Get User Created Public Characters)
-   **Method:** `GET`
-   **Endpoint:** `/character/user/create/{user_id}`
-   **Description:** 获取指定用户（可以是其他用户）所创建的公开AI角色列表。
-   **APP端优先级:** P2 (查看他人主页的创作列表)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名    | 类型     | 是否必需 | 说明     |
:-------- | :------- | :------- | :------- |
`user_id` | `string` | 是       | 用户ID   |
-   **请求参数 (Query):**
参数名  | 类型     | 是否必需 | 说明                 |
:------ | :------- | :------- | :------------------- |
`query` | `string` | 否       | 按角色名筛选         |
`num`   | `int`    | 否       | 返回数量 (默认: 10)   |
`offset`| `int`    | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回角色对象列表，结构类似 `/character/search` 的列表项，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 用户创建的公开角色列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 或查询参数 (`query`) 无效。
    *   `404 Not Found`: 指定 `user_id` 的用户不存在。

#### 3.6.4. 获取当前用户创建的所有角色 (Get Logged-in User's Created Characters)
-   **Method:** `GET`
-   **Endpoint:** `/character/self/created`
-   **Description:** 获取当前登录用户自己创建的所有AI角色列表 (包括私有的)。
-   **APP端优先级:** P1 (用户在个人中心管理自己的创作)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
参数名  | 类型     | 是否必需 | 说明                 |
:------ | :------- | :------- | :------------------- |
`query` | `string` | 否       | 按角色名筛选         |
`num`   | `int`    | 否       | 返回数量 (默认: 10)   |
`offset`| `int`    | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回角色对象列表，结构类似 `/character/search` 的列表项，包含 `meta` 分页信息，可能包含更多私有信息如是否私密等)
-   **成功状态码:**
    *   `200 OK`: 当前用户创建的角色列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 或查询参数 (`query`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

#### 3.6.5. 获取用户最近聊过的角色 (Get User Recent Chats with Characters)
-   **Method:** `GET`
-   **Endpoint:** `/character/self/chat`
-   **Description:** 获取当前登录用户最近聊过的AI角色列表。
-   **APP端优先级:** P0 (首页或聊天列表核心功能)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
参数名  | 类型  | 是否必需 | 说明                 |
:------ | :---- | :------- | :------------------- |
`num`   | `int` | 否       | 返回数量 (默认: 20)   |
`offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回角色对象列表，结构类似 `/character/search` 的列表项，并可能附带 `last_chat_at` 等字段，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 最近聊过角色列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

### 3.7. 其他角色操作 (Other Character Actions)

#### 3.7.1. 举报角色 (Report Character)
-   **Method:** `POST`
-   **Endpoint:** `/character/report`
-   **Description:** 用户举报指定的AI角色。
-   **APP端优先级:** P2 (内容安全与社区管理)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
参数名         | 类型     | 是否必需 | 说明       |
:------------- | :------- | :------- | :--------- |
`character_id` | `string` | 是       | 被举报角色ID |
`reason`       | `string` | 是       | 举报原因   |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 202 Accepted 如果举报处理是异步的
        "message": "Character reported successfully. We will review it soon.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 举报成功。
      *   `202 Accepted`: 举报已接收，将进行异步处理。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数 `character_id` 或 `reason` 未提供或无效。
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `404 Not Found`: 指定 `character_id` 的角色不存在。
      *   `422 Unprocessable Entity`: 举报原因过短或内容不当。
      *   `429 Too Many Requests`: 用户短时间内提交过多举报。
#### 3.7.2. 分享角色 (Share Character)
-   **Method:** `POST`
-   **Endpoint:** `/character/share`
-   **Description:** 用户分享指定的AI角色 (记录分享事件，并可能返回用于分享的链接或图片信息)。
-   **APP端优先级:** P2 (社区分享功能)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
参数名         | 类型     | 是否必需 | 说明       |
:------------- | :------- | :------- | :--------- |
`character_id` | `string` | 是       | 被分享角色ID |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Character share event recorded.",
            "data": {
                "image_url": "https://example.com/share_image_character.jpg", // 分享用图片URL
                "link": "https://app.example.com/character/character_id_example", // 分享链接
                "cta": "快来和[角色名]聊天吧！" // 分享文案建议
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 分享事件记录成功并返回分享所需信息。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `character_id` 未提供或无效。
        *   `404 Not Found`: 指定 `character_id` 的角色不存在。
---
## 四、故事线 (Story - stories)

本模块API用于管理“故事线”。故事线是隶属于某个特定AI角色的独立叙事单元或场景，用户在选定的故事线内与AI角色开启对话会话(Session)。

### 4.1. 故事线发现 (Story Discovery)

#### 4.1.1. 获取推荐故事线 (Recommend Stories)
-   **Method:** `GET`
-   **Endpoint:** `/story/recommend`
-   **Description:** 获取系统推荐的公开故事线列表。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用于首页、发现页或角色详情页推荐相关故事线)
-   **请求参数 (Query):**
    | 参数名    | 类型     | 是否必需 | 说明                                       |
    | :-------- | :------- | :------- | :----------------------------------------- |
    | `num`     | `int`    | 否       | 推荐数量 (默认: 10)                         |
    | `exclude` | `string` | 否       | 需要排除的故事线ID (逗号分隔, e.g., "id1,id2") |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Recommended stories retrieved.",
        "data": [
            {
                "_id": "string",               // 故事线ID (story_id)
                "title": "A Day at the Beach", // 故事线标题
                "description": "Enjoy a relaxing day at the beach with [CharacterName].", // 故事线描述
                "opening": "The sun is warm and the waves are gentle. What do you want to do first?", // 故事线的开场白
                "character_id": "string",      // 关联的角色ID
                "character_name": "Sunny",     // 关联的角色名 (方便前端展示)
                "character_avatar_url": "url", // 关联角色头像url (方便前端展示)
                "user_id": "string",           // 创建该故事线的用户ID
                "user_name": "StoryCreator",   // 创建者用户名 (方便前端展示)
                "tags": ["beach", "relaxing"], // 标签列表
                "image_url": "url",            // 故事线封面图片
                "chat_count": 0,               // 热度/聊天次数 (针对此故事线)
                "like_count": 0,               // 点赞数
                "is_liked_by_me": false,       // 当前登录用户是否已点赞
                "is_private": false,           // 是否为私密故事线
                "created_at": "2025-08-24T14:15:22.123Z", // 创建时间
                "updated_at": "2025-08-24T15:00:00.000Z"  // 最后更新时间
            }
        ],
        "meta": { // 可选，用于分页信息
            "num": 10,
            "exclude": "id1,id2",
            "total_items": 100, // 假设的总数，如果可获取
            "total_pages": 10   // 假设的总页数
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 推荐故事线获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`) 或排除参数 (`exclude`) 无效。
#### 4.1.2. 根据标签获取故事线 (Get Stories by Tag)
-   **Method:** `GET`
-   **Endpoint:** `/story/tag`
-   **Description:** 获取指定标签下的公开故事线列表。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用于按标签分类浏览故事线)
-   **请求参数 (Query):**
    | 参数名  | 类型     | 是否必需 | 说明                 |
    | :------ | :------- | :------- | :------------------- |
    | `tag`   | `string` | 是       | 要检索的标签名       |
    | `num`   | `int`    | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int`    | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回故事线对象列表，结构同 `4.1.1. 获取推荐故事线` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 按标签获取故事线成功。
-   **错误状态码:**
    *   `400 Bad Request`: `tag` 参数缺失或分页参数 (`num`, `offset`) 无效。

#### 4.1.3. 获取关注角色的故事线 (Get Followed Characters' Stories)
-   **Method:** `GET`
-   **Endpoint:** `/story/followed`
-   **Description:** 获取当前登录用户所关注的角色们发布的公开故事线列表。
-   **APP端优先级:** P2 (社区动态，聚合关注内容)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回故事线对象列表，结构同 `4.1.1. 获取推荐故事线` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 关注角色的故事线获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

#### 4.1.4. 搜索故事线 (Search Stories)
-   **Method:** `GET`
-   **Endpoint:** `/story/search`
-   **Description:** 根据关键词搜索公开的故事线 (可能匹配标题、描述、标签)。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型     | 是否必需 | 说明                 |
    | :------ | :------- | :------- | :------------------- |
    | `query` | `string` | 是       | 搜索关键词           |
    | `num`   | `int`    | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int`    | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回故事线对象列表，结构同 `4.1.1. 获取推荐故事线` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 故事线搜索成功。
-   **错误状态码:**
    *   `400 Bad Request`: `query` 参数缺失或分页参数 (`num`, `offset`) 无效。

### 4.2. 故事线管理 (Story Management)

#### 4.2.1. 创建故事线 (Create Story)
-   **Method:** `POST`
-   **Endpoint:** `/story/create`
-   **Description:** 用户为某个AI角色创建一个新的故事线。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - `multipart/form-data`):**
    | 参数名        | 类型         | 是否必需 | 说明                                      |
    | :------------ | :----------- | :------- | :---------------------------------------- |
    | `character_id`| `string`     | 是       | 该故事线所属的AI角色ID                         |
    | `title`       | `string`     | 是       | 故事线的标题                                   |
    | `description` | `string`     | 是       | 故事线的详细描述                               |
    | `opening`     | `string`     | 是       | 该故事线的开场白 (AI与用户开始对话的第一句话)    |
    | `tags`        | `string`     | 是       | 标签 (英文逗号分隔, e.g., "adventure,fantasy") |
    | `image`       | `UploadFile` | 是       | 故事线的封面/背景图片                           |
    | `is_private`  | `boolean`    | 否       | 是否为私密故事线 (默认: false，即公开)           |
    | `bgm_url`     | `string`     | 否       | 背景音乐URL (可选)                             |
    | `user_bio`    | `string`     | 否       | 用户在此故事线中的特定人设/简介 (可选)           |
    | `setting`     | `string`     | 否       | 此故事线的特定附加设定 (可选)                   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 201,
        "message": "Story created successfully.",
        "data": { // 建议返回完整的故事线对象，结构同 4.1.1 获取推荐故事线中的列表项
            "_id": "string", // 新创建的故事线ID
            "title": "Newly Created Story Title",
            "character_id": "string",
            "is_private": false
            // ... 其他相关字段 ...
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `201 Created`: 故事线创建成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效或缺失 (例如 `title`, `opening`, `character_id` 缺失，`tags` 格式错误)。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 关联的角色 (`character_id`) 不存在。
    *   `413 Payload Too Large`: 上传的图片文件过大。
    *   `422 Unprocessable Entity`: 语义错误，例如 `title` 过长。

#### 4.2.2. 设置故事线用户简介 (Set Story User Bio)
-   **Method:** `POST`
-   **Endpoint:** `/story/set-bio/{story_id}`
-   **Description:** 用户为自己在一个特定的故事线中设置/人设，这可能会影响AI在该故事线中与用户的互动方式。
-   **APP端优先级:** P2 (高级互动功能)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名 | 类型     | 是否必需 | 说明                               |
    | :----- | :------- | :------- | :--------------------------------- |
    | `bio`  | `string` | 是       | 用户为该故事线设定的个人简介/人设   |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "Story user bio updated successfully.",
        "data": {
          "story_id": "string",
          "bio": "Updated user bio for this story."
        },
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 故事线用户简介更新成功。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数 `bio` 未提供或无效。
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
#### 4.2.3. 切换故事线点赞状态 (Toggle Story Like)
-   **Method:** `POST`
-   **Endpoint:** `/story/toggle_like`
-   **Description:** 当前登录用户对指定故事线进行点赞或取消点赞。
-   **APP端优先级:** P2 (社区互动)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `story_id`| `string` | 是       | 故事线ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Story like status toggled.",
            "data": {
                "is_liked_by_me": true, // 操作后当前用户是否为点赞状态
                "like_count": 26     // 操作后该故事线的总点赞数
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 故事线点赞状态切换成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `story_id` 未提供或无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
#### 4.2.4. 根据ID获取故事线详情 (Get Story by ID)
-   **Method:** `GET`
-   **Endpoint:** `/story/{story_id}`
-   **Description:** 根据故事线ID获取其详细信息。
-   **APP端优先级:** P0
-   **APP端是否需要:** 是 (进入故事线详情页或开始聊天前加载)
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `story_id`| `string` | 是       | 故事线ID   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Story details retrieved.",
        "data": {
            "_id": "story_abc_123",
            "title": "A Mysterious Forest Adventure",
            "description": "Explore the deep secrets of an ancient and enchanted forest, where dangers and wonders await.",      //用于展示的简单介绍
            "opening": "You find yourself standing at the edge of a dark, ancient forest. A narrow path disappears into the shadows. What do you do?",
            "character_id": "char_xyz_789",
            "character_name": "ShadowSeeker The Ranger",
            "character_avatar_url": "url",
            "user_id": "user_creator_123",
            "user_name": "ForestCreator",
            "tags": ["mystery", "forest", "adventure", "exploration"],
            "image_url": "url",
            "chat_count": 1523,
            "like_count": 258,
            "is_liked_by_me": true, // 当前登录用户是否点赞
            "is_private": false,
            "bgm_url": "https://example.com/music/mysterious_forest.mp3",
            "setting": "The forest is rumored to hold ancient magic, guarded by mystical creatures. Time flows differently within its depths.",  //story's system prompt
            "user_bio_for_story": "I am a brave explorer, known for my keen senses and survival skills, seeking the legendary Sunstone.", // 当前登录用户为此故事线设置的简介
            "created_at": "2025-08-24T14:15:22.123Z",
            "updated_at": "2025-08-25T10:00:00.000Z"
        },
        "detail": null
    }
    ```
    -   **成功状态码:**
        *   `200 OK`: 故事线详情获取成功。
    -   **错误状态码:**
        *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
        *   `401 Unauthorized`: 如果故事线是私有的，且当前用户无权访问。
#### 4.2.5. 修改故事线信息 (Update Story)
-   **Method:** `PATCH`
-   **Endpoint:** `/story/{story_id}`
-   **Description:** 修改现有的故事线信息 (通常仅限创建者)。参数同创建故事线，传入需要修改的字段。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `story_id`| `string` | 是       | 故事线ID   |
-   **请求参数 (Body - `multipart/form-data`):** (所有字段均为可选，仅传入需要更新的字段)
    | 参数名        | 类型         | 说明                                      |
    | :------------ | :----------- | :---------------------------------------- |
    | `title`       | `string`     | 新的故事线标题                              |
    | `description` | `string`     | 新的故事线详细描述                          |
    | `opening`     | `string`     | 新的故事线开场白                           |
    | `tags`        | `string`     | 新的标签 (英文逗号分隔)                     |
    | `image`       | `UploadFile` | 新的封面/背景图片 (会替换旧的)              |
    | `is_private`  | `boolean`    | 是否设为私密故事线                         |
    | `bgm_url`     | `string`     | 新的背景音乐URL (传空字符串可清空)          |
    | `user_bio`    | `string`     | 用户在此故事线中的新特定人设 (传空字符串可清空) |
    | `setting`     | `string`     | 此故事线的特定附加设定 (传空字符串可清空)     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Story updated successfully.",
        "data": {
            "_id": "story_abc_123",
            "title": "An Updated Mysterious Forest Adventure",
            "description": "The secrets of the ancient and enchanted forest are now even deeper, and new wonders await the brave.",
            "opening": "You stand again at the edge of the shadowed forest. The path seems different now. What will you do?",
            "character_id": "char_xyz_789",
            "character_name": "ShadowSeeker The Ranger",
            "user_id": "user_creator_123",
            "user_name": "ForestCreator",
            "tags": ["mystery", "forest", "adventure", "updated"],
            "image_url": "url", 
            "is_private": false,
            "bgm_url": "https://example.com/music/mysterious_forest_updated.mp3",
            "setting": "The ancient magic stirs, revealing new paths and guardians.",
            "user_bio_for_story": "I am a seasoned explorer, now more determined than ever.",
            "chat_count": 1523, // 通常不在此处更新
            "like_count": 258,  // 通常不在此处更新
            "is_liked_by_me": true, // 通常不在此处更新
            "created_at": "2025-08-24T14:15:22.123Z",
            "updated_at": "2025-09-01T14:30:00.000Z" // 更新时间已改变
        },
        "detail": null
    }
    ```
    -   **成功状态码:**
        *   `200 OK`: 故事线修改成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数无效 (例如 `title` 过长)。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `403 Forbidden`: 当前用户无权限修改该故事线 (例如非创建者)。
        *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
        *   `413 Payload Too Large`: 上传的图片文件过大。
#### 4.2.6. 删除故事线 (Delete Story)
-   **Method:** `DELETE`
-   **Endpoint:** `/story/{story_id}`
-   **Description:** 删除指定的故事线 (通常仅限创建者)。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `story_id`| `string` | 是       | 故事线ID   |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 204 No Content
        "message": "Story deleted successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 故事线删除成功。
      *   `204 No Content`: 故事线删除成功，且响应体为空。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `403 Forbidden`: 当前用户无权限删除该故事线 (例如非创建者)。
      *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
### 4.3. 用户特定的故事线列表 (User-Specific Story Lists)

#### 4.3.1. 获取角色下的所有故事线 (Get All Stories by Character)
-   **Method:** `GET`
-   **Endpoint:** `/story/character/{character_id}`
-   **Description:** 获取指定AI角色所拥有的所有公开故事线列表。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (在角色详情页展示其所有故事线)
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回故事线对象列表，结构同 `4.1.1. 获取推荐故事线` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 角色下的故事线列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `404 Not Found`: 指定 `character_id` 的角色不存在。

#### 4.3.2. 获取相似的故事线 (Get Similar Stories)
-   **Method:** `GET`
-   **Endpoint:** `/story/similar/{story_id}`
-   **Description:** 获取与指定故事线相似的其他公开故事线列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用于故事线详情页的相关推荐)
-   **请求参数 (Query):**
    | 参数名 | 类型  | 是否必需 | 说明                 |
    | :----- | :---- | :------- | :------------------- |
    | `num`  | `int` | 否       | 返回数量 (默认: 10)   |
-   **返回示例 (成功):** (返回故事线对象列表，结构同 `4.1.1. 获取推荐故事线` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 相似故事线获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`) 无效。
    *   `404 Not Found`: 参考的故事线 (`story_id`) 不存在。

#### 4.3.3. 获取某用户创建的公开故事线 (Get User Created Public Stories)
-   **Method:** `GET`
-   **Endpoint:** `/story/user/create/{user_id}`
-   **Description:** 获取指定用户（可以是其他用户）所创建的公开故事线列表。
-   **APP端优先级:** P2 (查看他人主页的创作列表)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回故事线对象列表，结构同 `4.1.1. 获取推荐故事线` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 用户创建的故事线列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `404 Not Found`: 指定 `user_id` 的用户不存在。

#### 4.3.4. 获取所有故事线ID (Get All Story IDs)
-   **Method:** `GET`
-   **Endpoint:** `/story/all`
-   **Description:** 获取系统中所有公开故事线的ID及更新时间列表。
-   **APP端优先级:** P3 / APP无用
-   **APP端是否需要:** 否 (通常用于数据同步或后台管理)
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "All story IDs retrieved.",
            "data": [
                {
                    "story_id": "string",
                    "updated_at": "2025-08-24T14:15:22.123Z"
                }
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 所有故事线ID获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `403 Forbidden`: 当前用户无权限执行此操作 (通常为管理员接口)。
#### 4.3.5. 获取用户聊过的故事线 (Get User's Chatted Stories)
-   **Method:** `GET`
-   **Endpoint:** `/story/self/chat`
-   **Description:** 获取当前登录用户最近聊过的故事线列表 (去重，按最近聊天时间排序)。
-   **APP端优先级:** P0 (首页或聊天列表核心功能)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 20)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "User's chatted stories retrieved.",
            "data": [ // 故事线对象列表，并附带最新会话信息
                {
                    "_id": "string",          // 故事线ID (story_id)
                    "title": "Chatted Story Title",
                    "character_id": "string",
                    "character_name": "string", // 关联的角色名
                    "character_avatar_url": "url", // 关联角色头像
                    "image_url": "url",       // 故事线封面图片
                    // 最新会话相关信息
                    "session_id": "string",   // 最新/相关的会话ID
                    "last_message_snippet": "This was the last thing said.", // 最新一条消息文本摘要
                    "last_message_sender_id": "string", // 最新消息发送者ID
                    "last_message_at": "2025-08-26T10:30:00.123Z", // 最新消息时间
                    "unread_count": 0,        // 未读消息数 (针对此会话)
                    "is_session_pinned": false // 该会话是否被置顶
                    // ... 其他故事线基础字段，参考 4.1.1
                }
            ],
            "meta": { // 可选，用于分页信息
                "num": 20,
                "offset": 0,
                "total_items": 50,
                "total_pages": 3
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 用户聊过的故事线列表获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
#### 4.3.6. 获取当前用户创建的故事线 (Get Current User's Created Stories)
-   **Method:** `GET`
-   **Endpoint:** `/story/self/created`
-   **Description:** 获取当前登录用户自己创建的所有故事线列表 (包括私有的)。
-   **APP端优先级:** P1 (用户在个人中心管理自己的创作)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回故事线对象列表，结构同 `4.1.1. 获取推荐故事线` 的成功响应，包含 `meta` 分页信息，且会包含 `is_private` 字段)
-   **成功状态码:**
    *   `200 OK`: 当前用户创建的故事线列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

### 4.4. 故事线人设管理 (Story Persona Management)
**说明:** 此处“人设(Persona)”指的是用户在特定故事线中为自己扮演的角色设定的临时身份或简介，可能会影响AI的互动。非核心功能。

#### 4.4.1. 获取故事线玩家人设 (Get Story Player Persona)
-   **Method:** `GET`
-   **Endpoint:** `/story/persona/{story_id}`
-   **Description:** 获取当前登录用户在指定故事线中设置的玩家人设。
-   **APP端优先级:** P3
-   **APP端是否需要:** 否 (除非APP支持此高级功能)
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Story persona retrieved.",
            "data": {
                "story_id": "string",
                "name": "Explorer Extraordinaire", // 玩家在该故事线中的人设名
                "persona": "A brave explorer seeking ancient artifacts." // 人设描述
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 故事线玩家人设获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `404 Not Found`: 指定 `story_id` 的故事线不存在，或者当前用户未在该故事线设置人设。
#### 4.4.2. 设置故事线玩家人设 (Set Story Player Persona)
-   **Method:** `POST`
-   **Endpoint:** `/story/persona/{story_id}`
-   **Description:** 当前登录用户为自己在指定故事线中设置玩家人设。
-   **APP端优先级:** P3
-   **APP端是否需要:** 否
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型     | 是否必需 | 说明     |
    | :-------- | :------- | :------- | :------- |
    | `name`    | `string` | 是       | 人设名称 |
    | `persona` | `string` | 是       | 人设描述 |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Story persona set successfully.",
        "data": { // 可以返回设置后的人设信息
              "story_id": "string",
              "name": "Explorer Extraordinaire",
              "persona": "A brave explorer seeking ancient artifacts."
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 故事线玩家人设设置成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `name` 或 `persona` 未提供或无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
#### 4.4.3. 把全局人设应用到故事线人设 (Set Story Persona as Global)
-   **Method:** `POST`
-   **Endpoint:** `/story/persona/set-to-user`
-   **Description:** 把全局人设应用到故事线中 (即 `/user/me` 中的 `bio`)。
-   **APP端优先级:** P3
-   **APP端是否需要:** 否
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型     | 是否必需 | 说明                   |
    | :-------- | :------- | :------- | :--------------------- |
    | `story_id`| `string` | 是       | 作为来源的故事线ID     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Global persona updated from story persona.",
        "data": { // 可以返回设置后的人设信息
              "story_id": "string",
              "name": "  ",  //复用玩家名
              "persona": "user's bio"
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 故事线玩家人设更新成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `story_id` 未提供或无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 指定 `story_id` 的故事线不存在，或用户未设置全局人设。
#### 4.4.4. 恢复故事线默认玩家人设 (Reset Story Player Persona)
-   **Method:** `POST`
-   **Endpoint:** `/story/persona/set-to-story`
-   **Description:** 清除当前登录用户在指定故事线中设置的玩家人设，或恢复为该故事线可能存在的预设玩家人设。
-   **APP端优先级:** P3
-   **APP端是否需要:** 否
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `story_id`| `string` | 是       | 故事线ID   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Story persona reset.",
        "data": { // 可以返回重置后的人设，如果故事线有默认玩家人设，则返回该默认人设；否则可返回null或空对象
            "story_id": "string",
            "name": null, // 或故事线预设的默认玩家人设名
            "persona": null // 或故事线预设的默认玩家人设描述
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 故事线玩家人设重置/清除成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `story_id` 未提供或无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
### 4.5. 其他故事线操作 (Other Story Actions)

#### 4.5.1. 举报故事线 (Report Story)
-   **Method:** `POST`
-   **Endpoint:** `/story/report`
-   **Description:** 用户举报指定的故事线。
-   **APP端优先级:** P2 (内容安全与社区管理)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型     | 是否必需 | 说明           |
    | :-------- | :------- | :------- | :------------- |
    | `story_id`| `string` | 是       | 被举报故事线ID |
    | `reason`  | `string` | 是       | 举报原因       |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 202 Accepted 如果举报处理是异步的
        "message": "Story reported successfully. We will review it soon.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 举报成功。
      *   `202 Accepted`: 举报已接收，将进行异步处理。
  -   **错误状态码:**
      *   `400 Bad Request`: 请求参数 `story_id` 或 `reason` 未提供或无效。
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
      *   `422 Unprocessable Entity`: 举报原因过短或内容不当。
      *   `429 Too Many Requests`: 用户短时间内提交过多举报。
#### 4.5.2. 分享故事线 (Share Story)
-   **Method:** `POST`
-   **Endpoint:** `/story/share`
-   **Description:** 用户分享指定的故事线 (记录分享事件，并可能返回用于分享的链接或图片信息)。
-   **APP端优先级:** P2 (社区分享功能)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型     | 是否必需 | 说明           |
    | :-------- | :------- | :------- | :------------- |
    | `story_id`| `string` | 是       | 被分享故事线ID |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Story share event recorded.",
            "data": {
                "image_url": "https://example.com/share_image_story.jpg", // 分享用图片URL
                "link": "https://app.example.com/story/story_id_example", // 分享链接
                "cta": "快来体验这个精彩的故事线吧！" // 分享文案建议
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 分享事件记录成功并返回分享所需信息。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `story_id` 未提供或无效。
        *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
## 五、聊天与会话 (Chat & Session)

本模块包含驱动AI对话的核心接口以及管理这些对话会话（Session）的接口。一次会话通常发生在用户与特定AI角色在某条特定故事线（Story）内的交互。

### 5.1. AI聊天补全 (AI Chat Completion)

#### 5.1.1. 发送消息进行聊天补全 (Chat Completion)
-   **Method:** `POST`
-   **Endpoint:** `/completion/chat`
-   **Description:** 用户在指定的故事线(Story)和会话(Session)下发送消息，AI进行回复。此接口支持流式输出AI的回复。
-   **APP端优先级:** P0
-   **APP端是否需要:** 是 (核心聊天功能)
-   **请求参数 (Body - `multipart/form-data`):**
    | 参数名          | 类型         | 是否必需 | 说明                                                                                                |
    | :-------------- | :----------- | :------- | :-------------------------------------------------------------------------------------------------- |
    | `story_id`      | `string`     | 是       | (Form Field) 当前聊天所属的故事线ID。                                                                    |
    | `session_id`    | `string`     | 否       | (Form Field) 当前聊天所属的会话ID。如果是新会话，则不传此项。                                            |
    | `query`         | `string`     | 否       | (Form Field) 用户输入的文本消息。与 `audio_input` 二选一。                                               |
    | `audio_input`   | `UploadFile` | 否       | (File Part) 用户上传的语音消息文件。与 `query` 二选一。后端将进行STT处理。                               |
    | `retalk`        | `boolean`    | 否       | (Form Field) 是否让AI重新生成上一条AI的回复 (默认: `false`)。                                           |
    | `continue_talk` | `boolean`    | 否       | (Form Field) 是否让AI直接继续它上一条未完成的回复 (默认: `false`)。                                       |
    | `request_priority`| `string`   | 否       | (Form Field) 请求优先级，可选值为: `fast` (优先快速处理), `slow` (可接受排队慢速处理)。后端会根据此偏好及系统负载决定实际处理路径。默认为系统自动判断。 |
-   **成功状态码与响应:**
    *   `200 OK`
        *   **Headers:**
            *   `X-Session-ID`: `string` - 当前或新创建的会话ID。
            *   `X-Run-ID`: `string` - 本次聊天补全操作的唯一执行ID。
            *   `Content-Type`: `text/event-stream` (如果流式传输) 或 `application/json` (如果非流式，但通常聊天接口应为流式)。
        *   **Body (流式 `text/event-stream`):** AI 回复的文本块。
            ```
            data: {"text": "Hello"}

            data: {"text": " there!"}

            data: {"text": " How can"}

            data: {"text": " I help you?"}

            data: [DONE] // (或者其他表示流结束的特殊标记, 例如一个包含 "is_finished": true 的JSON对象)
            ```
        *   **Body (非流式 `application/json` - 不推荐用于聊天):**
            ```json
            {
                "code": 200,
                "message": "AI response generated.",
                "data": {
                    "text": "Hello there! How can I help you?",
                    "session_id": "new_or_existing_session_id", // 也可以在body中返回
                    "run_id": "current_run_id" // 也可以在body中返回
                },
                "detail": null
            }
            ```
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效或缺失 (例如 `story_id`, `query` 未提供)。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 用户无权限使用该模型或故事线。
    *   `404 Not Found`: 指定的 `story_id` 不存在。
    *   `429 Too Many Requests`: 用户请求过于频繁或超出配额。
    *   `500 Internal Server Error`: AI模型服务内部错误。
    *   `503 Service Unavailable`: AI模型服务暂时不可用。
-   **注意:**
    *   强烈建议APP端处理流式输出 (`text/event-stream`)，以提供更流畅的聊天体验。
    *   `X-Session-ID` (或响应体中的 `session_id`) 对于连续对话至关重要，客户端需要保存并在后续请求中传递。
    *   `X-Run-ID` (或响应体中的 `run_id`) 用于后续可能的操作，如停止生成。

#### 5.1.2. 停止AI生成回复 (Stop Generation)
-   **Method:** `POST`
-   **Endpoint:** `/completion/stop`
-   **Description:** 用户主动停止当前正在进行的AI聊天补全（流式输出）过程。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (配合流式输出，允许用户中断)
-   **请求参数 (Body - JSON):**
    | 参数名  | 类型     | 是否必需 | 说明                                                                 |
    | :------ | :------- | :------- | :------------------------------------------------------------------- |
    | `run_id`| `string` | 是       | 执行 `/completion/chat` 时从响应头 `X-Run-ID` 获取的唯一执行ID。 |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "AI generation stopped successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 停止指令已成功发送或AI已停止。
  -   **错误状态码:**
      *   `400 Bad Request`: `run_id` 未提供或格式无效。
      *   `404 Not Found`: 指定的 `run_id` 不存在或对应的生成任务已完成/不存在。
#### 5.1.3. 辅助生成：补全用户输入框提示 (Complete Input Tips) - (信息不全)
-   **Method:** `POST`
-   **Endpoint:** `/completion/input/tips`
-   **Description:** 根据当前故事线、角色信息、对话上下文，辅助补全生成若干给玩家的发言提示。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名              | 类型     | 是否必需 | 说明                                                               |
    | :------------------ | :------- | :------- | :----------------------------------------------------------------- |
    | `story_info`        | `string` | 否       | 当前故事线信息 (标题、介绍等)                                      |
    | `character_info`    | `string` | 否       | 当前角色信息                                                       |
    | `dialogue_context`  | `string` | 否       | 当前对话上下文 (例如最近几条消息)                                    |
    | `num_tips`          | `int`    | 否       | 希望生成的提示数量 (默认: 3)                                         |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Player input tips completion successful.",
            "data": {
                "tips": [ // 补全生成的玩家发言提示列表
                    "What do you mean by that?",
                    "Let's explore the mysterious cave.",
                    "I want to ask about your past."
                ]
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 输入提示补全成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 输入参数不足或无效。
        *   `422 Unprocessable Entity`: 无法根据当前上下文生成有效提示。
        *   `503 Service Unavailable`: AI补全服务暂时不可用。

#### 5.1.4. 辅助生成：补全标题 (Complete Title) - (信息不全)
-   **Method:** `POST`
-   **Endpoint:** `/completion/title`
-   **Description:** 根据用户输入的故事线介绍、角色信息等，辅助补全生成故事线标题。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名              | 类型     | 是否必需 | 说明                                                        |
    | :------------------ | :------- | :------- | :---------------------------------------------------------- |
    | `story_description` | `string` | 是       | 故事线介绍文本                                              |
    | `character_info`    | `string` | 否       | 角色相关信息文本                                            |
    | `max_length`        | `int`    | 否       | 生成标题的最大长度 (可选)                                     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Title completion successful.",
            "data": {
                "completed_title": "Completed Story Title", // 补全生成的标题
                "suggestions": [ // 可选，提供多个建议
                    "Another Suggested Title",
                    "Yet Another Option"
                ]
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 标题补全成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 输入参数不足或无效 (例如 `story_description` 和 `character_info` 均为空)。
        *   `422 Unprocessable Entity`: 输入内容无法生成有效标题。
        *   `503 Service Unavailable`: AI补全服务暂时不可用。
#### 5.1.5. 辅助生成：补全描述 (Complete Description) - (信息不全)
-   **Method:** `POST`
-   **Endpoint:** `/completion/description`
-   **Description:** 根据用户输入的故事线标题、角色信息等，辅助补全生成故事线介绍。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名           | 类型     | 是否必需 | 说明                                                        |
    | :--------------- | :------- | :------- | :---------------------------------------------------------- |
    | `story_title`    | `string` | 是       | 故事线标题文本                                              |
    | `character_info` | `string` | 否       | 角色相关信息文本                                            |
    | `max_length`     | `int`    | 否       | 生成描述的最大长度 (可选)                                     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Description completion successful.",
            "data": {
                "completed_description": "Completed story description." // 补全生成的故事线介绍
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 描述补全成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 输入参数不足或无效。
        *   `422 Unprocessable Entity`: 输入内容无法生成有效描述。
        *   `503 Service Unavailable`: AI补全服务暂时不可用。
#### 5.1.6. 辅助生成：补全开场白 (Complete Opening) - (信息不全)
-   **Method:** `POST`
-   **Endpoint:** `/completion/opening`
-   **Description:** 根据用户输入的故事线标题、故事线介绍、角色信息等，辅助补全生成故事线中的第一条消息 (first message / opening)。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名              | 类型     | 是否必需 | 说明                                                        |
    | :------------------ | :------- | :------- | :---------------------------------------------------------- |
    | `story_title`       | `string` | 是       | 故事线标题                                                  |
    | `story_description` | `string` | 是       | 故事线介绍                                                  |
    | `character_info`    | `string` | 是       | 角色信息                                                    |
    | `max_length`        | `int`    | 否       | 生成开场白的最大长度 (可选)                                     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Opening message completion successful.",
            "data": {
                "completed_opening_message": "Completed first message for the story." // 补全生成的故事线第一条消息
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 开场白补全成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 输入参数不足或无效。
        *   `422 Unprocessable Entity`: 输入内容无法生成有效开场白。
        *   `503 Service Unavailable`: AI补全服务暂时不可用。

### 5.2. 会话管理 (Session Management)
**说明:** 会话 (Session) 代表用户与AI角色在特定故事线 (Story) 中的一次连续对话。

#### 5.2.1. 删除会话的最后一轮对话 (Delete Last Round in Session)
-   **Method:** `POST`
-   **Endpoint:** `/session/delete/last/{session_id}`
-   **Description:** 删除指定会话的最后一轮对话，通常包括用户的最后一条输入和AI对应的最后一条回复。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用户“撤回”或清除最近的对话)
-   **请求参数 (Path):**
    | 参数名      | 类型     | 是否必需 | 说明       |
    | :---------- | :------- | :------- | :--------- |
    | `session_id`| `string` | 是       | 会话ID     |
-   **返回示例 (成功):**
    ```json
    {
      "code": 200, // 或 204 No Content
      "message": "Last round deleted successfully and Endora deducted.",
      "data": {
        "endora_transaction": {
          "consumed_amount": 15,       // 示例值：具体消耗量由后端规则确定
          "previous_balance": 100,     // 示例值
          "current_balance": 85        // 示例值
        }
      },
      "detail": null
    }
    ```
  -   **成功状态码:**
      *   `200 OK`: 最近一轮对话删除成功，并已处理Endora。
      *   `204 No Content`: 删除成功且无内容返回，并已处理Endora。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `402 Payment Required`: Endora 余额不足。响应体可能如下：
          ```json
          {
            "code": 402,
            "message": "Insufficient Endora balance.",
            "data": {
              "action_cost": 15,         // 本次操作所需Endora
              "current_balance": 10,     // 用户当前Endora余额
              "shortfall": 5             // 差额
            },
            "detail": "Please acquire more Endora to proceed."
          }
          ```
      *   `404 Not Found`: 指定 `session_id` 的会话不存在，或会话已空。
#### 5.2.2. 将会话回滚到指定消息轮次 (Rewind Session to Round)
-   **Method:** `POST`
-   **Endpoint:** `/session/rewind/{round_id}`
-   **Description:** 将会话状态回滚到指定的对话轮次 (`round_id`)，效果类似删除该轮次之后的消息，并准备让AI从该点重新生成回复。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用户不满意后续对话，希望从某处重试)
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明                                   |
    | :-------- | :------- | :------- | :------------------------------------- |
    | `round_id`| `string` | 是       | 要回滚到的轮次的ID                     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Session rewinded successfully and Endora deducted.",
        "data": {
            "last_message_before_rewind": {
                "message_id": "string",
                "role": "user", // 或 "ai"
                "text": "Message content of the round we rewinded to."
            },
            "endora_transaction": {
              "consumed_amount": 20,       // 示例值：具体消耗量由后端规则确定
              "previous_balance": 85,      // 示例值
              "current_balance": 65        // 示例值
            }
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 会话回滚成功，并已处理Endora。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `402 Payment Required`: Endora 余额不足。响应体可能如下：
        ```json
        {
          "code": 402,
          "message": "Insufficient Endora balance.",
          "data": {
            "action_cost": 20,         // 本次操作所需Endora
            "current_balance": 10,     // 用户当前Endora余额
            "shortfall": 10            // 差额
          },
          "detail": "Please acquire more Endora to proceed."
        }
        ```
    *   `404 Not Found`: 指定 `round_id` 的轮次不存在或所属会话不存在。
#### 5.2.3. 查询操作的 Endora 消耗 (Query Endora Cost for Operation)
**说明:** 以下接口用于在执行实际操作前，查询预估的 Endora 消耗量。

##### *******. 查询删除最后一轮对话的 Endora 消耗
-   **Method:** `GET`
-   **Endpoint:** `/session/cost/delete/{session_id}`
-   **Description:** 查询删除指定会话最后一轮对话预计消耗的 Endora。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (在用户执行删除操作前提示消耗)
-   **返回示例 (成功):**
    ```json
    {
      "code": 200,
      "message": "Estimated Endora cost for deleting last round retrieved successfully.",
      "data": {
        "operation_type": "delete_last_round",
        "target_id": "session_xyz123",
        "estimated_endora_cost": 15 // 示例值：具体消耗量由后端规则确定
      },
      "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 预估消耗获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 指定 `session_id` 的会话不存在。
    *   `500 Internal Server Error`: 后端计算成本时发生内部错误。

##### *******. 查询回滚会话的 Endora 消耗
-   **Method:** `POST`
-   **Endpoint:** `/session/cost/rewind/{session_id}`
-   **Description:** 查询将会话回滚到指定消息轮次预计消耗的 Endora。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (在用户执行回滚操作前提示消耗)
-   **请求参数 (Query):**
    | 参数名              | 类型     | 是否必需 | 说明                                                        |
    | :------------------ | :------- | :------- | :---------------------------------------------------------- |
    | `round_id`             | `string` | 是       | 要回滚的轮数                                                  |
-   **返回示例 (成功):**
    ```json
    {
      "code": 200,
      "message": "Estimated Endora cost for rewinding session retrieved successfully.",
      "data": {
        "operation_type": "rewind_session",
        "target_id": "round_abc789",
        "estimated_endora_cost": 20 // 示例值：具体消耗量由后端规则确定
      },
      "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 预估消耗获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 指定 `round_id` 的轮次不存在或所属会话不存在。
    *   `500 Internal Server Error`: 后端计算成本时发生内部错误。
### 5.3. 会话历史与列表 (Session History & Lists)

#### 5.3.1. 获取故事线的最新会话历史记录 (Get Latest Session History for a Story)
-   **Method:** `GET`
-   **Endpoint:** `/session/story/latest/{story_id}`
-   **Description:** 获取用户与指定故事线（Story）的最新一次（或当前活动）会话的聊天记录。用于加载聊天界面。
-   **APP端优先级:** P0
-   **APP端是否需要:** 是 (进入聊天界面时加载消息)
-   **请求参数 (Query):**
    | 参数名            | 类型     | 是否必需 | 说明                                                     |
    | :---------------- | :------- | :------- | :------------------------------------------------------- |
    | `num`             | `int`    | 否       | 返回消息数量 (例如，加载最近的N条，默认值待定)            |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Session history retrieved.",
        "data": {
            "session_id": "string", // 当前会话的ID
            "story_id": "string",   // 关联的故事线ID
            "messages": [ // 消息对象列表，按时间顺序排列
                {
                    "message_id": "string", // 消息的唯一ID
                    "round_id": "string",   // 所属对话轮次ID
                    "role": "user",         // 发送者角色: "user" 或 "ai"
                    "text": "Hello, AI!",   // 消息文本内容
                    "created_at": "2025-08-27T10:00:00.123Z", // 消息创建时间
                    "type": "text",         // 消息类型: "text", "image_url", "audio_url"
                    "media_url": null       // 如果是媒体类型消息，则为媒体链接
                },
                {
                    "message_id": "string",
                    "round_id": "string",
                    "role": "ai",
                    "text": "Hello, User! How can I help you today?",
                    "created_at": "2025-08-27T10:00:05.456Z",
                    "type": "text",
                    "media_url": null
                }
            ],
            "meta": { // 分页信息
                "num": 10,
                "before_round_id": "some_round_id_or_null",
                "has_more_older": true, // 是否还有更早的历史消息
                "has_more_newer": false // 是否还有更新的消息 (通常用于实时更新)
            }
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 会话历史获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `before_round_id`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 指定 `story_id` 的故事线不存在，或用户与该故事线无会话。
#### 5.3.2. 获取指定会话的完整历史记录 (Get Full History for a Specific Session)
-   **Method:** `GET`
-   **Endpoint:** `/session/{session_id}`
-   **Description:** 获取指定 `session_id` 的完整聊天记录。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (例如，从会话列表中点开一个历史会话时加载)
-   **请求参数 (Query):** (同 `5.3.1`，用于分页加载)
    | 参数名            | 类型     | 是否必需 | 说明                                     |
    | :---------------- | :------- | :------- | :--------------------------------------- |
    | `num`             | `int`    | 否       | 返回消息数量                             |
    | `before_round_id` | `string` | 否       | 获取指定轮次ID之前的消息 (用于分页加载)   |
-   **返回示例 (成功):** (返回消息列表，结构同 `5.3.1` 的 `data` 字段，包含 `messages` 和 `meta`)
-   **成功状态码:**
    *   `200 OK`: 指定会话历史获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `before_round_id`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 用户无权访问该会话记录 (例如非会话参与者且会话非公开)。
    *   `404 Not Found`: 指定 `session_id` 的会话不存在。

#### 5.3.3. 获取故事线的所有会话摘要列表 (Get All Session Summaries for a Story)
-   **Method:** `GET`
-   **Endpoint:** `/session/story/{story_id}`
-   **Description:** 获取指定故事线（Story）下发生过的所有会话的摘要列表。每个摘要包含该会话的最后一条消息、时间、是否置顶等信息。这可以用于展示一个故事线下的多个聊天分支或历史会话入口。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (如果允许一个故事线有多段独立会话)
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                      |
    | :------ | :---- | :------- | :------------------------ |
    | `num`   | `int` | 否       | 返回会话摘要数量 (默认: 5) |
    | `offset`| `int` | 否       | 分页 (默认: 0)          |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Session summaries for story retrieved.",
            "data": [
                {
                    "session_id": "string",       // 此会话的唯一ID
                    "story_id": "string",         // 所属故事线ID
                    "character_id": "string",     // 关联的角色ID
                    "character_name": "string",   // 关联的角色名
                    "last_message_snippet": "The dragon flew away.", // 最后一条消息摘要
                    "last_message_at": "2025-08-25T18:00:00.000Z", // 最后消息时间
                    "is_pinned": false,           // 该会话是否被用户置顶
                    "title": "My First Chat with Dragon", // 会话标题 (用户可自定义或系统生成)
                    "round_count": 50,            // 会话总轮次数 (可选)
                    "unread_count": 0             // 当前用户的未读消息数 (可选)
                }
            ],
            "meta": { // 分页信息
                "num": 5,
                "offset": 0,
                "total_items": 12,
                "total_pages": 3
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 故事线会话摘要列表获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `404 Not Found`: 指定 `story_id` 的故事线不存在。
#### 5.3.4. 获取与某角色的所有会话摘要列表 (Get All Session Summaries for a Character)
-   **Method:** `GET`
-   **Endpoint:** `/session/character/{character_id}`
-   **Description:** 获取当前登录用户与指定AI角色发生过的所有会话的摘要列表（可能跨越多个故事线）。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用于聚合展示用户与特定角色的所有聊天历史入口)
-   **请求参数 (Path):**
    | 参数名         | 类型     | 是否必需 | 说明     |
    | :------------- | :------- | :------- | :------- |
    | `character_id` | `string` | 是       | 角色ID   |
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 分页 (默认: 0)       |
-   **返回示例 (成功):** (会话摘要列表，结构同 `5.3.3` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 与角色相关的会话摘要列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 指定 `character_id` 的角色不存在。

#### 5.3.5. 获取推荐分享的会话摘要列表 (Get Recommended Shared Session Summaries)
-   **Method:** `GET`
-   **Endpoint:** `/session/recommend`
-   **Description:** 获取系统推荐的可供用户分享的精彩会话片段或完整会话的摘要列表。
-   **APP端优先级:** P2 (社区内容发现与分享)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名    | 类型     | 是否必需 | 说明                                      |
    | :-------- | :------- | :------- | :---------------------------------------- |
    | `num`     | `int`    | 否       | 返回数量 (默认: 10)                        |
    | `offset`  | `int`    | 否       | 偏移量 (默认: 0)                          |
    | `user_id` | `string` | 否       | (原始文档参数，若为通用推荐则不需要此用户ID) |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Recommended sessions for sharing retrieved.",
        "data": [
            {
                "session_id": "string",       // 会话ID
                "user_id": "string",        // 创建此会话的用户ID
                "user_name": "string",      // 用户名
                "story_id": "string",       // 所属故事线ID
                "story_title": "string",    // 故事线标题
                "character_id": "string",   // 关联角色ID
                "character_name": "string", // 角色名
                "created_at": "2025-08-20T11:00:00.000Z",
                "round_count": 30,          // 会话轮次数
                "share_title": "An Epic Conversation Snippet", // 分享时使用的标题
                "excerpt_user": "And then what did the king say?", // 摘要：某用户消息
                "excerpt_ai": "The king declared a holiday!",  // 摘要：某AI消息
                "language": "en",
                "view_count": 150,          // 浏览次数
                "like_count": 20,           // 点赞数
                "is_nsfw": false
            }
        ],
        "meta": { // 分页信息
             "num": 10,
             "offset": 0,
             "total_items": 50,
             "total_pages": 5
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 推荐会话摘要获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。

#### 5.3.6. 获取关注角色的会话摘要列表 (Get Session Summaries of Followed Characters)
-   **Method:** `GET`
-   **Endpoint:** `/session/followed`
-   **Description:** 获取当前登录用户所关注的AI角色们的最新或最精彩的会话摘要列表。
-   **APP端优先级:** P2 (社区动态，聚合关注内容)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (会话摘要列表，结构同 `5.3.5` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 关注角色的会话摘要列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

### 5.4. 会话操作 (Session Actions)

#### 5.4.1. 切换会话置顶状态 (Toggle Session Pin Status)
-   **Method:** `POST`
-   **Endpoint:** `/session/toggle_pin/{session_id}`
-   **Description:** 用户置顶或取消置顶指定的会话，方便快速访问。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (聊天列表常用功能)
-   **请求参数 (Path):**
    | 参数名      | 类型     | 是否必需 | 说明     |
    | :---------- | :------- | :------- | :------- |
    | `session_id`| `string` | 是       | 会话ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Session pin status toggled.",
            "data": {
                "is_pinned": true // 操作后是否为置顶状态
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 会话置顶状态切换成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `404 Not Found`: 指定 `session_id` 的会话不存在。
#### 5.4.2. 删除会话 (Delete Session)
-   **Method:** `DELETE`
-   **Endpoint:** `/session/{session_id}`
-   **Description:** 用户删除整个指定的会话记录。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用户管理自己的聊天记录)
-   **请求参数 (Path):**
    | 参数名      | 类型     | 是否必需 | 说明     |
    | :---------- | :------- | :------- | :------- |
    | `session_id`| `string` | 是       | 会话ID   |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 204 No Content
        "message": "Session deleted successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 会话删除成功。
      *   `204 No Content`: 删除成功且无内容返回。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `404 Not Found`: 指定 `session_id` 的会话不存在。
#### 5.4.3. 分享会话 (Share Session)
-   **Method:** `POST`
-   **Endpoint:** `/session/share`
-   **Description:** 用户分享指定的会话 (可能是生成一个可公开访问的快照链接或图片)。
-   **APP端优先级:** P2 (社区分享功能)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名      | 类型     | 是否必需 | 说明                                      |
    | :---------- | :------- | :------- | :---------------------------------------- |
    | `session_id`| `string` | 是       | 要分享的会话ID                            |
    | `story_id`  | `string` | 是       | 该会话所属的故事线ID (用于上下文或展示)   |
    | `title`     | `string` | 是       | 用户为本次分享自定义的标题                |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Session shared successfully.",
            "data": {
                "share_url": "https://app.example.com/shared_session/share_id_example", // 分享链接
                "share_image_url": "https://example.com/share_image_session.jpg", // 分享卡片图片URL (可选)
                "cta": "这段对话很有趣，一起来玩吧",
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 会话分享成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `session_id`, `story_id` 或 `title` 未提供或无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `403 Forbidden`: 用户无权分享该会话。
        *   `404 Not Found`: 指定 `session_id` 或 `story_id` 不存在。
#### 5.4.4. 导出会话记录 (Export Session History)
-   **Method:** `GET`
-   **Endpoint:** `/session/export/{session_id}`
-   **Description:** 用户将会话记录导出为纯文本 (.txt) 文件。
-   **APP端优先级:** P2 (增值功能)
-   **APP端是否需要:** 是 (部分用户可能有此需求)
-   **请求参数 (Path):**
    | 参数名      | 类型     | 是否必需 | 说明       |
    | :---------- | :------- | :------- | :--------- |
    | `session_id`| `string` | 是       | 会话ID     |
-   **成功状态码与响应:**
    *   `200 OK`
        *   **Headers:**
            *   `Content-Type`: `text/plain; charset=utf-8`
            *   `Content-Disposition`: `attachment; filename="chat_history_session_id.txt"` (建议使用实际session_id或用户自定义名称)
        *   **Body:** 纯文本格式的聊天记录。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 用户无权导出该会话。
    *   `404 Not Found`: 指定 `session_id` 的会话不存在。

### 5.5. Stage 相关会话接口 (Stage-related Session APIs) - (APP端暂缓)
**说明:** `Stage` (舞台/阶段) 功能（类似角色群聊）已规划，但将作为后续新增功能。因此，以下与 `Stage` 相关的 Session API 在当前 APP 版本中暂不启用，优先级标记为 P3 (后续版本)。

#### 5.5.1. 获取用户聊过的所有Stage会话摘要 (Get User's Chatted Stages Summaries)
-   **Endpoint:** `/session/stage/all`
-   **APP端优先级:** P3 / APP暂无用

#### 5.5.2. 获取Stage最新会话历史 (Get Latest Stage Session History)
-   **Endpoint:** `/session/stage/latest/{stage_id}`
-   **APP端优先级:** P3 / APP暂无用

#### 5.5.3. 获取Stage所有会话摘要 (Get All Session Summaries for a Stage)
-   **Endpoint:** `/session/stage/{stage_id}`
-   **APP端优先级:** P3 / APP暂无用

---
## 六、图片 (Image - image)

本模块包含与图片生成、管理和发现相关的功能。

### 6.1. 图片生成 (Image Generation)
**核心功能，对应 `Plan.md` Phase 2+ 的情景化AI绘图。**

#### 6.1.1. 生成图片 - 提交任务
-   **Method:** `POST`
-   **Endpoint:** `/image/generate`
-   **Description:** 提交一个图片生成任务 (支持文本到图片 Text-to-Image, 或图片到图片 Image-to-Image)。此接口异步执行，返回一个任务ID (`request_id`)。客户端需要通过 `/image/query/{request_id}` 接口轮询获取最终的生成结果。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Body - `multipart/form-data`):**
    | 参数名            | 类型          | 是否必需 | 说明                                                                                                 |
    | :---------------- | :------------ | :------- | :--------------------------------------------------------------------------------------------------- |
    | `model`           | `string`      | 是       | 使用的模型ID (参考 `/image/model/list` 获取可用模型)                                                |
    | `width`           | `int`         | 是       | 生成图片的宽度 (px)                                                                                    |
    | `height`          | `int`         | 是       | 生成图片的高度 (px)                                                                                    |
    | `positive_prompt` | `string`      | 否       | 正向提示词 (描述希望在图片中看到的内容)                                                                  |
    | `negative_prompt` | `string`      | 否       | 负向提示词 (描述不希望在图片中看到的内容)                                                                |
    | `gender`          | `string`      | 是       | 性别提示: `male`, `female`, `other` (可能影响人物生成偏好)                                             |
    | `styles`          | `string`      | 否       | 风格参数 (JSON字符串或特定格式，例如 `{"style_id_1": 0.7, "style_id_2": 0.5}` 表示风格ID及其权重，需具体确认格式) |
    | `reference_image` | `UploadFile`  | 否       | 参考图片文件 (用于 Image-to-Image 模式)                                                              |
    | `seed`            | `int`         | 否       | 随机种子 (用于复现结果，可选)                                                                          |
    | `steps`           | `int`         | 否       | 生成步数 (影响图像质量和生成时间，可选)                                                                |
    | `cfg_scale`       | `float`       | 否       |提示词相关性 (Classifier-Free Guidance scale，可选)                                                        |
-   **返回示例 (成功提交任务):**
    ```json
    {
        "code": 202, // HTTP 202 Accepted 表示任务已接收，将在后台处理
        "message": "Image generation task submitted successfully.",
        "data": {
           "request_id": "string" // 图片生成任务的唯一ID，用于后续查询状态和结果
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `202 Accepted`: 图片生成任务已成功提交。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效或缺失 (例如 `model`, `width`, `height` 无效)。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `402 Payment Required`: 需要付费但用户余额不足或未订阅。
    *   `403 Forbidden`: 用户无权限使用该图片生成模型。
    *   `413 Payload Too Large`: 上传的 `reference_image` 过大。
    *   `422 Unprocessable Entity`: 参数语义错误 (例如 `positive_prompt` 包含非法内容)。
    *   `429 Too Many Requests`: 用户提交任务过于频繁。
    *   `503 Service Unavailable`: 图片生成服务暂时不可用。
#### 6.1.2. 查询图片生成状态与结果 (Query Image Generation Status & Result)
-   **Method:** `GET`
-   **Endpoint:** `/image/query/{request_id}`
-   **Description:** 根据 `/image/generate` 接口返回的 `request_id`，轮询查询图片生成任务的状态和最终结果。
-   **APP端优先级:** P0 (配合异步图片生成功能)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
参数名       | 类型     | 是否必需 | 说明             |
:----------- | :------- | :------- | :--------------- |
`request_id` | `string` | 是       | 图片生成任务ID   |
-   **返回示例 (成功 - 处理中):**
    ```json
    {
        {
            "code": 200,
            "message": "Image generation in progress.",
            "data": {
                "status": "processing", // "pending", "processing", "completed", "failed"
                "progress": 50,         // (可选) 生成进度百分比 0-100
                "estimated_time_seconds": 30 // (可选) 预计剩余时间 (秒)
            },
            "detail": null
        }
        ```
    -   **返回示例 (成功 - 已完成):**
        ```json
        {
            "code": 200,
            "message": "Image generated successfully.",
            "data": {
                "status": "completed",
                "image_id": "string", // 生成图片的唯一ID (重要)
                "image_url": "https://cdn.example.com/image1.png", // 生成的图片URL
                "thumbnail_url": "https://cdn.example.com/image1_thumb.png", // 缩略图URL
                "seed": "1234567890",                 // 生成图片的种子
                "request_details": { // 原始请求参数回顾
                    "positive_prompt": "a beautiful landscape",
                    "negative_prompt": "ugly, blurry",
                    "model": "stable-diffusion-xl",
                    "width": 1024,
                    "height": 768
                    // ... 其他参数
                }
            },
            "detail": null
        }
        ```
    -   **返回示例 (失败):**
        ```json
        {
            "code": 200, // 即使任务失败，查询本身是成功的，所以在data中表达失败状态
            "message": "Image generation task failed.",
            "data": {
                "status": "failed",
                "error_code": "INSUFFICIENT_CREDITS", // 错误代码
                "error_message": "Insufficient credits to generate image." // 错误描述
            },
            "detail": "Detailed error information if any."
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 查询成功，响应体中的 `status` 字段指示任务状态。
    -   **错误状态码:**
        *   `404 Not Found`: 指定的 `request_id` 不存在。
### 6.2. 图片管理与列表  (Image Management & Lists - )

#### 6.2.1. 获取当前用户生成的图片列表 ()
-   **Method:** `GET`
-   **Endpoint:** `/image/self/created`
-   **Description:** 获取当前登录用户通过  接口生成的所有图片（或图片生成记录）列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用户查看和管理自己的作品)
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "User's  created images retrieved.",
            "data": [
                {
                    "image_id": "string", // 图片的唯一ID
                    "user_id": "string",
                    "created_at": "2025-08-28T10:01:30.000Z", // 图片生成完成时间
                    "generation_type": "t2i", // "t2i" 或 "i2i"
                    "positive_prompt": "a beautiful cat",
                    "negative_prompt": "ugly, blurry",
                    "model": "sdxl_v1",        // 使用的模型ID
                    "width": 1024,
                    "height": 1024,
                    "image_url": "https://cdn.example.com/image1.png",
                    "thumbnail_url": "https://cdn.example.com/image1_thumb.png",
                    "is_favorite": false, // 用户是否收藏了这张图片
                    "is_public": true     // 图片是否公开可见
                }
            ],
            "meta": { // 分页信息
                "num": 10,
                "offset": 0,
                "total_items": 50,
                "total_pages": 5
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 图片列表获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
-   **Method:** `GET`
-   **Endpoint:** `/image/{image_id}`
-   **Description:** 获取指定  图片的详细信息。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用户查看单张图片详情)
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明              |
    | :-------- | :------- | :------- | :---------------- |
    | `image_id`| `string` | 是       | 图片的唯一ID     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Image details retrieved.",
        "data": { // 单个图片对象的完整信息，结构类似 6.2.1 列表中的项，可能包含更多细节
                "image_id": "string",
                "user_id": "string",
                "created_at": "2025-08-28T10:01:30.000Z",
                "generation_type": "t2i",
                "positive_prompt": "a beautiful cat",
                "negative_prompt": "ugly, blurry",
                "model": "sdxl_v1",
                "width": 1024,
                "height": 1024,
                "image_url": "https://cdn.example.com/image1.png",
                "thumbnail_url": "https://cdn.example.com/image1_thumb.png",
                "seed": "12345",
                "steps": 30,
                "cfg_scale": 7.5,
                "is_favorite": false,
                "is_public": true,
                "like_count": 15,
                "view_count": 100
                // ... 其他可能存在的详细信息
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 图片详情获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 若图片非公开，且非本人查看，则禁止访问。
    *   `404 Not Found`: 指定 `image_id` 的图片不存在。

#### 6.2.3. 批量获取图片详情
-   **Method:** `GET`
-   **Endpoint:** `/image/batch/{image_ids}`
-   **Description:** 批量获取一组图片的详细信息。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (例如，在图片选择器或相册中批量加载信息)
-   **请求参数 (Path):**
    | 参数名     | 类型     | 是否必需 | 说明                               |
    | :--------- | :------- | :------- | :--------------------------------- |
    | `image_ids`| `string` | 是       | 图片ID列表 (英文逗号分隔, e.g., "id1,id2,id3") |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Batch image details retrieved.",
        "data": [ // 图片对象列表，结构同 6.2.1 列表中的项
            {
                "image_id": "id1",
                // ...其他字段
            },
            {
                "image_id": "id2",
                // ...其他字段
            }
            // 注意: 如果部分ID无效，可以考虑在此处不包含这些ID的条目，或包含带有错误标记的条目
        ],
        "meta": {
            "requested_ids": ["id1", "id2", "id_not_found"],
            "found_ids": ["id1", "id2"],
            "not_found_ids": ["id_not_found"] // 可选，明确指出哪些ID未找到
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 批量图片详情获取成功（即使部分ID无效）。
-   **错误状态码:**
    *   `400 Bad Request`: `image_ids` 参数格式错误或过多。

#### 6.2.4. 下载图片
-   **Method:** `GET`
-   **Endpoint:** `/image/download/{image_id}`
-   **Description:** 获取用于下载指定图片的 URL，或直接触发图片文件下载。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用户保存图片到本地)
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明           |
    | :-------- | :------- | :------- | :------------- |
    | `image_id`| `string` | 是       | 要下载的图片ID |
-   **返回示例 (成功 - 返回下载链接):**
    ```json
    {
        {
            "code": 200,
            "message": "Image download URL retrieved.",
            "data": {
               "download_url": "https://cdn.example.com/download_image_id.png?token=xyz" // 具备时效性或权限控制的下载链接
            },
            "detail": null
        }
        ```
    -   **或者 (成功 - 直接文件流):**
        *   **Headers:** `Content-Type: image/png` (或其他图片格式), `Content-Disposition: attachment; filename="image_id.png"`
        *   **Body:** 图片的二进制数据。
    -   **成功状态码 (返回URL时):**
        *   `200 OK`: 下载链接获取成功。
    -   **成功状态码 (直接文件流时):**
        *   `200 OK`: 图片文件流传输开始。
    -   **错误状态码 (返回JSON错误):**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `403 Forbidden`: 用户无权下载该图片。
        *   `404 Not Found`: 指定 `image_id` 的图片不存在。
    -   **注意:** 建议API返回可直接使用的下载链接，由客户端发起下载，以减轻服务器负担。
#### 6.2.5. 删除图片
-   **Method:** `DELETE`
-   **Endpoint:** `/image/{image_id}`
-   **Description:** 用户删除自己生成的指定图片。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明           |
    | :-------- | :------- | :------- | :------------- |
    | `image_id`| `string` | 是       | 要删除的图片ID |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 204 No Content
        "message": "Image deleted successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 图片删除成功。
      *   `204 No Content`: 图片删除成功，无内容返回。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `403 Forbidden`: 用户无权限删除该图片 (例如非图片所有者)。
      *   `404 Not Found`: 指定 `image_id` 的图片不存在。
### 6.3. 图片生成辅助工具 (Prompt & Style Tools)

#### 6.3.1. 获取随机图片提示词 (Get Random Prompt)
-   **Method:** `GET`
-   **Endpoint:** /image/prompt/random
-   **Description:** 获取一个随机生成的、高质量的图片提示词，辅助用户生成图片。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (作为“灵感来源”功能)
-   **请求参数 (Query):** (可能包含主题、风格等参数，需确认)
    | 参数名  | 类型     | 是否必需 | 说明                         |
    | :------ | :------- | :------- | :--------------------------- |
    | `count` | `int`    | 否       | 返回数量 (默认: 1)            |
    | `lang`  | `string` | 否       | 提示词语言 (默认: "en")     |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Random prompt(s) retrieved.",
            "data": [ // 即使count为1也建议返回数组
                "A cute cat wearing a wizard hat, photorealistic, high detail"
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 随机提示词获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `count` 或 `lang` 无效。
        *   `503 Service Unavailable`: 提示词服务暂时不可用。
#### 6.3.2. 翻译图片提示词 (Translate Prompt)
-   **Method:** `POST`
-   **Endpoint:** /image/prompt/translate
-   **Description:** 将用户输入的提示词翻译成适合图片生成模型的目标语言 (通常是英文)。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (方便非英文用户使用)
-   **请求参数 (Body - JSON):**
    | 参数名       | 类型     | 是否必需 | 说明                               |
    | :----------- | :------- | :------- | :--------------------------------- |
    | `prompt`     | `string` | 是       | 需要翻译的原始提示词               |
    | `target_lang`| `string` | 否       | 目标语言代码 (如 "en", 默认 "en") |
    | `source_lang`| `string` | 否       | 源语言代码 (可选，可自动检测)       |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Prompt translated successfully.",
            "data": {
                "original_prompt": "一只可爱的猫戴着巫师帽",
                "translated_prompt": "A cute cat wearing a wizard hat",
                "source_language_detected": "zh-CN", // 可选
                "target_language": "en"
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 提示词翻译成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `prompt` 未提供或 `target_lang` 无效。
        *   `422 Unprocessable Entity`: 无法翻译提供的文本。
        *   `503 Service Unavailable`: 翻译服务暂时不可用。
#### 6.3.3. 扩展/优化图片提示词 (Lengthen/Optimize Prompt)
-   **Method:** `POST`
-   **Endpoint:** /image/prompt/lengthen
-   **Description:** 基于用户输入的简短提示词，AI辅助扩展或优化成更详细、更利于生成高质量图片的提示词。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (帮助用户写出更好的Prompt)
-   **请求参数 (Body - JSON):**
    | 参数名  | 类型     | 是否必需 | 说明             |
    | :------ | :------- | :------- | :--------------- |
    | `prompt`| `string` | 是       | 用户输入的原始提示词 |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Prompt optimized successfully.",
            "data": {
                "original_prompt": "cat wizard",
                "optimized_prompt": "A detailed portrait of a mystical cat wearing a tall, pointed wizard hat adorned with glowing runes, intricate patterns on its robe, set against a backdrop of a magical library, dramatic lighting, fantasy art, high detail, sharp focus, by Greg Rutkowski and Alphonse Mucha."
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 提示词优化成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `prompt` 未提供。
        *   `422 Unprocessable Entity`: 无法优化提供的提示词。
        *   `503 Service Unavailable`: 提示词优化服务暂时不可用。
#### 6.3.4. 获取可用图片模型列表
-   **Method:** `GET`
-   **Endpoint:** `/image/model/list`
-   **Description:** 获取图片生成功能所有可用的AI模型列表。
-   **APP端优先级:** P2 (如果允许用户选择模型)
-   **APP端是否需要:** 是 (配合 `/image/generate` 接口的 `model` 参数)
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Image models listed.",
            "data": [
                {
                   "id": "sdxl_v1.0_base",     // 模型唯一ID (用于API调用)
                   "name": "Stable Diffusion XL 1.0", // 模型显示名称
                   "description": "High quality text-to-image model.", // 模型描述
                   "tags": ["general", "photorealistic", "artistic"], // 模型特性标签
                   "preview_url": "https://example.com/model_previews/sdxl.jpg", // 模型效果预览图URL (可选)
                   "type": "text-to-image", // "text-to-image", "image-to-image"
                   "status": "online" // "online", "maintenance", "beta"
                }
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 图片模型列表获取成功。
    -   **错误状态码:**
        *   `503 Service Unavailable`: 模型信息服务暂时不可用。
#### 6.3.5. 获取可用图片风格列表
-   **Method:** `GET`
-   **Endpoint:** `/image/style/list`
-   **Description:** 获取图片生成功能所有可用的风格预设列表。
-   **APP端优先级:** P2 (如果允许用户选择风格)
-   **APP端是否需要:** 是 (配合 `/image/generate` 接口的 `styles` 参数)
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Image styles listed.",
            "data": [
                {
                   "id": "anime_style",      // 风格唯一ID (用于API调用)
                   "name": "Anime Illustration", // 风格显示名称
                   "description": "Generates images in a vibrant anime style.", // 风格描述
                   "tags": ["anime", "illustration", "colorful"], // 风格特性标签
                   "preview_url": "https://example.com/style_previews/anime.jpg", // 风格效果预览图URL
                   "compatible_models": ["sdxl_v1.0_base", "another_model_id"] // (可选) 兼容此风格的模型ID列表
                }
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 图片风格列表获取成功。
    -   **错误状态码:**
        *   `503 Service Unavailable`: 风格信息服务暂时不可用。
#### 6.3.6. 获取可用图片预设列表
-   **Method:** `GET`
-   **Endpoint:** `/image/preset/list`
-   **Description:** 获取图片生成功能所有可用的组合预设列表 (可能包含模型、风格、特定参数等)。
-   **APP端优先级:** P2 (提供快速开始的模板)
-   **APP端是否需要:** 是
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Image presets listed.",
            "data": [
                {
                   "id": "cinematic_portrait_preset", // 预设唯一ID
                   "name": "Cinematic Portrait",      // 预设显示名称
                   "description": "Creates a dramatic, cinematic portrait.", // 预设描述
                   "preview_url": "https://example.com/preset_previews/cinematic.jpg", // 预设效果预览图URL
                   "settings": { // 点击此预设后，应自动填充到生成界面的参数
                        "model": "realistic_vision_v5",
                        "positive_prompt": "close up portrait, cinematic lighting, dramatic",
                        "negative_prompt": "cartoon, ugly, blurry",
                        "styles": {"film_grain_style": 0.5}, // style_id: weight
                        "width": 768,
                        "height": 1024,
                        "steps": 30,
                        "cfg_scale": 7.0
                    }
                }
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 图片预设列表获取成功。
    -   **错误状态码:**
        *   `503 Service Unavailable`: 预设信息服务暂时不可用。
### 6.4. 图片分享 (Image Sharing)

#### 6.4.1. 分享图片 (Share Image)
-   **Method:** `POST`
-   **Endpoint:** `/image/share`
-   **Description:** 用户分享指定的图片 (记录分享事件，并可能返回用于社交分享的链接或预览信息)。
-   **APP端优先级:** P2 (社区分享功能)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型     | 是否必需 | 说明           |
    | :-------- | :------- | :------- | :------------- |
    | `image_id`| `string` | 是       | 要分享的图片ID |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Image share event recorded.",
        "data": {
            "image_url": "https://cdn.example.com/image_to_share.jpg", // 被分享的图片URL (可能带水印或特定尺寸)
            "share_link": "https://app.example.com/image/image_id_example",    // 分享落地页链接
            "suggested_text": "快来看看我生成的图片！" // 分享文案建议
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 图片分享事件记录成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `image_id` 未提供或无效。
    *   `404 Not Found`: 指定 `image_id` 的图片不存在。

---
## 七、语音 (Voice - voice)

本模块包含与语音相关的功能，如文本转语音 (TTS)、用户上传和管理语音样本、以及发现和选用语音。主要服务于角色的语音配置和聊天中的语音播放。对应 `Plan.md` Phase 2+ 的双向语音交流和角色语音定制。

### 7.1. 文本转语音 (Text-to-Speech - TTS)

#### 7.1.1. 合成语音 (Text-to-Speech Synthesis)
-   **Method:** `POST`
-   **Endpoint:** `/voice/tts`
-   **Description:** 将输入的文本转换为语音数据。可以指定一组 `voice_ids` 来混合和试听声音，或者直接使用 `character_id` 来获取该角色配置的声音并进行转换。返回原始音频数据流。此接口合并了原 `/voice/preview` 的试听功能，主要用于角色创建时试听声音、聊天中播放AI的语音回复。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Query for GET, Body - JSON for POST):**
    | 参数名        | 类型     | 是否必需 | 说明                                                                        |
    | :------------ | :------- | :------- | :-------------------------------------------------------------------------- |
    | `text`        | `string` | 是       | 需要转换为语音的文本内容。                                                      |
    | `voice_ids`   | `string` | 否       | 逗号分隔的语音ID列表 (e.g., "id1,id2,id3")。与 `character_id` 二选一。       |
    | `character_id`| `string` | 否       | 角色ID。系统将使用该角色配置的声音进行合成。与 `voice_ids` 二选一。             |
    | `language`    | `string` | 否       | 语言代码 (e.g., "en-US", "zh-CN")，如果 TTS 服务支持多语言且需要明确指定。 |
    | `speed`       | `float`  | 否       | 语速调节 (e.g., 1.0 代表正常语速, 0.8 代表慢速, 1.2 代表快速)。             |
-   **成功状态码与响应 (音频流):**
    *   `200 OK`
        *   **Headers:** `Content-Type: audio/mpeg` (或其他音频格式如 `audio/wav`, `audio/opus`，具体需确认)
        *   **Body:** 原始音频数据的二进制流。
-   **错误状态码 (JSON响应):**
    *   `400 Bad Request`: 请求参数无效或缺失 (例如 `text` 未提供，或 `voice_ids` 和 `character_id` 均未提供)。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `402 Payment Required`: TTS服务需要付费但用户未满足条件。
    *   `404 Not Found`: 指定的 `character_id` 或 `voice_ids` 中的某些ID不存在。
    *   `422 Unprocessable Entity`: 输入的 `text` 无法合成为语音 (例如过长、包含不支持的字符)。
    *   `503 Service Unavailable`: TTS服务暂时不可用。
-   **注意:**
    *   APP端需要能接收和播放此类音频流。
    *   如果文本较长，使用 `POST` 请求更合适。

### 7.2. 语音管理 (Voice Sample Management)
**说明:** 用户可以上传自己的语音样本，或使用他人分享的、平台提供的语音。

#### 7.2.1. 上传新语音样本 (Upload New Voice Sample)
-   **Method:** `POST`
-   **Endpoint:** `/voice/upload`
-   **Description:** 用户上传新的语音样本文件 (原始文档建议3-8秒)。可为语音样本命名并打标签，便于搜索和分类。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (支持用户自定义声音或声音克隆的前置步骤)
-   **请求参数 (Body - `multipart/form-data`):**
    | 参数名 | 类型         | 是否必需 | 说明                                                               |
    | :----- | :----------- | :------- | :----------------------------------------------------------------- |
    | `name` | `string`     | 是       | 语音样本的名称 (e.g., "温柔女声", "我的声音片段1")                      |
    | `tags` | `string`     | 是       | 逗号分隔的标签 (e.g., "female,gentle,custom")                       |
    | `file` | `UploadFile` | 是       | 音频文件 (支持格式如 .wav, .mp3, .m4a，具体支持列表需确认)          |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 201, // HTTP 201 Created
            "message": "Voice uploaded successfully. It may require review before becoming active.",
            "data": { // 新上传的语音对象信息
                "voice_id": "string",           // 语音ID
                "name": "温柔女声",
                "user_id": "string",            // 上传用户的ID
                "tags": ["female", "gentle"],
                "gender_detected": "female",    // (系统检测的性别，可选)
                "status": "pending_review",     // "pending_review", "active", "rejected"
                "created_at": "2025-08-29T11:00:00.000Z",
                "duration_seconds": 5.3,        // 音频时长 (秒)
                "file_format": "mp3"            // 上传文件的格式
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `201 Created`: 语音样本上传成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `name`, `tags`, `file` 未提供或无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `402 Payment Required`: 如果上传或存储声音需要付费。
        *   `413 Payload Too Large`: 上传的音频文件过大。
        *   `415 Unsupported Media Type`: 上传的音频文件格式不被支持。
        *   `422 Unprocessable Entity`: 音频内容不符合要求 (例如过短、过长、静音)。
#### 7.2.2. 修改语音样本信息 (Update Voice Sample Info)
-   **Method:** `PATCH`
-   **Endpoint:** `/voice/{voice_id}`
-   **Description:** 修改已上传语音样本的信息，如名称、标签 (通常仅限上传者或管理员)。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `voice_id`| `string` | 是       | 语音ID     |
-   **请求参数 (Body - JSON):**
    | 参数名     | 类型     | 是否必需 | 说明                       |
    | :--------- | :------- | :------- | :------------------------- |
    | `name`     | `string` | 否       | 新的语音名称               |
    | `tags`     | `string` | 否       | 新的逗号分隔标签列表       |
    | `is_enable`| `boolean`| 否       | 设置启用状态 (可能仅管理员) |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Voice sample info updated successfully.",
        "data": { // 更新后的语音对象，结构同 7.2.1 的 data 字段
            "voice_id": "string",
            "name": "Updated Voice Name",
            "tags": ["female", "gentle", "updated"],
            "status": "active" // 例如管理员审核通过
            // ... 其他字段
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 语音样本信息更新成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 用户无权限修改该语音样本 (例如非上传者或非管理员)。
    *   `404 Not Found`: 指定 `voice_id` 的语音样本不存在。

#### 7.2.3. 删除语音样本 (Delete Voice Sample)
-   **Method:** `DELETE`
-   **Endpoint:** `/voice/{voice_id}`
-   **Description:** 删除指定的语音样本 (通常仅限上传者或管理员)。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `voice_id`| `string` | 是       | 语音ID     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200, // 或 204 No Content
        "message": "Voice deleted successfully.",
        "data": null,
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 语音样本删除成功。
    *   `204 No Content`: 删除成功且无内容返回。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 用户无权限删除该语音样本。
    *   `404 Not Found`: 指定 `voice_id` 的语音样本不存在。
#### 7.2.4. 批量删除语音样本 (Batch Delete Voice Samples)
-   **Method:** `POST`
-   **Endpoint:** `/voice/delete_batch`
-   **Description:** 批量删除指定的语音样本。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (方便用户管理)
-   **请求参数 (Body - JSON):**
    | 参数名     | 类型        | 是否必需 | 说明             |
    | :--------- | :---------- | :------- | :--------------- |
    | `voice_ids`| `List[str]` | 是       | 要删除的语音ID列表 |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Voice samples batch deleted successfully.",
        "data": {
            "deleted_count": 3,
            "failed_ids": ["id_not_found"], // 删除失败的ID列表 (可选)
            "details": [ // (可选) 更详细的删除结果
                {"voice_id": "id1", "status": "deleted"},
                {"voice_id": "id_not_found", "status": "failed", "reason": "not_found"}
            ]
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 批量删除操作完成（即使部分失败）。
-   **错误状态码:**
    *   `400 Bad Request`: `voice_ids` 参数未提供或格式无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 用户无权限删除部分或全部指定的语音样本。
#### 7.2.5. 根据ID获取语音样本详情 (Get Voice Sample by ID)
-   **Method:** `GET`
-   **Endpoint:** `/voice/{voice_id}`
-   **Description:** 获取指定语音ID的详细信息，包括创建者信息。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `voice_id`| `string` | 是       | 语音ID     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Voice details retrieved.",
        "data": { // 语音对象详细信息，结构同 7.2.1 的 data 字段，可能包含更多如音频URL等
            "voice_id": "string",
            "name": "Charming Male Voice",
            "user_id": "string",        // 上传者用户ID
            "user_name": "VoiceCreator123", // 上传者昵称
            "tags": ["male", "charming"],
            "status": "active",
            "created_at": "timestamp",
            "duration_seconds": 7.1,
            "like_count": 150,
            "usage_count": 1200,
            "audio_preview_url": "https://cdn.example.com/voice_preview.mp3" // (可选) 语音预览URL
        },
        "detail": null
    }
-   **成功状态码:**
    *   `200 OK`: 语音样本详情获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 若语音样本是私有的，且非本人查看。
    *   `404 Not Found`: 指定 `voice_id` 的语音样本不存在。

#### 7.2.6. 获取当前用户创建的语音样本 (Get Current User's Created Voices)
-   **Method:** `GET`
-   **Endpoint:** `/voice/self/created`
-   **Description:** 获取当前登录用户上传的所有语音样本列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用户管理自己的声音库)
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Current user's created voices retrieved.",
        "data": [ // 语音对象列表，结构同 7.2.5 的 data 字段 (或 7.2.1 data)
            {
                "voice_id": "string",
                "name": "My Voice Sample 1",
                // ... 其他字段
                "status": "active"
            }
        ],
        "meta": {
            "num": 10,
            "offset": 0,
            "total_items": 3,
            "total_pages": 1
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 获取用户创建的语音样本列表成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

### 7.3. 语音发现 (Voice Discovery)

#### 7.3.1. 搜索语音样本 (Search Voice Samples)
-   **Method:** `GET`
-   **Endpoint:** `/voice/search`
-   **Description:** 根据关键词搜索公开且已启用的语音样本 (匹配名称和标签)。支持分页和排序。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (用户寻找合适的语音)
-   **请求参数 (Query):**
    | 参数名   | 类型     | 是否必需 | 说明                                           |
    | :------- | :------- | :------- | :--------------------------------------------- |
    | `query`  | `string` | 是       | 搜索关键词                                     |
    | `num`    | `int`    | 否       | 返回数量 (默认: 10)                             |
    | `offset` | `int`    | 否       | 分页 (默认: 0)                               |
    | `sort_by`| `string`| 否       | 排序字段 (如 `use`, `like`, `created_at`, 默认: `relevance`) |
    | `order`  | `string`| 否       | 排序方式 (`asc`, `desc`, 默认: `desc`)          |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Voice samples searched successfully.",
        "data": [ // 语音对象列表，结构同 7.2.5 的 data 字段 (或更简略)
            {
                "voice_id": "string",
                "name": "Popular Gentle Voice",
                "user_name": "CommunityContributor",
                "tags": ["female", "gentle", "popular"],
                "like_count": 500,
                "usage_count": 2000,
                "audio_preview_url": "url"
            }
        ],
        "meta": {
            "num": 10,
            "offset": 0,
            "total_items": 25,
            "total_pages": 3,
            "query": "gentle female" // 本次搜索的关键词
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 语音样本搜索成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `query` 未提供或无效，或分页/排序参数无效。

#### 7.3.2. 获取推荐语音样本 (Recommend Voice Samples)
-   **Method:** `GET`
-   **Endpoint:** `/voice/recommend`
-   **Description:** 获取系统推荐的公开语音样本列表 (可能基于用户偏好、标签、热度等)。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (帮助用户发现优质语音)
-   **请求参数 (Query):**
    | 参数名 | 类型     | 是否必需 | 说明                               |
    | :----- | :------- | :------- | :--------------------------------- |
    | `tags` | `string` | 否       | 逗号分隔的标签，用于筛选特定类型的推荐 |
    | `num`  | `int`    | 否       | 返回数量 (默认: 10)                 |
-   **返回示例 (成功):** (返回语音对象列表，结构同 `7.3.1` 的 `data` 列表项，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 推荐语音样本获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `tags` 或 `num` 无效。

#### 7.3.3. 获取精选语音样本 (Get Featured Voice Samples)
-   **Method:** `GET`
-   **Endpoint:** `/voice/featured`
-   **Description:** 获取官方精选或推广的优质语音样本列表。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是 (展示高质量语音)
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回语音对象列表，结构同 `7.3.1` 的 `data` 列表项，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 精选语音样本获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。

#### 7.3.4. 获取指定角色的语音配置 (Get Character's Configured Voices)
-   **Method:** `GET`
-   **Endpoint:** `/voice/character/{character_id}`
-   **Description:** 获取指定AI角色当前配置使用的语音ID和名称列表。
-   **APP端优先级:** P1 (角色详情页展示或编辑角色时)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名         | 类型     | 是否必需 | 说明     |
    | :------------- | :------- | :------- | :------- |
    | `character_id` | `string` | 是       | 角色ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Character voices retrieved.",
            "data": [ // 角色配置的语音列表
                {
                    "voice_id": "voice_id_1",         // 语音ID
                    "name": "Main Voice for Character", // 语音名称
                    "audio_preview_url": "url"       // 语音预览URL
                },
                {
                    "voice_id": "voice_id_2",
                    "name": "Secondary Voice",
                    "audio_preview_url": "url"
                }
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 角色语音配置获取成功。
    -   **错误状态码:**
        *   `404 Not Found`: 指定 `character_id` 的角色不存在。
### 7.4. 其他语音操作 (Other Voice Actions)

#### 7.4.2. 分享语音样本 (Share Voice Sample)
-   **Method:** `POST`
-   **Endpoint:** `/voice/share`
-   **Description:** 用户分享指定的语音样本 (记录分享事件，并可能返回用于社交分享的链接或信息)。
-   **APP端优先级:** P2 (社区分享功能)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名    | 类型     | 是否必需 | 说明             |
    | :-------- | :------- | :------- | :--------------- |
    | `voice_id`| `string` | 是       | 要分享的语音ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Voice share event recorded.",
            "data": {
                "audio_preview_url": "https://cdn.example.com/voice_preview.mp3", // 语音试听片段URL
                "share_link": "https://app.example.com/voice/voice_id_example",      // 分享落地页链接
                "suggested_text": "快来听听这个声音！" // 分享文案建议
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 语音分享事件记录成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `voice_id` 未提供或无效。
        *   `404 Not Found`: 指定 `voice_id` 的语音样本不存在。
---
## 八、草稿 (Draft - draft)

本模块用于管理用户在正式发布角色或故事线之前的草稿内容。这允许用户分步创建和保存进度。

### 8.1. 创建新草稿 (Create New Draft)
-   **Method:** `POST`
-   **Endpoint:** `/draft/create`
-   **Description:** 用户创建一个新的空白草稿，用于后续填充角色或故事线的具体内容。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名      | 类型     | 是否必需 | 说明                                              |
    | :---------- | :------- | :------- | :------------------------------------------------ |
    | `draft_type`| `string` | 是       | 草稿类型: `character` (角色草稿) 或 `story` (故事线草稿) |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 201, // HTTP 201 Created
            "message": "Draft created successfully.",
            "data": {
                "draft_id": "string", // 新创建的草稿ID
                "draft_type": "character", // 创建时传入的类型
                "created_at": "2025-08-30T09:00:00.000Z"
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `201 Created`: 草稿创建成功。
    -   **错误状态码:**
        *   `400 Bad Request`: `draft_type` 未提供或无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
### 8.2. 更新角色草稿 (Update Character Draft)
-   **Method:** `PATCH`
-   **Endpoint:** `/draft/character`
-   **Description:** 更新指定 `draft_id` 的角色草稿内容。如果该 `draft_id` 不存在或类型不匹配，可能会创建新的角色草稿或报错。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - `multipart/form-data`):**
    | 参数名                 | 类型                  | 是否必需 | 说明 (所有字段均为可选，仅传入需要更新的字段)                                                              |
    | :--------------------- | :-------------------- | :------- | :------------------------------------------------------------------------------------------------------ |
    | `draft_id`             | `string`              | 是       | 草稿ID                           |
    | `step`                 | `int`                 | 否       | 当前所在的创建步骤 (可选, 用于前端分步UI)                      |
    | `name`                 | `string`              | 否       | 角色名称                         |
    | `description`          | `string`              | 否       | 角色描述                         |
    | `setting`              | `string`              | 否       | 角色设定                         |
    | `sample_conversations` | `string`              | 否       | 对话示例 (JSON字符串，格式如 `[{"role": "user", "text": "你好"}, {"role": "ai", "text": "你好呀！"}]`) |
    | `voice_ids`            | `string`              | 否       | 逗号分隔的声音ID                 |
    | `gender`               | `string`              | 否       | 性别: `male`, `female`, `other` |
    | `tags`                 | `string`              | 否       | 逗号分隔的标签                   |
    | `memories`             | `List[UploadFile]`    | 否       | 新增的知识文件 (txt, pdf, docx, md)   |
    | `deleted_memories`     | `List[string]`        | 否       | 要删除的已有关联知识文件的ID列表。|
    | `image`                | `UploadFile`          | 否       | 角色立绘图片 (若更新，则替换旧的) |
    | `avatar`               | `UploadFile`          | 否       | 角色头像图片 (若更新，则替换旧的) |
    | `audio_file`           | `UploadFile`          | 否       | 背景音乐文件 (若更新，则替换旧的) |
    | `title`                | `string`              | 否       | (关联故事线的标题 - 如果角色和其首个故事线一起在草稿中创建)                    |
    | `opening`              | `string`              | 否       | (关联故事线的开场白)             |
    | `story_setting`        | `string`              | 否       | (关联故事线的设定)               |
    | `user_bio`             | `string`              | 否       | (关联故事线的用户人设)           |
    | `is_private`           | `boolean`             | 否       | (关联故事线是否私密)             |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Character draft updated successfully.",
            "data": { // 更新后的完整草稿对象，或至少包含 draft_id 和 updated_at
                "draft_id": "string",
                "updated_at": "2025-08-30T12:00:00.000Z",
                "step": 3 // (可选) 更新后的步骤
                // 完整结构参考 GET /draft/{draft_id}
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 角色草稿更新成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数或上传文件无效 (例如 `draft_id` 未提供，或文件类型不支持)。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `403 Forbidden`: 用户无权限修改该草稿 (例如非草稿所有者)。
        *   `404 Not Found`: 指定 `draft_id` 的草稿不存在，或类型不是 `character`。
        *   `413 Payload Too Large`: 上传的文件过大。
        *   `415 Unsupported Media Type`: 上传的文件格式不被支持 (例如 `memories` 文件)。
        *   `422 Unprocessable Entity`: 草稿内容不符合业务逻辑 (例如 `name` 过长)。
    -   **注意:** 已将 `moment_setting` 字段统一为 `story_setting`。
### 8.3. 更新故事线草稿 (Update Story Draft)
-   **Method:** `PATCH`
-   **Endpoint:** `/draft/story` (原始文档为 `/draft/moment`，已根据用户要求改为 `/draft/story`)
-   **Description:** 更新指定 `draft_id` 的故事线草稿内容。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Body - `multipart/form-data`):**
    | 参数名              | 类型                  | 是否必需 | 说明 (所有字段均为可选，仅传入需要更新的字段)                 |
    | :------------------ | :-------------------- | :------- | :------------------------------------------------------ |
    | `draft_id`          | `string`              | 是       | 草稿ID                                   |
    | `step`              | `int`                 | 否       | 当前所在的创建步骤 (可选)                 |
    | `character_id`      | `string`              | 否       | 关联的角色ID (如果故事线草稿是基于某个已存在角色创建的)                   |
    | `title`             | `string`              | 否       | 故事线标题                               |
    | `description`       | `string`              | 否       | 故事线描述                               |
    | `opening`           | `string`              | 否       | 故事线开场白                             |
    | `tags`              | `string`              | 否       | 逗号分隔的标签                           |
    | `setting`           | `string`              | 否       | 故事线特定设定 (已从`moment_setting`更正)                   |
    | `user_bio`          | `string`              | 否       | 用户在该故事线中的人设/简介              |
    | `is_private`        | `boolean`             | 否       | 是否为私密故事线                         |
    | `image`             | `UploadFile`          | 否       | 故事线封面/背景图片 (若更新，则替换旧的)  |
    | `audio_file`        | `UploadFile`          | 否       | 背景音乐文件 (若更新，则替换旧的)         |
-   **返回示例 (成功):** (结构同 `8.2. 更新角色草稿` 的成功响应，包含 `draft_id` 和 `updated_at`)
-   **成功状态码:**
    *   `200 OK`: 故事线草稿更新成功。
-   **错误状态码:** (类似 `8.2`)
    *   `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found` (草稿ID不存在，或类型不是 `story`), `413 Payload Too Large`, `415 Unsupported Media Type`, `422 Unprocessable Entity`.

### 8.4. 获取用户的所有草稿 (Get All User Drafts)
-   **Method:** `GET`
-   **Endpoint:** `/draft/all`
-   **Description:** 获取当前登录用户的所有草稿列表，包括角色草稿和故事线草稿，支持分页。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用户查看和管理自己的草稿箱)
-   **请求参数 (Query):**
    | 参数名     | 类型  | 是否必需 | 说明                      |
    | :--------- | :---- | :------- | :------------------------ |
    | `page`     | `int` | 否       | 页码 (默认: 1 或 0，需确认) |
    | `page_size`| `int` | 否       | 每页数量 (默认: 10 或 20) |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "All drafts retrieved.",
            "data": {
                "drafts": [ // 草稿对象列表 (使用 "drafts" 替代 "result" 更直观)
                    {
                        "draft_id": "string",
                        "draft_type": "character", // "character" 或 "story"
                        "linked_character_id": null, // 如果已关联发布的角色ID
                        "linked_story_id": null,     // 如果已关联发布的故事线ID (针对story草稿)
                        "last_step_completed": 2,    // 上次保存的步骤
                        "created_at": "2025-08-30T10:00:00.000Z",
                        "updated_at": "2025-08-30T11:30:00.000Z",
                        "preview_name": "My WIP Character", // 草稿的预览名称 (可能是角色名或故事线标题)
                        "preview_image_url": "https://cdn.example.com/draft_thumb.png" // 草稿的预览图 (可选)
                        // 详细的 character_draft 或 story_draft 内容通常在获取单个草稿详情时返回，列表接口可返回摘要信息
                    }
                ],
                "pagination": { // 将分页信息聚合到 pagination 对象
                    "total_items": 1,
                    "current_page": 1,
                    "page_size": 10,
                    "total_pages": 1
                }
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 用户草稿列表获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 分页参数 (`page`, `page_size`) 无效。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    -   **注意:** `story_draft` 中的字段名已尽量与 `story` 保持一致，例如使用 `story_setting`。
### 8.5. 获取特定草稿详情 (Get Specific Draft Details)
-   **Method:** `GET`
-   **Endpoint:** `/draft/{draft_id}`
-   **Description:** 根据草稿ID获取单个草稿的详细完整信息。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用户编辑或查看特定草稿时)
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明     |
    | :-------- | :------- | :------- | :------- |
    | `draft_id`| `string` | 是       | 草稿ID   |
-   **返回示例 (成功):** (返回单个完整的草稿对象，结构同 `8.4. 获取用户的所有草稿` 中 `drafts` 数组的单个元素，但包含完整的 `character_draft` 或 `story_draft` 及 `draft_files` 内容)
    ```json
    {
        "code": 200,
        "message": "Draft details retrieved.",
        "data": {
            "draft_id": "string",
            "draft_type": "character",
            // ... 其他元数据 ...
            "character_draft": { /* 完整的角色草稿内容 */ },
            // "story_draft": null, // 如果是character draft
            "draft_files": { /* 关联的文件信息 */ }
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 草稿详情获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期，或无权访问该草稿。
    *   `404 Not Found`: 指定 `draft_id` 的草稿不存在。

### 8.6. 删除草稿 (Delete Draft)
-   **Method:** `DELETE`
-   **Endpoint:** `/draft/{draft_id}`
-   **Description:** 删除用户指定的草稿。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明     |
    | :-------- | :------- | :------- | :------- |
    | `draft_id`| `string` | 是       | 草稿ID   |
-   **返回示例 (成功):**
    ```json
    {
      "code": 200, // 或 204 No Content
      "message": "Draft deleted successfully.",
      "data": null,
      "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 草稿删除成功。
    *   `204 No Content`: 删除成功且无内容返回。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期，或无权删除该草稿。
    *   `404 Not Found`: 指定 `draft_id` 的草稿不存在。
---
## 九、记忆片段 (Memory - memory)

本模块用于管理“记忆片段”，即用户保存的有意义、有趣的对话片段或聊天快照。这些记忆片段可以被用户回顾、管理，也可能被分享。
**产品预留功能：** 未来规划此功能可能扩展为：玩家在聊天过程中，当AI对话产生精彩片段时，系统可能会自动触发生成一张包含场景图和对应内容的图文结合的“记忆片段”，作为一种特殊的记忆保存下来。此自动化触发功能目前为预留设计，当前API主要支持用户手动创建和管理记忆片段。
这与角色创建时上传的“知识文件”(knowledges)是不同概念，也区别于AI的短期/长期对话记忆。

### 9.1. 创建/分享记忆片段 (Create/Share Memory Capsule)
-   **Method:** `POST`
-   **Endpoint:** `/memory/create`
-   **Description:** 用户将当前的某段对话或会话状态创建为一个“记忆片段”。成功后可能返回用于分享的链接或图片。
-   **APP端优先级:** P1 (核心体验，对应 `Plan.md` 的“记忆片段”功能)
-   **APP端是否需要:** 是
-   **请求参数 (Body - JSON):**
    | 参数名         | 类型             | 是否必需 | 说明                                                                               |
    | :------------- | :--------------- | :------- | :--------------------------------------------------------------------------------- |
    | `session_id`   | `string`         | 是       | 要从中创建记忆的会话ID。           |
    | `name`         | `string`         | 否       | 用户为此记忆片段取的名称/标题 (可选, 若无则可由系统基于内容生成)。            |
    | `message_ids`  | `List[string]`   | 否       | 用户选择的特定消息ID列表，用于构成此记忆 (可选，若无则可能保存整个会话或最近N条)。    |
    | `character_id` | `string`         | 是       | 关联的角色ID。                    |
    | `story_id`     | `string`         | 是       | 关联的故事线ID。                  |
-   **返回示例 (成功):**
    ```json
    {
        "code": 201, // HTTP 201 Created
        "message": "Memory capsule created successfully.",
        "data": {
            "memory_id": "string", // 新创建的记忆片段ID
            "name": "My Awesome Memory", // 记忆片段名称
            "session_id": "string",
            "character_id": "string",
            "story_id": "string",
            "created_at": "2025-08-31T10:00:00.000Z",
            "share_url": "https://app.example.com/memory/memory_id_example", // (可选) 分享链接
            "image_url": "https://cdn.example.com/memory_snapshot.png", // (可选) 该记忆片段的可视化图片URL (如聊天截图)
            "cta_text": "来看看这段有趣的对话！" // (可选) 推荐分享文案
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `201 Created`: 记忆片段创建成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `session_id`, `character_id`, `story_id` 未提供或无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `404 Not Found`: 指定的 `session_id`, `character_id` 或 `story_id` 不存在。
    *   `422 Unprocessable Entity`: 无法根据提供的信息创建有效的记忆片段 (例如 `message_ids` 中的消息不存在于该会话)。

### 9.2. 获取当前用户的记忆片段列表 (List Current User's Memory Capsules)
-   **Method:** `GET`
-   **Endpoint:** `/memory/me`
-   **Description:** 获取当前登录用户创建或保存的所有记忆片段列表。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用户查看自己的记忆)
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "User's memories retrieved.",
        "data": [
            {
                "memory_id": "string",            // 记忆片段ID
                "name": "First Encounter with Dragon", // 记忆片段名称/标题
                "user_id": "string",        // 创建用户ID
                "user_name": "string",      // 创建用户名
                "character_id": "string",   // 关联的角色ID
                "character_name": "string", // 关联的角色名
                "character_avatar_version": "url", // 关联角色头像
                "story_id": "string",       // 关联的故事线ID
                "story_title": "string",    // 关联的故事线标题
                "excerpt_message_1": {    // 摘要：其中一条消息 (通常是最后几条中的)
                    "text": "The dragon roared!",
                    "role": "ai"
                },
                "excerpt_message_2": {    // 摘要：另一条消息
                    "text": "I stood my ground.",
                    "role": "user"
                },
                "message_count": 15,        // 该记忆片段包含的消息数量
                "created_at": "2025-08-30T14:00:00.000Z",
                "share_url": "https://app.example.com/memory/mem123", // 分享链接
                "image_url": "https://cdn.example.com/memory_thumb.png", // 记忆片段缩略图/预览图
                "like_count": 10,           // 点赞数 (如果记忆片段可被公开和点赞)
                "is_liked_by_me": false,    // 当前用户是否已点赞
                "is_public": true           // 此记忆是否公开
            }
        ],
        "meta": { // 分页信息
            "num": 10,
            "offset": 0,
            "total_items": 20,
            "total_pages": 2
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 用户记忆片段列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

### 9.3. 获取特定记忆片段详情 (Get Memory Capsule Details)
-   **Method:** `GET`
-   **Endpoint:** `/memory/{memory_id}`
-   **Description:** 根据ID获取单个记忆片段的详细信息，通常包含构成该记忆的完整消息列表。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名     | 类型     | 是否必需 | 说明         |
    | :--------- | :------- | :------- | :----------- |
    | `memory_id`| `string` | 是       | 记忆片段ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Memory details retrieved.",
            "data": { // 结构类似 9.2 的列表项，但包含完整的 messages 列表
                "memory_id": "string",
                "name": "The Dragon's Riddle",
                "user_id": "string",
                "user_name": "string",
                "character_id": "string",
                "character_name": "string",
                "story_id": "string",
                "story_title": "string",
                "created_at": "2025-08-30T15:00:00.000Z",
                "message_count": 5,
                "share_url": "https://app.example.com/memory/mem456",
                "image_url": "https://cdn.example.com/memory_snapshot_large.png",
                "like_count": 5,
                "is_liked_by_me": true,
                "is_public": true,
                "messages": [ // 构成此记忆片段的完整消息列表
                    {"message_id": "msg1", "text": "Dragon: I have a riddle for you, human.", "role": "ai", "created_at": "timestamp"},
                    {"message_id": "msg2", "text": "Me: I'm listening.", "role": "user", "created_at": "timestamp"},
                    {"message_id": "msg3", "text": "Dragon: What has an eye, but cannot see?", "role": "ai", "created_at": "timestamp"},
                    {"message_id": "msg4", "text": "Me: A needle!", "role": "user", "created_at": "timestamp"},
                    {"message_id": "msg5", "text": "Dragon: Clever...", "role": "ai", "created_at": "timestamp"}
                ]
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 记忆片段详情获取成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期，或无权访问该记忆 (如非公开且非本人)。
        *   `404 Not Found`: 指定 `memory_id` 的记忆片段不存在。
### 9.4. 修改记忆片段信息 (Update Memory Capsule)
-   **Method:** `PATCH`
-   **Endpoint:** `/memory/{memory_id}`
-   **Description:** 用户修改已保存的记忆片段的信息，例如重命名。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名     | 类型     | 是否必需 | 说明         |
    | :--------- | :------- | :------- | :----------- |
    | `memory_id`| `string` | 是       | 记忆片段ID   |
-   **请求参数 (Body - JSON):**
    | 参数名 | 类型     | 是否必需 | 说明                                 |
    | :----- | :------- | :------- | :----------------------------------- |
    | `name` | `string` | 否       | 新的记忆片段名称/标题                |
    | `is_public` | `boolean`| 否    | 是否公开此记忆 (如果支持公开分享)   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Memory updated successfully.",
        "data": { // 更新后的记忆片段对象，结构同 GET /memory/{memory_id} 的 data 字段
            "memory_id": "string",
            "name": "The Dragon's Riddle - Solved!",
            "is_public": true
            // ... 其他可能已更新的字段
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 记忆片段更新成功。
-   **错误状态码:**
    *   `400 Bad Request`: 请求参数 `name` 或 `is_public` 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `403 Forbidden`: 用户无权限修改该记忆片段 (例如非创建者)。
    *   `404 Not Found`: 指定 `memory_id` 的记忆片段不存在。

### 9.5. 删除记忆片段 (Delete Memory Capsule)
-   **Method:** `DELETE`
-   **Endpoint:** `/memory/{memory_id}`
-   **Description:** 用户删除自己保存的指定记忆片段。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名     | 类型     | 是否必需 | 说明         |
    | :--------- | :------- | :------- | :----------- |
    | `memory_id`| `string` | 是       | 记忆片段ID   |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200, // 或 204 No Content
        "message": "Memory deleted successfully.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 记忆片段删除成功。
      *   `204 No Content`: 删除成功且无内容返回。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `403 Forbidden`: 用户无权限删除该记忆片段。
      *   `404 Not Found`: 指定 `memory_id` 的记忆片段不存在。
### 9.6. 切换记忆片段点赞状态 (Toggle Memory Capsule Like)
-   **Method:** `POST`
-   **Endpoint:** `/memory/toggle_like/{memory_id}`
-   **Description:** 当前登录用户对公开的记忆片段进行点赞或取消点赞。
-   **APP端优先级:** P2 (社区互动功能，如果记忆片段可公开)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名     | 类型     | 是否必需 | 说明         |
    | :--------- | :------- | :------- | :----------- |
    | `memory_id`| `string` | 是       | 记忆片段ID   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Memory like status toggled.",
            "data": {
                "is_liked_by_me": true, // 操作后当前用户是否为点赞状态
                "like_count": 6      // 最新的点赞总数
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 点赞状态切换成功。
    -   **错误状态码:**
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `404 Not Found`: 指定 `memory_id` 的记忆片段不存在或非公开。
### 9.7. 获取推荐/最新的公开记忆片段 (List Recommended/Latest Public Memories)
-   **Method:** `GET`
-   **Endpoint:** `/memory/latest`
-   **Description:** 获取系统推荐的或按时间排序的最新公开记忆片段列表，用于内容发现。
-   **APP端优先级:** P2 (社区内容发现)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回记忆片段对象列表，结构同 `9.2. 获取当前用户的记忆片段列表` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 获取推荐/最新公开记忆片段列表成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。

### 9.8. 列出某角色的公开记忆片段 (List Character's Public Memories)
-   **Method:** `GET`
-   **Endpoint:** `/memory/character/{character_id}`
-   **Description:** 列出与指定AI角色相关联的所有公开记忆片段。
-   **APP端优先级:** P2 (在角色详情页展示相关精彩对话片段)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名         | 类型     | 是否必需 | 说明     |
    | :------------- | :------- | :------- | :------- |
    | `character_id` | `string` | 是       | 角色ID   |
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):** (返回记忆片段对象列表，结构同 `9.2. 获取当前用户的记忆片段列表` 的成功响应，包含 `meta` 分页信息)
-   **成功状态码:**
    *   `200 OK`: 获取角色公开记忆片段列表成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `404 Not Found`: 指定 `character_id` 的角色不存在。

### 9.9. 列出用户的所有记忆片段 (简略信息) - (可能与 /memory/me 重叠)
-   **Method:** `GET`
-   **Endpoint:** `/memory/all`
-   **Description:** (原始文档描述为“列出当前用户的所有记忆条目的基本信息”) 如果 `/memory/me` 返回的是详细列表，此接口可能返回更简略的信息或有不同用途。若功能重叠，可考虑合并或移除。
-   **APP端优先级:** P1 (若与 `/memory/me` 不同且有必要) / P3 (若重叠)
-   **APP端是否需要:** 待定 (需与 `/memory/me` 区分其必要性)
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 10)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功 - 假设为简略信息):**
    ```json
    {
        "code": 200,
        "message": "All user memories (basic) retrieved.",
        "data": [
            {
                "memory_id": "string", // Memory ID
                "name": "Quick Snippet about Dragon", // Memory name
                "created_at": "2025-08-30T14:00:00.000Z",
                "character_id": "string",
                "story_id": "string",
                "preview_image_url": "https://cdn.example.com/mem_thumb.png" // (可选) 缩略图
            }
        ],
        "meta": { // 分页信息
            "num": 10,
            "offset": 0,
            "total_items": 50,
            "total_pages": 5
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 用户所有记忆片段的简略信息列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
-   **注意:** 此接口 `/memory/all` 的具体用途和返回与 `/memory/me` 的差异需要明确。如果 APP 端 `/memory/me` 已经满足需求，此接口可能对 APP 无用。

---
## 十、通知 (Notification - notification)

本模块用于管理和展示给用户的应用内通知，如系统消息、社区互动提醒等。

### 10.1. 获取通知列表 (Get Notifications)
-   **Method:** `GET`
-   **Endpoint:** `/notification`
-   **Description:** 获取当前登录用户的通知列表，支持分页和筛选未读。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名       | 类型     | 是否必需 | 说明                                  |
    | :----------- | :------- | :------- | :------------------------------------ |
    | `num`        | `int`    | 否       | 返回数量 (默认: 20)                    |
    | `offset`     | `int`    | 否       | 偏移量 (默认: 0)                      |
    | `unread_only`| `boolean`| 否       | 是否只返回未读通知 (默认: `false`)     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Notifications retrieved successfully.",
        "data": [
            {
                "notification_id": "string", // 通知的唯一ID (使用 notification_id)
                "recipient_user_id": "string", // 接收通知的用户ID (即当前用户)
                "sender_user_id": "string", // 发送通知的用户ID (若是系统通知，则为特定系统ID或null)
                "sender_user_name": "string", // 发送者昵称 (可选)
                "sender_avatar_url": "url", // 发送者头像
                "notification_type": "comment_reply", // 通知类型
                "title": "您有一条新回复",    // 通知标题
                "content": "用户 [UserX] 回复了您的评论: “说得好！”", // 通知详细内容
                "is_read": false,         // 是否已读
                "created_at": "2025-09-02T10:00:00.000Z", // 通知创建时间
                "target_info": {        // 关联的目标信息 (可选)
                    "target_type": "comment", // "comment", "user", "story", "character", "post" 等
                    "target_id": "string",    // 目标对象的ID
                    "target_url": "app://story/story_id_abc/comment/comment_id_xyz", // APP内跳转DeepLink (可选)
                    "target_summary": "在《龙之谷》中的评论" // 目标内容的简要描述 (可选)
                }
            }
        ],
        "meta": { // 分页信息
            "num": 20,
            "offset": 0,
            "total_items": 55, // 总通知数 (或未读通知数，根据 unread_only)
            "unread_count": 5 // (可选) 明确返回未读通知总数
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 通知列表获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 或过滤参数 (`unread_only`) 无效。
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。

### 10.2. 标记所有通知为已读 (Mark All Notifications as Read)
-   **Method:** `POST`
-   **Endpoint:** `/notification/read-all`
-   **Description:** 将当前登录用户的所有（或所有未读）通知标记为已读状态。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "All notifications marked as read.",
        "data": {
            "marked_count": 15 // 成功标记为已读的通知数量
        },
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 标记所有通知为已读操作成功。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
### 10.3. 标记单个通知为已读 (Mark Notification as Read)
-   **Method:** `POST`
-   **Endpoint:** `/notification/read/{not_id}`
-   **Description:** 将指定的单个通知标记为已读状态。
-   **APP端优先级:** P2
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名  | 类型     | 是否必需 | 说明       |
    | :------ | :------- | :------- | :--------- |
    | `not_id`| `string` | 是       | 通知ID     |
-   **返回示例 (成功):**
    ```json
    {
      {
        "code": 200,
        "message": "Notification marked as read.",
        "data": null,
        "detail": null
      }
      ```
  -   **成功状态码:**
      *   `200 OK`: 标记单个通知为已读成功。
  -   **错误状态码:**
      *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
      *   `404 Not Found`: 指定 `not_id` 的通知不存在，或不属于当前用户。
---

## 十一、标签 (Tag - tag)

本模块用于管理和获取内容的标签，标签可用于角色、故事线、语音样本等的分类和发现。

### 11.1. 获取推荐标签 (Get Recommended Tags)
-   **Method:** `GET`
-   **Endpoint:** `/tag/recommend/{type}`
-   **Description:** 根据指定的类型（如角色、故事线）获取推荐的热门或常用标签列表。
-   **APP端优先级:** P1 (在创建或编辑内容时提供标签建议)
-   **APP端是否需要:** 是
-   **请求参数 (Path):**
    | 参数名 | 类型     | 是否必需 | 说明                                                  |
    | :----- | :------- | :------- | :---------------------------------------------------- |
    | `type` | `string` | 是       | 标签类型: `story`, `character`, `voice` (原文档还有`stage`) |
-   **请求参数 (Query):**
    | 参数名 | 类型  | 是否必需 | 说明                 |
    | :----- | :---- | :------- | :------------------- |
    | `num`  | `int` | 否       | 推荐数量 (默认: 10)   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Recommended tags retrieved.",
            "data": [ // 推荐的标签字符串列表
                "Adventure",
                "Fantasy",
                "Sci-Fi",
                "Romance"
            ],
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 推荐标签获取成功。
    -   **错误状态码:**
        *   `400 Bad Request`: `type` 参数无效，或 `num` 参数无效。
### 11.2. 搜索标签 (Search Tags)
-   **Method:** `GET`
-   **Endpoint:** `/tag/search` (原始文档还有一个 `/tag/search/{type}`，建议统一为此接口并通过query参数区分类型)
-   **Description:** 根据类型和关键词搜索已存在的标签。
-   **APP端优先级:** P1 (用户在输入标签时进行搜索和自动补全)
-   **APP端是否需要:** 是
-   **请求参数 (Query):**
    | 参数名  | 类型     | 是否必需 | 说明                                                  |
    | :------ | :------- | :------- | :---------------------------------------------------- |
    | `type`  | `string` | 是       | 标签类型: `story`, `character`, `voice`               |
    | `query` | `string` | 是       | 搜索的标签关键词 (部分匹配)                             |
    | `num`   | `int`    | 否       | 返回数量 (默认: 10)                                    |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Tags searched successfully.",
            "data": [ // 匹配的标签字符串列表
                "Science Fiction",
                "Sci-Fi Adventure"
            ],
            "meta": {
                "query": "Sci-Fi", // 本次搜索的关键词
                "type": "story",   // 本次搜索的类型
                "num_found": 2     // 找到的数量
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 标签搜索成功。
    -   **错误状态码:**
        *   `400 Bad Request`: `query` 或 `type` 参数未提供或无效。
### 11.3. 获取所有标签 (Get All Tags by Type or Language)
-   **Method:** `GET`
-   **Endpoint:** `/tag/all`
-   **Description:** 获取系统中某一类型或某一语言下的所有可用标签列表。
-   **APP端优先级:** P2 (用于标签云展示或高级筛选)
-   **APP端是否需要:** 是 (特定场景下可能需要)
-   **请求参数 (Query):**
    | 参数名    | 类型     | 是否必需 | 说明                                                              |
    | :-------- | :------- | :------- | :---------------------------------------------------------------- |
    | `type`    | `string` | 否       | 标签类型: `story`, `character`, `voice` (若不传，则可能返回所有类型标签) |
    | `language`| `string` | 否       | 语言代码 (e.g., "en", "zh-CN")。默认为用户当前设置的语言或系统默认语言。 |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "All tags retrieved.",
            "data": [ // 所有符合条件的标签字符串列表
                "Adventure", "Fantasy", "Mystery", "Comedy", "Drama", "Horror", "Sci-Fi", "Romance", "Historical", "Slice of Life"
                // ... 可能还有更多
            ],
            "meta": {
                "type": "story", // (如果指定了类型)
                "language": "en" // (如果指定了语言)
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 获取所有标签成功。
    -   **错误状态码:**
        *   `400 Bad Request`: `type` 或 `language` 参数无效。
### 11.4. 获取主页展示标签 (Get Main Page Tags)
-   **Method:** `GET`
-   **Endpoint:** `/tag/mainpage`
-   **Description:** 获取一组用于应用主页或发现页上进行快速导航或展示的热门/精选标签。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是 (用于首页引导用户发现内容)
-   **请求参数 (Query):**
    | 参数名 | 类型  | 是否必需 | 说明                 |
    | :----- | :---- | :------- | :------------------- |
    | `num`  | `int` | 否       | 返回数量 (默认: 10)   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Main page tags retrieved.",
        "data": [ // 主页展示的标签字符串列表
            "热门推荐", "本周最火", "奇幻冒险", "都市日常", "AI女友"
        ],
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 主页标签获取成功。
-   **错误状态码:**
    *   `400 Bad Request`: `num` 参数无效。
---
## 十二、兑换码 (Redeem - redeem)

本模块用于处理兑换码功能。

### 12.1. 兑换兑换码 (Redeem Code)
-   **Method:** `POST`
-   **Endpoint:** `/redeem/{code}`
-   **Description:** 用户输入兑换码以获取相应的奖励或权益。
-   **APP端优先级:** P3 (运营活动功能)
-   **APP端是否需要:** 是 (如果APP支持兑换码活动)
-   **请求参数 (Path):**
    | 参数名 | 类型     | 是否必需 | 说明     |
    | :----- | :------- | :------- | :------- |
    | `code` | `string` | 是       | 兑换码   |
-   **返回示例 (成功):**
    ```json
    {
        {
            "code": 200,
            "message": "Redemption successful!",
            "data": { // 返回兑换到的奖励详情
                "reward_type": "Alphane", // "Alphane", "subscription_days", "item_id", "feature_unlock"
                "reward_amount": 100,  // 如果是数量型奖励
                "reward_description": "You've received 100 Rubies!" // 奖励描述
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK`: 兑换成功。
    -   **错误状态码 (使用标准HTTP状态码，并可在 detail 中提供业务错误码):**
        *   `400 Bad Request`: 兑换码格式错误。
        *   `401 Unauthorized`: 用户未登录。
        *   `403 Forbidden`: 用户不满足兑换条件 (例如等级不够、活动限制)。
        *   `404 Not Found`: 兑换码不存在。
        *   `409 Conflict`: 兑换码已被使用。
        *   `410 Gone`: 兑换码已过期。
        *   `429 Too Many Requests`: 用户尝试兑换过于频繁。
---

## 十四、支付 (Payment - payment)

本模块处理与支付相关的操作，如购买虚拟货币或订阅服务。APP端通常通过SDK或跳转到第三方支付页面完成支付，这些API更多是服务器端与支付平台的交互。

### 14.1. 获取可购买的订阅产品列表 (Get Purchasable Subscription Products)
-   **Method:** `GET`
-   **Endpoint:** `/payment/subscription-products`
-   **Description:** 获取所有可购买的订阅产品列表（如小月卡、大月卡详情及价格）。实际购买通过 `/payment/checkout` 发起。
-   **APP端优先级:** P1
-   **APP端是否需要:** 是
-   **请求参数:** 无
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Subscription products retrieved successfully.",
        "data": [
            {
                "product_id": "alphane_pass_monthly",
                "name": "Alphane Pass (小月卡)",
                "description": "解锁每日快速响应次数、每日代币奖励、进阶战令等特权。",
                "price": "9.99",
                "currency": "usd",
                "billing_cycle": "monthly",
                "features": ["每日15次Fast Req+无限使用的Slow Req", "每日50曦光微尘", "每日10心悦晶石", "解锁战令进阶轨道"]
            },
            {
                "product_id": "alphane_diamond_monthly",
                "name": "Alphane Diamond (大月卡)",
                "description": "包含小月卡所有权益，并提供更多的快速响应、更丰厚每日奖励、尊享密语空间等顶级特权。",
                "price": "29.99",
                "currency": "USD",
                "billing_cycle": "monthly",
                "features": ["每日60次Fast Req+无限使用的Slow Req", "每日150曦光微尘", "每日50心悦晶石", "每日1忆境拼图碎片", "尊享密语空间访问权", "AI记忆容量提升", "可以获得创作者激励"]
            }
        ],
        "detail": null
    }
    ```

### 14.2. 发起结账请求 (Checkout)
-   **Method:** `POST`
-   **Endpoint:** `/payment/checkout_card`
-   **Description:** 用户选择商品或订阅后，客户端向服务器发起结账请求，服务器返回处理支付所需的信息（如支付网关的订单ID、客户端秘钥或跳转URL）。
-   **APP端优先级:** P2 (核心商业化流程)
-   **APP端是否需要:** 是 (APP需要调用此接口获取支付凭证或参数)
-   **请求参数 (Body - JSON):** (具体参数需根据实际支付流程确定)
    | 参数名             | 类型     | 是否必需 | 说明                                                                |
    | :----------------- | :------- | :------- | :------------------------------------------------------------------ |
    | `product_id`       | `string` | 是       | 要购买的商品ID或订阅计划ID (e.g., "Alphane_pack_100", "plus_monthly_sub") |
    | `quantity`         | `int`    | 否       | 购买数量 (默认为1，主要用于可堆叠商品)                                 |
    | `payment_method_type`| `string` | 否       | 期望的支付方式 (如 "alipay", "wechatpay", "apple_iap", "google_play_billing", "card")，服务器可根据此选择合适的支付渠道 |
    | `redirect_urls`    | `object` | 否       | 支付完成后的跳转URL (success_url, cancel_url)，主要用于Web支付场景     |
-   **返回示例 (成功 - 以需要客户端进一步操作的Stripe为例):**
    ```json
    {
        {
            "code": 200, // 或 201 Created 如果服务器创建了支付订单记录
            "message": "Checkout initiated. Please proceed with payment.",
            "data": {
                "provider": "stripe", // 使用的支付服务商
                "transaction_id": "pi_123abc...", // 支付意图ID (Payment Intent ID)
                "client_secret": "pi_123abc_secret_xyz...", // 客户端用以确认支付的秘钥
                "publishable_key": "pk_live_...", // Stripe的Publishable Key
                "ephemeral_key_secret": "ek_live_..." // (若使用Stripe Customer Session)
                // 对于Apple IAP 或 Google Play Billing，这里可能包含产品ID和服务器生成的订单ID等信息
            },
            "detail": null
        }
        ```
    -   **返回示例 (成功 - 跳转URL模式):**
        ```json
        {
            "code": 200, // 或 201 Created
            "message": "Checkout initiated. Please redirect to payment page.",
            "data": {
                "provider": "alipay_web",
                "checkout_url": "https://alipay.com/pay?order_id=...", // 跳转到支付平台的URL
                "internal_order_id": "server_order_id_123" // 服务器内部订单号
            },
-   **成功状态码:**
    *   `200 OK`: 订阅产品列表获取成功。
-   **错误状态码:**
    *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
    *   `503 Service Unavailable`: 产品信息服务暂时不可用。
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `200 OK` / `201 Created`: 结账请求处理成功，返回支付所需参数。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `product_id` 或 `payment_method_type` 无效。
        *   `401 Unauthorized`: 用户未登录。
        *   `402 Payment Required`: (可能不在此阶段返回，支付失败由后续回调或查询处理)。
        *   `404 Not Found`: `product_id` 对应的商品或订阅不存在。
        *   `422 Unprocessable Entity`: 无法处理该购买请求 (例如库存不足、区域限制)。
        *   `503 Service Unavailable`: 支付网关或相关服务暂时不可用。
### 14.3. 支付回调通知 (Payment Notification Callback)
-   **Method:** `POST` (通常为 `POST`)
-   **Endpoint:** `/payment/notify` (或特定支付渠道的专用回调路径如 `/payment/notify/alipay`)
-   **Description:** 此接口由第三方支付平台在用户支付完成后异步调用，用于通知我方服务器支付结果。**APP端不直接调用此接口。**
-   **APP端优先级:** APP无用
-   **APP端是否需要:** 否
-   **请求参数:** (参数结构和内容完全取决于第三方支付平台，如支付宝、微信支付、Stripe Webhook等，包含订单号、金额、支付状态、签名等)
-   **成功响应给支付平台:** 通常为特定字符串 (如 "success", "SUCCESS", "OK") 或 HTTP 200状态码。

---

## 十五、动画 (Animate - animate)

本模块用于将静态图片（如故事线封面）转换为简单动画。

### 15.1. 提交图片动画任务 (Animate Story Image)
-   **Method:** `POST`
-   **Endpoint:** `/animate/{story_id}`
-   **Description:** 为指定故事线（Story）的封面图片提交一个动画生成任务。异步处理，返回任务ID。
-   **APP端优先级:** P3 (视觉增强功能，非核心)
-   **APP端是否需要:** 是 (如果产品包含此功能)
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `story_id`| `string` | 是       | 故事线ID   |
-   **请求参数 (Body - JSON):** (可选参数，具体支持的动画类型和参数需定义)
    | 参数名           | 类型     | 是否必需 | 说明                                     |
    | :--------------- | :------- | :------- | :--------------------------------------- |
    | `animation_style`| `string` | 否       | 动画风格ID (e.g., "gentle_breathing", "eye_blink", "parallax_scroll") |
    | `intensity`      | `float`  | 否       | 动画强度 (0.0 - 1.0)                      |
-   **返回示例 (成功提交任务):**
    ```json
    {
        {
            "code": 202, // HTTP 202 Accepted
            "message": "Animation task submitted.",
            "data": {
                "task_id": "string" // 动画任务ID，用于查询状态
            },
            "detail": null
        }
        ```
    -   **成功状态码:**
        *   `202 Accepted`: 动画任务提交成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数 `animation_style` 或 `intensity` 无效。
        *   `401 Unauthorized`: 用户未登录。
        *   `404 Not Found`: 指定 `story_id` 的故事线不存在，或没有可动画化的图片。
        *   `429 Too Many Requests`: 用户提交动画任务过于频繁。
        *   `503 Service Unavailable`: 动画服务暂时不可用。
### 15.2. 获取动画任务状态与结果 (Get Animation Task Status)
-   **Method:** `GET`
-   **Endpoint:** `/animate/{task_id}`
-   **Description:** 根据任务ID查询图片动画任务的当前状态和结果 (如生成的动画文件URL)。
-   **APP端优先级:** P3
-   **APP端是否需要:** 是 (配合异步任务)
-   **请求参数 (Path):**
    | 参数名    | 类型     | 是否必需 | 说明       |
    | :-------- | :------- | :------- | :--------- |
    | `task_id` | `string` | 是       | 动画任务ID |
-   **返回示例 (成功 - 任务完成):**
    ```json
    {
        "code": 200,
        "message": "Animation task completed.",
        "data": {
            "task_id": "string",
            "status": "completed", // "pending", "processing", "completed", "failed"
            "result_url": "https://cdn.example.com/animated_story.gif", // 动画结果URL (gif, mp4等)
            "thumbnail_url": "https://cdn.example.com/animated_story_thumb.jpg" // 动画预览缩略图 (可选)
        },
        "detail": null
    }
    ```
-   **返回示例 (成功 - 任务处理中):**
    ```json
    {
        "code": 200,
        "message": "Animation task is processing.",
        "data": {
            "task_id": "string",
            "status": "processing",
            "progress": 0.75 // 处理进度 (0.0 - 1.0, 可选)
        },
        "detail": null
    }
    ```
-   **返回示例 (成功 - 任务失败):**
    ```json
    {
        "code": 200,
        "message": "Animation task failed.",
        "data": {
            "task_id": "string",
            "status": "failed",
            "error_message": "Failed to process image for animation." // 失败原因
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 查询成功，`status` 字段指示任务状态。
-   **错误状态码:**
    *   `404 Not Found`: 指定 `task_id` 的任务不存在。
### 15.3. 动画回调通知 (Animation Callback)
-   **Method:** `POST`
-   **Endpoint:** `/animate/callback`
-   **Description:** 此接口由后台动画处理服务在任务完成或失败时调用，用于通知主服务器。**APP端不直接调用此接口。**
-   **APP端优先级:** APP无用
-   **APP端是否需要:** 否

---

## 十六、抽卡 (Gacha - gacha)

本模块用于实现游戏化的抽卡功能，可能用于获取角色图片、道具或其他虚拟物品。根据用户反馈，此为次要功能。

### 16.1. 单次抽卡 (Single Gacha Roll)
-   **Method:** `POST`
-   **Endpoint:** `/gacha/single_roll`
-   **Description:** 用户执行一次单抽操作。
-   **APP端优先级:** P3
-   **APP端是否需要:** 是 (如果产品包含此功能)
-   **请求参数 (Body - JSON):** (具体参数需根据抽卡系统设计确定)
    | 参数名         | 类型     | 是否必需 | 说明                                                |
    | :------------- | :------- | :------- | :-------------------------------------------------- |
    | `pool_id`      | `string` | 是       | 抽卡池ID (不同的卡池可能有不同的物品和概率)           |
    | `character_id` | `string` | 否       | 如果是针对特定角色的抽卡 (如获取该角色的不同形象)   |
    | `cost_type`    | `string` | 否       | 消耗类型 (如 "Alphane", "free_ticket", "Endora" 默认可能为Alphane) |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Gacha roll successful!",
        "data": {
            "gacha_event_id": "string",   // 本次抽卡事件的ID
            "items_received": [         // 抽中的物品列表 (单抽也用列表)
                {
                    "item_type": "character_image", // "character_image", "item", "currency"
                    "item_id": "image_id_xyz",    // 物品的具体ID (如图片ID)
                    "name": "稀有角色皮肤A",
                    "rarity": "SSR",
                    "image_url": "https://cdn.example.com/gacha_ssr_image.png"
                }
            ],
            "remaining_balance": 900 // 用户剩余货币 (可选)
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 抽卡成功。
-   **错误状态码:**
    *   `400 Bad Request`: `pool_id` 无效或 `cost_type` 不支持。
    *   `401 Unauthorized`: 用户未登录。
    *   `402 Payment Required`: 用户货币/抽卡券不足。
    *   `404 Not Found`: `pool_id` 或 `character_id` (如果提供) 不存在。
    *   `429 Too Many Requests`: 抽卡过于频繁。
    *   `503 Service Unavailable`: 抽卡服务暂时不可用。

### 16.2. 重新抽卡 (Reroll Single Gacha)
-   **Method:** `POST`
-   **Endpoint:** `/gacha/reroll_single`
-   **Description:** 用户对上一次不满意的抽卡结果进行重新抽取 (通常有次数或道具限制)。
-   **APP端优先级:** P3
-   **APP端是否需要:** 是 (如果支持重抽机制)
-   **请求参数 (Body - JSON):**
    | 参数名           | 类型     | 是否必需 | 说明                                   |
    | :--------------- | :------- | :------- | :------------------------------------- |
    | `gacha_event_id` | `string` | 是       | 需要重抽的上一次抽卡事件ID             |
    | `reroll_cost_type`| `string`| 否       | 重抽消耗类型 (如 "reroll_ticket", "Alphane") |
-   **返回示例 (成功):** (结构同 `16.1. 单次抽卡` 的成功响应)
-   **成功状态码:**
    *   `200 OK`: 重抽成功。
-   **错误状态码:**
    *   `400 Bad Request`: `gacha_event_id` 无效，或已达到最大重抽次数，或缺少重抽道具/货币。
    *   `401 Unauthorized`: 用户未登录。
    *   `404 Not Found`: `gacha_event_id` 对应的抽卡事件不存在或不属于当前用户。

### 16.3. 获取用户所有抽卡记录 (Get User's All Gacha Results)
-   **Method:** `GET`
-   **Endpoint:** `/gacha/history` (更名以区分)
-   **Description:** 获取当前登录用户的所有抽卡历史记录。
-   **APP端优先级:** P3
-   **APP端是否需要:** 是 (用户查看历史)
-   **请求参数 (Query):**
    | 参数名  | 类型  | 是否必需 | 说明                 |
    | :------ | :---- | :------- | :------------------- |
    | `num`   | `int` | 否       | 返回数量 (默认: 20)   |
    | `offset`| `int` | 否       | 偏移量 (默认: 0)     |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Gacha history retrieved.",
        "data": [ // 抽卡记录列表
            {
                "gacha_event_id": "string",
                "created_at": "2025-09-03T12:00:00.000Z",
                "character_id": "string", // 如果是角色相关抽卡
                "pool_id": "string",      // 所属卡池ID
                "pool_name": "string",    // 卡池名称
                "items_received": [ /* ... 同16.1 items_received 结构 ... */ ]
            }
        ],
        "meta": { // 分页信息
            "num": 20,
            "offset": 0,
            "total_items": 150,
            "total_pages": 8
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 获取抽卡历史成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数 (`num`, `offset`) 无效。
    *   `401 Unauthorized`: 用户未登录。

### 16.4. 获取用户对特定角色的抽卡记录 (Get User's Gacha Results for a Character)
-   **Method:** `GET`
-   **Endpoint:** `/gacha/history/character/{character_id}` (更名以区分)
-   **Description:** 获取当前登录用户针对特定AI角色的所有抽卡记录。
-   **APP端优先级:** P3
-   **APP端是否需要:** 是 (如果抽卡与角色强相关)
-   **请求参数 (Path):**
    | 参数名         | 类型     | 是否必需 | 说明     |
    | :------------- | :------- | :------- | :------- |
    | `character_id` | `string` | 是       | 角色ID   |
-   **请求参数 (Query):** (同 `16.3`)
-   **返回示例 (成功):** (结构同 `16.3` 的成功响应，但列表项仅包含与指定 `character_id` 相关的记录)
-   **成功状态码:**
    *   `200 OK`: 获取角色抽卡历史成功。
-   **错误状态码:**
    *   `400 Bad Request`: 分页参数无效。
    *   `401 Unauthorized`: 用户未登录。
    *   `404 Not Found`: 指定 `character_id` 的角色不存在。

---

## 十七、服务状态 (Uptime - uptime)

本模块用于检查服务或模型的在线状态，主要为运维监控使用。

### 17.1. 获取模型在线状态 (Get Model Uptime/Status)
-   **Method:** `GET`
-   **Endpoint:** `/uptime/model/{model_name}`
-   **Description:** 获取指定AI模型的在线状态或最近正常运行时间。
-   **APP端优先级:** APP无用
-   **APP端是否需要:** 否 (APP端通常不需要关心此信息)
-   **请求参数 (Path):**
    | 参数名      | 类型     | 是否必需 | 说明             |
    | :---------- | :------- | :------- | :--------------- |
    | `model_name`| `string` | 是       | 模型的名称或ID   |
-   **返回示例 (成功):**
    ```json
    {
        "code": 200,
        "message": "Model status retrieved.",
        "data": {
            "model_name": "gpt-4-turbo",
            "status": "online", // "online", "offline", "maintenance"
            "last_checked_at": "2025-09-03T15:00:00.000Z",
            "uptime_percentage_24h": 0.999 // 过去24小时可用率 (可选)
        },
        "detail": null
    }
    ```
-   **成功状态码:**
    *   `200 OK`: 模型状态获取成功。
-   **错误状态码:**
    *   `404 Not Found`: 指定 `model_name` 的模型不存在。
---
---