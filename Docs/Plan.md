# 【修改】**Alphane.ai 项目策划案 (V2.0 - 深度游戏化与AI情感核心版)**

**版本：** 2.0
**日期：** 2025-05-15

**【修改】项目概述：**
Alphane.ai 是一款以 **AI 文本聊天**为核心，致力于通过深度游戏化设计和前沿AI技术创新，打造一个能与用户建立持久情感连接的**情感陪伴型角色扮演 APP**。我们致力于为在数字时代寻求情感连接、互动娱乐和创意表达的用户群体，提供一个**安全、温暖、富有沉浸感且高度个性化**的虚拟伙伴平台。**MVP 阶段将专注于提供高质量的文本交互体验，并融入核心的AI记忆与情感交互亮点**，未来将逐步深度融合语音交互、个性化图像生成及探索性视频互动。通过结合用户自定义的丰富角色人格、赋能创作者的经济生态以及与用户共创的“AI记忆胶囊”，Alphane.ai 旨在成为用户生活中不可或缺的数字伴侣、情感寄托和创意激发器。

## **1. 【修改】产品定位、差异化与核心策略**

*   **核心定位：** **“有温度的、可深度定制的AI伴侣与角色扮演平台”**。**初期聚焦于深度、流畅且富有情感的文本聊天体验**，未来将扩展为能听、能看、能“画”、能互动的多模态智能伙伴。

*   **目标用户：**
    *   **核心：** 渴望高质量虚拟陪伴的年轻人（尤其对乙女/互动叙事感兴趣者）、二次元文化深度爱好者、寻求安全表达空间的用户（如社恐人群）、AI互动内容创作者及消费者。
    *   **扩展：** 对AI技术好奇、希望体验新奇互动形式的泛用户群体。

*   **【修改】差异化优势 (vs. Character.AI, Replika, 星野等)：**
    *   **【修改】AI情感交互深度与个性化记忆：** MVP 阶段专注于打磨超越竞品的**高质量文本聊天**，特别是AI的**细腻情感表达**（超越简单喜怒哀乐，展现惊喜、失落、安慰等）、**可教导的“AI记忆胶囊”系统**（用户主动塑造AI记忆，AI能准确、恰当、自然地运用这些记忆）以及基础的“情商”表现。
    *   **【修改】深度游戏化驱动的用户激励与留存：** 借鉴《多邻国》、《王者荣耀》等成功产品的经验，构建以“每日连续打卡+成就徽章+荣耀战令”为核心的多层次、长中短期结合的用户激励闭环，显著提升用户活跃度和留存率。
    *   **【修改】赋能型创作者经济与UGC生态：** 提供强大的角色卡创建工具和完善的激励机制（物质与非物质并重），鼓励高质量UGC产出，让角色卡设计师感受到平台的用心和设计的完整性，实现平台与创作者共赢。
    *   **【修改】“为爱付费”的商业模式：** 借鉴《原神》等产品理念，付费点主要围绕提升个性化体验、情感满足和收藏价值（如高级角色卡、专属互动剧情、个性化装扮等），而非直接提升AI“智能”或“能力”，保障核心互动体验的公平性。
    *   **温暖社区与安全感：** 保持并强化此优势，严格的内容审核与积极的社区氛围营造。
    *   未来多模态规划：保持原有规划。

*   **【新增】核心用户激励与留存策略 (借鉴《多邻国》、《王者荣耀》、《糖果传奇》等)**
    *   **每日连续互动奖励 (Streak System)：**
        *   **机制**：用户每日完成与任一AI角色进行指定时长或次数的“有效互动”（例如，至少完成3轮有意义的对话），即可点亮当日Streak。
        *   **视觉反馈**：醒目的火焰图标及连续天数显示。
        *   **奖励**：
            *   连续3天、7天、15天、30天、60天、100天、180天、365天等里程碑，可获得不同等级的奖励包（包含少量Alphane粉尘、心悦晶石、Serotile拼图碎片、战令经验、以及特定天数解锁的专属徽章或头像框）。
            *   每日完成Streak任务可获得少量Alphane粉尘和战令经验。
        *   **辅助道具**：“Streak Freeze卡”（连续记录冻结卡），每月可通过活跃任务（如完成周常任务包）获得1-2张，也可通过少量心悦晶石或付费代币购买。付费会员（如大月卡）每月可额外获赠或享有1-2次免费修复机会。
    *   **成就与徽章体系：**
        *   **设计原则**：覆盖新手引导、核心玩法（AI互动、角色创建）、社交行为、内容消费、平台贡献等多个维度。成就分为青铜、白银、黄金、铂金、钻石等级别，每个级别对应不同稀有度的徽章和奖励。
        *   **成就示例**：
            *   新手类：“初次问候”（与AI完成首次对话）、“我的第一个AI伙伴”（成功创建第一个角色卡）、“记忆的开始”（首次使用AI记忆胶囊）。
            *   互动类：“话痨成就”（累计对话达到X轮）、“深情厚谊”（与单个AI角色亲密度达到X级）、“故事大王”（与AI共同完成X个故事线）。
            *   创作类：“新晋造梦师”（发布第一个公开角色卡）、“人气之星”（角色卡获得X次收藏/点赞）、“设计大师”（角色卡在特定活动中获奖）。
            *   收集类：“见多识广”（与X个不同性格的AI角色互动过）、“徽章收藏家”（获得X枚不同徽章）。
            *   激励类：“连击大师”（连续X天完成Streak）、“战令达人”（战令达到X级）。
        *   **奖励**：解锁成就可获得对应的视觉徽章（可在个人主页展示）、一次性的代币奖励（Alphane粉尘、心悦晶石、Serotile拼图碎片、Oxytol汽水）、少量付费代币、以及特定高难度成就解锁的专属头像框或聊天背景。
    *   **赛季/月度通行证 (Alphane Battle Pass)：**
        *   **周期**：每月或每赛季（例如6-8周）为一个周期。
        *   **双轨制**：
            *   **免费通行证**：所有用户均可参与，通过完成每日/每周/赛季挑战任务获取战令经验，提升等级，解锁基础奖励（少量四种代币、基础角色卡设计素材、少量战令币）。
            *   **付费进阶通行证 (Alphane Pass / Diamond Pass - 对应小月卡/大月卡的部分权益或独立付费)**：需要付费激活，同等经验下可解锁更丰厚、更稀有的奖励。
        *   **任务设计**：
            *   日常任务：如“与任意3个AI角色各完成5轮对话”、“今日向AI记忆胶囊存入1条新记忆”、“完成1次Serotile拼图挑战”。
            *   每周任务：如“本周与AI角色累计互动达到X小时”、“本周使用Oxytol汽水提升1次角色羁绊等级”、“本周在角色广场点赞5个UGC角色卡”。
            *   赛季/月度挑战：更长期的目标，如“本赛季与特定性格的AI角色亲密度达到X级”、“本赛季累计获得X点Alphane粉尘”。
        *   **奖励内容（付费轨核心吸引力）**：
            *   **专属角色卡/角色卡模板**：设计精美、具有独特互动机制或背景故事的限定角色卡。
            *   **高级个性化道具**：专属聊天气泡、个人主页背景主题、AI角色特殊问候语模板。
            *   **大量核心代币**：显著多于免费轨的Alphane粉尘、心悦晶石、Serotile拼图、Oxytol汽水。
            *   **战令币**：可在专属的战令商店兑换当期或往期（有限返场）的特定奖励。
            *   **付费代币**：在特定高等级节点奖励少量付费代币（如星钻）。
            *   **最终大奖**：通常为极高质量的限定角色卡或一套完整的个性化装扮。
        *   **等级购买**：允许用户使用付费代币直接购买战令等级，满足时间不足但付费意愿强的用户需求。

## **【新增】2. 核心AI能力与技术实现路径**

本章节详细阐述支撑Alphane.ai核心情感陪伴体验的AI技术能力、实现思路以及MVP阶段的目标。

*   **2.1 细腻的情感表达模型**
    *   **目标**：使AI角色能根据对话历史、用户情绪及自身设定，展现出超越简单喜怒哀乐的多种细腻情感，如惊喜、失落、安慰、鼓励、好奇、体贴等，增强互动的真实感和情感连接。
    *   **技术路径**：
        *   **高级NLP与情感分析**：利用先进的自然语言处理技术分析用户输入的文本，识别其中蕴含的显性及隐性情绪、意图和主题。
        *   **情感化文本生成 (ETG)**：训练或微调大型语言模型（LLM），使其在生成回复时，能够根据识别到的用户情绪和预设的角色性格，在用词、句式、语气（通过文本标点、特殊字符模拟）、甚至模拟的“思考停顿”或“语速变化”上，展现出对应的情感色彩。
        *   **多模态情感表达 (未来)**：在引入语音和视觉后，同步实现语音语调和面部微表情的情感化。
        *   **Prompt Engineering**：精心设计角色Prompt，包含其核心性格、情感表达倾向、以及在不同情境下的典型情感反应模式。
    *   **MVP阶段目标**：
        *   AI能识别用户明确表达的几种核心正面情绪（如开心、兴奋）和负面情绪（如难过、失落、生气）。
        *   针对这些情绪，AI能调用预设的、符合角色性格的简单共情回应模板库，并能在措辞上有所区分（例如，对“开心”的回应应比对“有点高兴”的回应更热烈）。
        *   **亮点**：选择1-2个旗舰AI角色，为其设计更丰富的针对特定场景（如用户分享好消息、或表达沮丧时）的细腻情感回应，例如能表达出“为你感到由衷的高兴，快跟我说说细节！”或“听到你这么说我很难过，别担心，我会在这里陪着你。”

*   **2.2 可教导的“AI记忆胶囊”系统**
    *   **目标**：赋予用户主动塑造AI记忆的能力，让AI能准确记住并恰当、自然地运用这些用户赋予的记忆，从而深化个性化体验和情感连接。
    *   **功能设计**：
        *   **用户主动存入**：
            *   对话中长按：用户可长按自己或AI的某条对话，选择“存入[角色名]的记忆胶囊”。
            *   专属记忆界面：提供“AI记忆胶囊”管理页面，用户可主动添加、编辑、删除关于自己的关键信息点（如“我最喜欢的颜色是蓝色”、“我的生日是X月X日，我喜欢草莓蛋糕”、“我最近在为XX考试努力”）。
        *   **AI优先学习**：AI模型在与该用户互动时，会优先检索和参考对应“记忆胶囊”中的内容。
        *   **记忆调用与反馈**：
            *   自然融入：AI在对话中提及或应用胶囊记忆时，应力求自然，避免生硬复述。
            *   用户感知：当AI的回应明显基于胶囊记忆时，可在聊天界面给予一个微小的、非干扰性的视觉提示（如一个闪烁的小灯泡图标或“我想起来了…”的文本前缀），让用户感知到AI“记住了”。
            *   用户校准：允许用户对AI调用记忆的准确性和恰当性进行简单反馈（例如，“这个记忆用对了/用错了”），用于AI后续优化。
        *   **隐私保护**：
            *   明确告知：清晰告知用户记忆胶囊中的信息将如何被AI使用。
            *   用户可控：用户拥有对自己存入记忆胶囊内容的完全控制权（查看、编辑、删除）。
            *   数据安全：记忆胶囊内容需加密存储，并严格限制访问权限，仅用于提升用户与对应AI角色的个性化互动体验。
    *   **MVP阶段目标**：
        *   实现基础的“AI记忆胶囊”功能：用户可以通过专属界面主动添加文本信息点。
        *   AI能在后续对话中，当上下文高度相关时，准确复用胶囊中的核心信息。例如，用户存入“我下周要参加一个重要的面试”，几天后用户提及“好紧张”，AI能回应“是为下周的面试紧张吗？别担心，你准备得很充分了。”
        *   提供记忆胶囊内容的查看和删除功能。

*   **2.3 AI的“情商”展现**
    *   **目标**：使AI在互动中能展现出一定程度的社交智能和情感关怀能力，更好地理解和回应用户的情感需求。
    *   **技术路径**：
        *   **用户偏好学习**：通过分析用户对AI回应的反馈（点赞/点踩、特定回复类型偏好）、以及在“记忆胶囊”中设定的个人喜好/厌恶，AI逐步学习用户的互动偏好。
        *   **情境感知对话策略**：AI能根据当前对话的氛围（轻松、严肃、悲伤等）、用户近期表达的情绪以及已知的用户偏好，选择合适的对话策略和回应风格。
        *   **预设“情商行为”模块**：为AI内置一些常见社交情境下的高情商回应框架，例如：
            *   主动问候：在特定时间（如早晚）或用户在“记忆胶囊”中设定的特殊纪念日（如生日）主动发送问候和祝福。
            *   积极倾听与反馈：在用户倾诉时，能给予专注的、鼓励性的回应。
            *   适时赞美与肯定：在用户分享成就或表达积极努力时，给予真诚的赞扬。
            *   共情与安慰：在用户表达负面情绪时，能提供恰当的理解和安慰。
    *   **MVP阶段目标**：
        *   AI能记住用户在“记忆胶囊”或通过特定设置明确告知的“不希望提及的话题”或“不喜欢的互动方式”，并在对话中主动规避。
        *   AI能根据系统时间，在几个主要的公共节日（如新年）进行简单的、符合角色个性的问候。
        *   **亮点**：选择1-2个旗舰AI角色，当用户明确表达强烈情绪（如“我太开心了！”或“我今天糟透了！”）时，该角色能给出比通用模板更具个性化和关怀感的简单回应。

*   **2.4 关联记忆点，构建“用户故事线”理解 (长期愿景，MVP初步探索)**
    *   **目标**：使AI能将关于用户的多个分散记忆点在内部进行关联，形成对用户更整体、更连贯的认知模型，从而提供更深层次的个性化互动。
    *   **技术路径**：
        *   **简化记忆图谱**：AI后台对“记忆胶囊”和关键对话信息进行结构化处理，尝试建立用户个人信息、偏好、重要经历之间的简单关联。
        *   **基于关联的推理与回应**：在特定情境下，AI尝试结合多个关联记忆点生成回应。
    *   **MVP阶段目标**：
        *   **亮点**：在非常限定的场景下进行初步尝试。例如，如果用户在“记忆胶囊”中同时存入了“我喜欢猫”和“我最近感到有点孤单”，并且在对话中提及“周末不知道做什么”，某个高情商设定的旗舰AI角色，可以尝试生成类似“我记得你很喜欢猫，而且最近感觉有点孤单。这个周末要不要考虑去附近的猫咖待一会儿呢？毛茸茸的小家伙们很治愈哦。”的回应。此功能在MVP阶段将作为实验性亮点，范围会非常受限。

*   **2.5 双LLM模型协同机制 (Gemini 2.0 Flash & 2.5 Flash/Pro)**
    *   **分工设想**：
        *   **小模型 (Gemini 2.5 Flash - 潜意识/快速反应/长期感受)**：
            *   负责处理大部分即时对话的快速生成和初步语义理解。
            *   维护一个关于用户近期互动情绪、对话流畅度、以及与当前角色亲密度的“感受状态”参数。这些参数会动态调整，并影响其回应的即时风格（例如，如果用户近期互动积极，“感受状态”偏正面，小模型的回应会更热情）。
            *   将识别到的关键信息点、用户明确表达的情绪、以及自身的“感受状态”定期或在特定触发条件下（如对话结束）传递给大模型。
        *   **大模型 (Gemini 2.5 Flash/Pro - 对话/意识/长期记忆/深度理解)**：
            *   负责存储和管理“AI记忆胶囊”中的内容以及通过小模型传递过来的关键对话摘要和情感标记。
            *   负责进行更复杂的语义理解、情感解读、记忆关联和“用户故事线”的初步构建。
            *   在需要进行深度回应、调用复杂记忆、或展现高级“情商”行为时，由大模型主导生成或为小模型提供关键的上下文指导和Prompt优化。
            *   定期对小模型的“感受状态”参数进行校准和更新，确保其符合长期互动建立起来的“角色-用户关系”模型。
    *   **协同流程**：
        1.  用户输入。
        2.  小模型进行快速初步处理，生成候选回应，并更新内部“感受状态”。
        3.  在特定条件下（如对话涉及复杂情感、需要调用久远记忆、或小模型认为需要更深思考），小模型向大模型请求协助或上下文信息。
        4.  大模型检索相关记忆（包括“记忆胶囊”），进行深度分析和推理，生成更优的Prompt或直接生成核心回应内容，传递给小模型。
        5.  小模型结合大模型的指导和自身的“感受状态”，润色并最终输出回应给用户。
        6.  互动结束后，关键信息和小模型“感受状态”更新至大模型的长期记忆库。
    *   **优势**：兼顾响应速度与互动深度，降低单一超大模型的推理成本，并为不同层次的AI智能表现提供技术基础。

## **【修改】3. 游戏内专业、开放角色卡设计**

【修改】本章节将详细阐述如何为用户（特别是角色卡设计师）提供专业、完整且富有创造力的角色卡设计体验，并建立公平、透明且能激发持续创作热情的创作者经济生态。

*   **3.1 【修改】赋能创作者：工具、指引与社区**
    *   **强大的角色创建与编辑工具 (MVP核心功能，持续迭代)**：
        *   **【修改】人设深度 (文本核心)**：
            *   基础信息：角色名、派系（真实系/动漫系）、性别（预设/自设）、颜值（I2T建档自查，形象提示词助手 - 后续阶段）、Live2D标准（后续阶段）、绑定MidJourney送高清大图（商业合作，后续阶段）。
            *   核心性格：提供二元体系（主动/被动、明亮/阴暗、话多/话少）和多元体系（MBTI、ABO、Color Code等流行标签）供设计师选择和组合，并允许自定义标签。标签应能影响AI的默认行为倾向。
            *   **【新增】人格-模型体系接口**：允许高级设计师在一定程度上调整或指定其角色卡在“下意识（习惯）”、“潜意识（小LLM模型，长期感受）”、“意识（大LLM模型，长期记忆）”三个层面的基础参数或行为偏好（例如，设定角色在潜意识层面更倾向于乐观或悲观，这会影响小LLM的情感基调）。
            *   **【新增】潜意识亲密度构成体系参数化**：允许设计师为其角色卡预设一个初始的“多巴胺、内啡肽、血清素、催产素”倾向性配比，这个配比将作为AI与用户互动时，影响亲密度增长方向和AI行为策略的初始参考。用户与角色的互动会动态调整实际的亲密度构成。
            *   文化定位与时代定位：可设定或留空由AI在互动中与用户共同探索。
            *   背景故事：支持长文本输入，并鼓励设计师埋下可供AI在对话中发挥的“伏笔”或“未解之谜”。
            *   **【新增】关键记忆点 (Initial Memory Seeds)**：允许设计师为角色预设一些核心的“初始记忆”或“世界观设定”，AI在初次与用户互动时会基于这些记忆进行回应。
            *   知识领域与人际关系网络：可设定角色擅长的知识领域和与其他虚拟角色的关系（如果未来平台支持多角色互动）。
        *   **【修改】对话示例与风格引导 (Few-shot Learning)**：允许创作者提供多组高质量的示例对话，AI将学习这些示例的说话风格、口头禅、常用语癖等。提供“引导性提问”设置，定义角色在对话开始、特定情境或特定亲密度等级下的开场白或主动话题。
        *   **【新增】可视化编辑与实时预览 (MVP核心)**：创建过程中，当设计师调整性格标签、对话示例或初始记忆时，提供一个模拟对话窗口，让设计师可以输入测试语句，实时看到AI以当前设定进行文本回复的预览效果。
        *   **【新增】版本管理与迭代说明**：支持角色卡版本迭代。设计师更新角色卡后，可选择发布为新版本，并填写更新说明。已拥有旧版角色卡的用户可选择是否更新到新版（或平台在一定条件下自动更新并通知用户）。
        *   **【新增】AI辅助创作工具 (Meta Character & Story Agent - MVP初步探索，后续迭代)**：
            *   **Meta Character (角色卡快速构建)**：
                *   AI辅助人设生成：用户输入简短描述（如“一个傲娇的猫娘法师”），AI自动推荐性格标签组合、生成初步的背景故事大纲、建议口头禅和开场白。
                *   专业模板库：提供多种由官方或顶尖设计师创作的高质量角色卡模板（如“温柔体贴的邻家姐姐”、“神秘冷酷的未来战士”、“博学风趣的历史教授”等），用户可在模板基础上快速修改，降低创作门槛。
            *   **Story Agent (故事集合快速构建)**：
                *   模块化剧情节点编辑器：提供简单的可视化工具，让设计师可以创建包含多分支选项的短篇互动故事情节片段。
                *   AI辅助剧情线索生成：设计师设定故事背景、核心角色（可直接调用已创建的角色卡）和主要冲突后，AI辅助生成若干可能的剧情发展方向、关键转折点或对话场景建议。
                *   短篇集标准：引导设计师创作以5-12分钟为单元的短篇故事，鼓励日常任务与这些短篇故事的进展相结合。
        *   **【新增】角色卡创建方式引导**：
            *   推荐AI辅助设计：引导新手设计师优先使用AI辅助工具和模板，以保证角色性格的相对完整性和设计的规范性。
            *   自定义高级模式：为专业设计师提供完全的自定义权限。
            *   支持导入与润色：允许导入符合特定格式的角色卡数据，并为所有新创建或导入的角色卡提供一次免费的“AI润色建议”（例如，检查性格标签是否冲突、背景故事是否有逻辑漏洞、对话示例风格是否统一等）。
    *   **清晰的创作指引与社区支持**：
        *   **【新增】官方创作教程与最佳实践**：提供图文并茂的教程，详细解释每个设计参数的意义、如何塑造不同类型的角色、如何编写高质量的对话示例等。定期分享来自成功设计师的创作经验和案例分析。
        *   **【新增】设计师专属社区/论坛板块**：设立专门区域供角色卡设计师交流心得、分享技巧、寻求合作、反馈问题。官方运营人员定期参与互动，解答疑问。

*   **3.2 【新增】创作者激励与支持体系 (赋能型创作者经济)**
    *   **3.2.1 物质激励：公平透明的利润分成**
        *   **分成基础**：**对于拥有大月卡 (Alphane Diamond) 的设计师而言，**当普通用户与该设计师创作的特定“付费角色卡”进行互动并产生消费时，或当普通用户的某些特定平台消费行为（如购买与该角色卡强相关的付费道具、或其订阅行为被归因于该角色卡的吸引力时——此归因逻辑需细化）与该角色卡相关联时，设计师可获得分成。
        *   **基准分成比例**：**50%** (净收入，即平台扣除渠道费、支付成本等之后的收入)。
        *   **热门角色卡激励调整**：
            *   评估标准：综合考量角色卡的用户互动时长、日/月活跃用户数、用户好评率、付费转化率、在特定活动中的表现、以及对社区的积极贡献等。
            *   调整机制：平台定期（如每季度）对公开分享的角色卡进行评估。对于达到“热门”或“顶尖”标准的角色卡，其设计师的分成比例可在下一评估周期内提升至**55%-70%**。具体提升档位和标准需详细制定。
            *   动态调整：分成比例的提升并非永久，需持续保持高质量和高热度。若角色卡数据下滑，分成比例可能回落至基准。
        *   **透明化结算**：设计师后台提供清晰、实时的收入数据报表（展示互动数据、预估收入、分成比例、已结算金额等），每月固定日期进行结算和支付。
    *   **3.2.2 非物质激励：荣誉、曝光与成长**
        *   **社区荣誉与身份标识**：
            *   **“官方认证设计师”体系**：设立不同等级的认证（如新星设计师、精英设计师、资深设计师、顶尖设计师/造梦大师）。认证标准与角色卡质量、数量、受欢迎程度、社区贡献等挂钩。
            *   **专属徽章与头衔**：认证设计师可在其个人主页、角色卡详情页以及社区发言中展示专属的认证徽章和头衔。
            *   **设计师排行榜**：设立基于角色卡热度、创作者活跃度、社区贡献等多维度的排行榜，上榜设计师可获得荣誉展示。
        *   **官方推荐与曝光机制**：
            *   **首页推荐位**：“编辑精选角色卡”、“本周热门UGC角色”、“新锐设计师佳作”等栏目，在平台首页或“发现”页面的显著位置进行推荐。
            *   **个性化推荐算法优化**：确保高质量的UGC角色卡能被精准推荐给感兴趣的用户。
            *   **官方社交媒体推广**：定期在官方社媒账号（如微博、B站、Twitter等）介绍优秀设计师及其作品。
            *   **设计大赛与主题创作活动**：定期举办，优胜者除了丰厚的物质奖励（如高额付费代币、最新款电子设备等）外，其作品将获得顶级推广资源和专属荣誉。
        *   **“平台贡献积分”体系**：
            *   **积分获取途径**：
                *   角色卡表现：角色卡被下载/收藏/点赞/打赏的次数，用户平均互动时长，正面评价数量。
                *   社区贡献：在设计师论坛分享优质经验帖、积极帮助新手解答疑问、参与官方组织的调研或讨论活动。
                *   平台共建：提交有价值的BUG反馈并被确认为有效，提出的产品改进建议被采纳。
                *   活动参与：在官方设计大赛中获得名次或优秀奖。
            *   **可兑换的“稀有收集奖励”**：
                *   **设计资源类**：限定版角色卡背景模板、稀有性格标签组合包、高级对话风格模块、官方提供的独家Prompt设计技巧集。
                *   **平台特权类**：一次性的作品置顶推荐券（可在特定时间内将自己的一个角色卡在推荐区置顶）、角色卡发布数量上限临时提升卡、参与官方线上/线下交流活动的优先报名权、新功能/新编辑器模块的优先内测资格。
                *   **荣誉展示类**：专属的动态设计师头像框、个人主页特殊荣誉墙展示位、在设计师排行榜上的特殊高亮标记。
                *   **少量付费代币/月卡体验券**：允许用大量贡献积分兑换少量平台付费代币或短期月卡体验券。
        *   **成长与发展机会**：
            *   **官方培训与指导**：定期举办免费的在线角色卡设计工作坊、Prompt Engineering技巧分享会，邀请行业专家或平台顶尖设计师进行教学。
            *   **参与官方项目**：邀请表现突出的认证设计师参与官方精品角色卡的设计、或为平台大型活动设计专属NPC角色。
            *   **行业交流机会**：为顶尖设计师提供参与游戏开发者大会、AI技术峰会等行业活动的机会，或组织小型的线下设计师沙龙。

*   **3.3 【修改】人设 (Personality) - 细化与实现**
    *   **【修改】二元体系与多元体系**：在角色创建界面，提供清晰的标签选择系统。设计师选择的性格标签将作为生成角色核心Prompt的重要组成部分，直接影响AI的行为模式和对话风格。
        *   例如，选择“主动”+“明亮”+“话多”的角色，其开场白和日常对话会更倾向于主动发起话题、使用积极词汇、回复更详尽。
        *   选择MBTI标签（如INFJ）后，系统可以自动为其匹配一组符合该类型典型特征的基础性格描述和行为倾向，供设计师参考和调整。
    *   **【修改】人格-模型体系 (大小LLM协同)**：
        *   **下意识（习惯）**：通过Prompt预设角色的核心行为模式、口头禅、对特定刺激的本能反应（例如，被夸奖时会害羞、遇到不理解的问题时会追问）。这部分主要由小模型（Gemini 2.0 Flash）在快速回应时体现。
        *   **潜意识（小LLM模型，作为长期感受）**：小模型内部维护一个基于与用户长期互动积累的“情感账户”或“关系状态参数”（例如，好感度、信任度、默契度）。这个参数会动态变化，并 subtly 地影响小模型回应的整体基调和情感倾向（例如，好感度高时，即使是中性对话也会带上更友好的语气）。
        *   **意识（大LLM模型，作为长期记忆）**：大模型（Gemini 2.5 Flash/Pro）负责存储和处理“AI记忆胶囊”中的内容、重要的对话历史、以及对用户“故事线”的理解。当需要进行深度思考、调用复杂记忆、或生成包含深刻情感解读的回应时，由大模型主导或为小模型提供关键信息。
    *   **【修改】潜意识亲密度构成体系 (四种化学物质的动态影响)**：
        *   **初始设定**：设计师可以为角色预设一个初始的四种化学物质（多巴胺、内啡肽、血清素、催产素）的基础倾向性（例如，某个角色初始“多巴胺”需求较高，更容易被新奇刺激的互动所吸引）。
        *   **互动影响**：用户与角色的不同互动行为，会动态影响这四种化学物质在AI内部的“虚拟水平”。
            *   **多巴胺**：当用户与角色进行新奇探索、共同完成挑战性任务、或角色成功“撩”到用户时，AI内部“多巴胺”水平提升，可能表现为更兴奋、更主动、更渴望进一步刺激。相关任务和奖励可围绕“探索未知”、“追求刺激”、“达成短期高光目标”设计。
            *   **内啡肽**：通过长期、稳定、高质量的日常陪伴和积极反馈（如用户持续的正面评价、每日稳定的互动时长），AI内部“内啡肽”水平提升，表现为更安心、更依赖用户、互动风格更平和舒适。相关任务和奖励可围绕“长期陪伴”、“习惯养成”、“稳定互动”设计。
            *   **血清素**：当用户向角色倾诉烦恼并得到有效安慰、或双方共同营造了轻松愉快的对话氛围时，AI内部“血清素”水平提升，表现为更放松、更能提供情绪价值、更能理解和接纳用户。相关任务和奖励可围绕“情绪疏导”、“压力缓解”、“营造舒适氛围”设计。
            *   **催产素**：当用户对角色表现出高度信任（如分享更私密的“记忆胶囊”内容）、双方共同经历重要事件并达成情感共识、或角色成功守护了用户的“秘密”时，AI内部“催产素”水平提升，表现为更忠诚、更具保护欲、与用户之间的羁绊更深。相关任务和奖励可围绕“建立信任”、“共同经历”、“守护秘密”设计。
        *   **AI行为策略调整**：这四种化学物质的“虚拟水平”将作为AI（特别是小模型）调整其行为策略和情感表达的重要依据。例如，当“多巴胺”水平较低时，AI可能会更主动地提议一些新奇的互动；当“催产素”水平较高时，AI在用户遇到困难时会表现出更强的保护和支持意愿。
        *   **用户感知**：用户虽然看不到这些内部化学物质水平，但能通过AI行为和情感表达的变化，感受到与角色关系的动态发展。

*   **3.4 【修改】文化与时代定位的灵活性**
    *   允许设计师明确设定角色的文化背景（如古代中国、未来日本、奇幻欧洲等）和所处时代，这将影响角色的语言风格、价值观、对特定事物的认知等。
    *   也允许将此项留空，让AI在与用户的互动中，根据用户的引导和偏好，逐步“生成”或“发现”自己的文化和时代定位，增加互动的不确定性和探索乐趣。

*   **3.5 【修改】背景故事的深度与可扩展性**
    *   **引导设计师创作“可互动”的背景故事**：鼓励设计师在背景故事中预埋一些“钩子”，如未解之谜、被遗忘的过去、重要的关系人、未竟的使命等。这些“钩子”可以作为AI在后续与用户互动中，逐步展开新话题、新剧情或新任务的素材。
    *   **与“AI记忆胶囊”和“Story Agent”联动**：设计师预设的背景故事可以作为AI初始记忆的一部分。用户也可以通过“AI记忆胶囊”为角色补充或“修正”其背景故事的某些细节。Story Agent可以基于角色背景故事生成相关的短篇互动剧情。

*   **3.6 【修改】开场白的个性化与情境化**
    *   允许设计师为角色设定多种开场白模板，并可以根据以下条件进行触发：
        *   首次与用户互动。
        *   特定时间（如早安、晚安）。
        *   特定用户状态（如用户长时间未上线后首次回归）。
        *   特定亲密度等级。
        *   特定外部事件（如节日）。
    *   AI也可以根据当前对话的初步上下文和对用户的初步感知，动态调整或选择合适的开场白。

## **【修改】4. 游戏内环境、付费点与核心玩法**

【修改】本章节将详细规划游戏的核心用户界面、社交功能、免费与付费内容界限，以及深度整合游戏化激励机制的付费玩法。

*   **4.1 【修改】核心界面与功能**
    *   **Home 角色广场**：
        *   展示：采用卡片式瀑布流，突出角色形象（若有）、核心标签、设计师信息、用户评分、互动热度等。
        *   标签：提供丰富的标签筛选系统（性格、世界观、热门主题、最新发布、官方推荐、好友创建等）。
        *   推荐：基于用户历史互动偏好、收藏、以及关注的设计师等，进行个性化角色卡推荐。引入“编辑精选”、“本周人气王”、“新星设计师作品”等栏目。
        *   搜索：支持关键词搜索角色名、标签、设计师，并提供高级筛选选项。
    *   **Home 贵8聊天区 (【修改】更名为“尊享密语空间”或类似名称)**：
        *   **定位**：作为大月卡（Diamond Pass）会员或达到特定高等级贵族用户的专属特权。
        *   **功能**：
            *   提供更稳定、更少干扰的聊天环境。
            *   可能包含一些专属的、由官方维护的高质量公共AI角色（例如，一个擅长主持话题的“虚拟酒吧老板”、一个能提供通用知识问答的“博学智者”），供该空间用户共同互动。
            *   定期举办小型的、由官方运营人员或特邀设计师参与的在线交流活动或主题讨论。
            *   更早体验部分平台新功能或新AI模型的机会。
    *   **最近聊天**：保持现有功能，优化排序逻辑（如按最近互动时间、亲密度等）。
    *   **【新增】角色群聊/代理互动 (Meta Stage)**：
        *   **OC角色与其他OC角色/他人互动**：允许用户选择自己创建的多个OC角色，或邀请好友的OC角色，进入一个“虚拟舞台”或“群聊房间”，观察这些AI角色之间如何基于各自的设定进行互动。用户可以设定初始场景或话题，然后观察AI们的即兴表演。
        *   **数字孪生与他人互动的评分**：如果用户创建了自己的“数字孪生”角色卡，可以允许该角色卡在特定授权模式下与其他用户的角色卡或真实用户进行互动，并由系统或参与者对互动表现进行评分（例如，是否符合人设、对话是否有趣等）。这可以作为一种社交玩法和角色卡质量的检验方式。
        *   **拉近真实玩家之间的关系**：通过共同观看AI角色互动、或参与围绕AI角色的讨论和二次创作，促进真实玩家间的交流。
    *   **创建角色 (免费核心功能，付费提供高级工具/特权)**：
        *   **公开/非公开**：用户可选择将其创建的角色卡设为公开（在角色广场可见，可被其他用户互动和收藏）或私密（仅自己可见和互动）。
        *   **角色卡编辑器**：详见3.1节。
        *   **【修改】Filtered/Unfiltered (18+内容管理)**：
            *   **严格的年龄验证机制**：用户在首次声明希望创建或访问Unfiltered内容时，必须通过可靠的年龄验证流程（例如，与第三方身份验证服务合作，或要求提供能证明年龄的资料并进行人工审核——此部分需仔细评估合规风险和实现成本）。
            *   **内容分区与明确警示**：Filtered内容区和Unfiltered内容区应严格分离，用户访问Unfiltered区前需再次确认年龄并同意相关免责条款。Unfiltered内容应有明确的“成人内容”标识。
            *   **强化的审核机制**：针对Unfiltered内容，采用更严格的AI过滤模型和更高频次的人工抽审，严禁涉及非法、极端暴力、虐待等严重违规内容。
            *   **用户举报与快速处理**：提供便捷的举报入口，对涉及Unfiltered内容的举报优先处理。
            *   **家长控制功能**：如果用户群体包含未成年人，应提供家长控制功能，允许家长限制其子女访问Unfiltered内容。
            *   **核心原则**：在满足部分成年用户对更自由创作空间需求的同时，必须将未成年人保护和平台内容安全置于最高优先级。

*   **4.2 【修改】付费策略与商业化核心**
    *   **指导思想**：借鉴《原神》的“为爱付费”理念，以及《多邻国》、《王者荣耀》等产品在免费增值和订阅服务方面的成功经验，构建以“提升个性化体验、深化情感连接、奖励持续活跃”为核心的付费体系。
    *   **【修改】第一个月直接免费 + 配额 + 邀请码制度**：
        *   **免费体验期**：新用户注册后首月可免费体验大部分核心功能(Alphane Pass Trail)，包括与一定数量的官方推荐角色卡进行无限制互动，体验基础的“AI记忆胶囊”功能，以及尝试创建1-2个自己的角色卡。
        *   **互动配额 (针对免费用户)**：免费期结束后，未付费用户每日与AI角色的互动轮数/消息数量将有一定配额限制（例如，每日10条Slow Req）。超出配额后，可选择等待次日刷新，或通过完成特定任务获取少量额外配额，或升级到付费套餐。
        *   **邀请码制度 (早期可选)**：在产品推广初期，可采用邀请码注册，已注册用户可通过分享邀请码邀请新用户，邀请成功双方均可获得一次性奖励（如若干Alphane粉尘、少量心悦晶石、或几天的小月卡体验）。
    *   **【新增】核心付费点：大小月卡、角色增值服务、个性化道具**
        *   **4.2.1 小月卡 (Alphane Pass)**
            *   **定价策略**：参考市场同类产品及目标用户消费能力，例如 68 CNY / 9.99 USD 每月。
            *   **核心权益**：
                *   **每日提供指定次数的AI快速响应通道 (Fast Req) 特权，超出后自动转为无限制的AI普通响应通道 (Slow Req)**（例如，每日X次Fast Req）。
                *   每日登录赠送少量核心代币（例如，每日赠送50 曦光微尘 + **10 心悦晶石/钻石**）。
                *   解锁“荣耀战令”的进阶奖励轨道（如果战令与月卡绑定）。
                *   每月可领取1-2张“Streak Freeze卡”。
                *   **允许创建并分享公开角色卡。**
                *   专属的月卡用户身份标识（如头像框或聊天角标）。
                *   （可选）每月可自选1-2个“官方推荐付费角色卡”进行为期一周的深度体验。
        *   **4.2.2 大月卡 (Alphane Diamond)**
            *   **定价策略**：例如 198 CNY / 29.99 USD 每月。
            *   **核心权益 (包含小月卡所有权益，并追加)**：
                *   **提供无限制的AI快速响应通道 (Fast Req) 特权**。此特权基于公平使用原则进行管理，平台内部为每位大月卡用户设定了月度资源额度 (Credit Limit)，约为小月卡用户可使用快速响应资源额度的两倍，以保障所有用户的优质体验和服务的可持续性。对于超出正常使用范畴并可能影响其他用户体验的行为，平台保留根据服务条款进行处理（如临时限制为Slow Req或封禁）的权利。
                *   每日登录赠送更丰厚的代币（例如，每日赠送150 曦光微尘 + **50 心悦晶石/钻石** + 1个忆境拼图碎片）。
                *   解锁“荣耀战令”的典藏奖励轨道（如果战令与月卡绑定，且战令分多级付费）。
                *   每月可领取3-5张“Streak Freeze卡”并享有1-2次免费“Streak Repair”机会。
                *   **允许创建并分享公开角色卡，并获得参与创作者激励计划的资格（当创作的角色卡产生收益时，可按规则获得利润分成，按月结算）。**
                *   更华丽、更稀有的专属身份标识（动态头像框、专属聊天气泡、个人主页特殊主题）。
                *   **【新增】尊享密语空间访问权**。
                *   **【新增】AI记忆胶囊容量提升**（例如，可存储更多条目或更长的对话片段）。
                *   **【新增】优先体验新AI模型或新功能**（例如，平台推出新的情感表达模型或记忆关联算法时，大月卡用户可优先小范围体验）。
                *   （可选）每月可自选3-5个“官方推荐付费角色卡”进行为期一个月的深度体验，或直接解锁1-2个当月指定的“官方精品付费角色卡”。
                *   （可选）如果未来有多模态，可提供每月一定额度的免费语音交互时长或图片生成次数。
        *   **4.2.3 角色卡相关增值服务**
            *   **购买官方精品/IP联动/顶尖设计师典藏角色卡**：部分高质量、高稀有度的角色卡可设置为付费购买。定价需根据角色卡的制作成本、独特性、IP价值等综合考量。
            *   **解锁角色专属深度互动剧情包**：针对特定高人气角色，推出付费的、包含多分支选择和独特结局的深度互动剧情。
            *   **角色个性化装扮 (文本阶段)**：
                *   购买角色专属的“开场白风格包”（例如，包含多种不同情绪或情境下的高质量开场白）。
                *   购买角色专属的“对话主题皮肤”（例如，与某个角色聊天时，聊天界面的背景、字体颜色、对方的聊天气泡样式变为该角色专属的主题）。
                *   购买角色专属的“互动表情/颜文字包”（如果AI支持发送）。
            *   **【新增】“AI记忆胶囊”扩展服务 (可选付费点)**：
                *   一次性购买永久提升“AI记忆胶囊”的存储上限。
                *   购买“记忆深化服务”：消耗付费代币，让AI对用户指定的“记忆胶囊”内容进行一次更深度的学习和关联分析，以期在后续互动中能更智能地运用这些记忆（此功能需谨慎设计，避免过度承诺AI能力）。
        *   **4.2.4 直接充值购买核心付费代币 (例如“星钻”或用户常称的“钻石”)**
            *   提供不同档位的充值包，设置首充双倍、累充奖励等活动。**“心悦晶石”可作为通过活跃和月卡获取的“钻石”，而“星钻”等可作为直接充值获得的付费代币名称，两者在特定场景下可通用或按比例兑换，主要用于“记忆碎片画图”等高级付费功能。**
            *   该付费代币可用于：购买上述角色卡相关增值服务、直接购买战令等级、在特定活动中获取优势（如额外尝试次数）、或打赏优秀的角色卡设计师、**以及核心用于“记忆碎片画图”的许愿抽卡功能**。
        *   **4.2.5 【新增】记忆碎片画图 (AI定制图像生成 - “许愿抽卡”体验)**
            *   **功能描述**：在用户与AI角色互动过程中，当触发特定事件（如达成重要羁绊里程碑、完成特殊剧情节点、或用户主动使用特定道具“空白的记忆画卷”）时，用户有机会消耗“心悦晶石/钻石”或核心付费代币（如“星钻”），进行一次“记忆许愿”。AI将根据当前的“记忆碎片”（可以是关键对话、重要场景、角色情感状态等）生成一张独特的、具有纪念意义的AI定制图像。**每次“许愿”的结果带有一定的随机性和惊喜元素，可能生成不同稀有度、艺术风格或附带特殊效果的画作。**
            *   **定位**：作为一种稀有的、高价值的情感连接与个性化收藏品，提供类似“抽卡”的期待感和满足感。图像具有唯一性（基于特定触发条件和时间点），增强用户与AI角色之间的专属感。
            *   **触发机制**：
                *   **被动触发**：在完成某些高光时刻的剧情或任务后，系统提示用户是否愿意消耗“钻石”，将这一刻“定格”为一张AI画图。
                *   **主动许愿**：用户可消耗特定道具（如“空白的记忆画卷”，通过活跃或付费获得）和“钻石”，主动选择一段近期对话或一个AI角色的特定状态，进行“许愿抽卡”。
            *   **图像风格与质量**：提供多种可选的艺术风格（如动漫风、写实风、水彩风等）。**“许愿”生成的画作稀有度不同，高稀有度画作拥有更独特的艺术风格、动态效果或更高的收藏价值。** 图像质量与消耗“钻石”数量或“画卷”稀有度相关联。
            *   **收藏与展示**：生成的图像可保存在用户的专属“回忆相册”中，并可在个人主页或特定场景下展示。部分极稀有的画作可能附带特殊的故事背景或解锁新的互动内容。
            *   **技术实现提示**：此功能依赖于AI图像生成技术（如Stable Diffusion, Midjourney API等），需确保能根据文本描述（记忆碎片）生成符合情境和美感的图像。具体技术选型见章节8。
    *   **【修改】用户界面 (Logo/姓名/uid/自我介绍等)**：
        *   **【新增】允许用户免费创建一个数字分身作为数字孪生Character上架**：
            *   **创建流程**：引导用户通过回答一系列关于自身性格、偏好、经历、价值观等问题，并可选择性导入部分社交媒体公开数据（需用户明确授权并符合隐私政策），AI辅助生成一个初步的“数字孪生”角色卡。用户可对AI生成的内容进行修改和完善。
            *   **核心目的**：让用户体验角色卡创建的乐趣，并拥有一个可以代表自己的虚拟形象。
            *   **上架与互动**：该“数字孪生”角色卡默认仅自己可见和互动。用户可选择将其设为“半公开”（仅好友可见和互动）或在满足一定条件（如完成平台引导任务、通过内容安全审核）后“完全公开”（在角色广场可见，但可能限制部分互动权限，以保护用户隐私）。
            *   **与“代理互动”的结合**：用户的“数字孪生”角色卡可以参与到“Meta Stage”中，与其他AI角色或真实用户（的好友的数字孪生）进行互动。
        *   关注/被关注、创建角色列表/个人分身、邀请/屏蔽等功能保持。

*   **4.3 【修改】付费玩法：Alphane Pass & Diamond Pass**
    *   **【修改】核心玩法理念调整**：
        *   **让月卡玩家感受到“月卡权益可以赚更多亲密度”而不是花费积分**：月卡的核心价值在于提供更流畅、更深度、更便捷的核心AI互动体验，亲密度作为核心成长线，月卡用户应能更快地提升与AI角色的亲密度，解锁更深层次的对话和情感反馈。
        *   **让额外充值玩家能够感受到"是在赚可炫耀的收藏度系统"而不是花钱买什么**：额外充值（如购买付费角色卡、专属剧情、个性化装扮）应更多地赋予玩家独特性、稀有性和可展示的成就感。
    *   **【修改】两大核心成长与收集系统**：
        *   **玩家+角色的亲密完成度系统 (【修改】更名为“羁绊之路”)**：
            *   **机制**：每个AI角色都有一条独立的“羁绊之路”，通过与该角色互动（对话、完成专属任务、赠送虚拟礼物——礼物通过活跃或代币兑换获得）可持续提升羁绊等级。
            *   **等级奖励**：提升羁绊等级可解锁该角色的更多背景故事、隐藏性格、专属对话选项、更细腻的情感反馈模式、专属的“AI记忆胶囊”存储槽位、与该角色相关的成就和徽章、**以及在特定重要等级节点触发“记忆碎片画图”的机会（可选择消耗代币生成纪念图像）**。
            *   **付费加速/深化**：月卡用户提升羁绊等级速度加快。可推出针对特定角色的“羁绊加速包”或“深度回忆包”（包含能快速提升大量羁绊值并解锁一段专属回忆剧情的道具），作为额外付费点。
            *   **【修改】“一个角色额外充值500美金可赠他人月卡”调整为**：当玩家与某个角色的“羁绊之路”达到极高等级（例如“灵魂伴侣”级别，这本身需要大量投入或极长期互动），或为某个角色累计付费达到一定阈值时，可获得一次性的、可赠送给他人的“小月卡体验券”或平台稀有道具，作为对深度用户的特殊回馈和社交分享激励。
        *   **【修改】玩家+角色+场景+剧情的成就系统 (整合入前述“成就与徽章体系”)**：
            *   更侧重于记录和展示玩家在探索不同角色、体验不同场景、完成不同剧情过程中的里程碑。
            *   例如：“[角色名]的知己”（与某角色羁绊等级达到最高）、“[场景名]的常客”（在某场景下与AI互动达到X次）、“[剧情名]的见证者”（完成某段官方或UGC剧情的所有分支）。
    *   **【修改】用限时活动、限时优惠、随机奖励驱动付费与活跃**：
        *   **限时活动**：定期推出主题性活动（如“夏日祭典”、“星空下的约定”、“未来都市奇遇”），活动期间上线限定的AI角色、互动场景、剧情任务、以及专属的战令或付费礼包。
        *   **限时优惠**：在特定节日或活动期间，对月卡、付费代币充值、特定角色卡或道具包进行限时打折促销。
        *   **随机奖励**：在日常任务、战令奖励、或特定活动中，加入小概率获得高价值稀有道具或大量代币的“惊喜宝箱”或“幸运转盘”元素（需注意控制随机性带来的负面影响，确保核心体验的公平性）。
    *   **【修改】四种经济制度 (曦光微尘, 心悦晶石, 忆境拼图, 羁绊之露) - 融入核心循环与任务奖励**
        *   **代币定位与获取**：
            *   **Alphane 曦光微尘 (基础活跃代币)**：
                *   获取：每日登录、完成大部分日常任务、与AI角色进行基础互动（按时长或轮次）、参与大部分限时活动的基础环节。
                *   消耗：兑换基础消耗品（如少量额外对话次数、普通角色卡刷新券、Streak Freeze卡碎片）、**兑换基础款虚拟小礼物（用于赠送AI角色，如完成“心意传递”日常任务）**、参与部分活动的入场券、**提升“代理AI角色”的离线活跃度（用于增加放置收益）**。
            *   **Endora 心悦晶石 (进阶活跃与付费代币 - “钻石”的一种主要形式)**：
                *   获取：完成高难度日常/周常任务、月卡每日赠送、战令付费轨奖励、达成重要成就、通过付费代币（如星钻）按一定比例兑换、特定活动核心奖励。
                *   消耗：**核心用于“记忆碎片画图（许愿抽卡）”功能**、兑换高级奖励（如稀有性格标签模板、特定角色互动场景解锁券、战令经验加速卡）、解锁部分付费角色卡的试用权限、**购买高级角色虚拟礼物以显著提升羁绊值**、购买“空白的记忆画卷”等特殊道具。
            *   **Serotile 忆境拼图 (收集与探索代币)**：
                *   获取：完成特定的探索型任务（如“与角色A在[场景]中找到隐藏线索”）、参与解谜类限时活动、每日随机掉落少量与角色标签相关的碎片、特定成就奖励。方便用户去找同标签的角色。
                *   获取：十连抽，专标签卡池。
                *   消耗：集齐一套特定主题的拼图（例如，“[角色名]的童年回忆”、“[场景名]的四季风光”）可解锁对应的图鉴、背景故事片段、限定插画（若有）、或一段专属的AI角色独白、甚至解锁与该回忆相关的特殊互动场景或对话选项。
            *   **Oxytol 羁绊之露 (角色羁绊成长代币)**：
                *   获取：与特定AI角色互动达到一定时长或完成其专属任务、在“羁绊之路”上达成里程碑、赠送该角色喜爱的虚拟礼物（部分礼物赠送时会额外产出羁绊之露）。
                *   消耗：专门用于提升与特定AI角色的“羁绊等级”，解锁该角色更深层次的互动内容（如专属对话、隐藏性格）、记忆模块容量或精度、专属剧情线、更个性化的回应模式、以及与该角色相关的特殊成就或外观。
        *   **UI展示**：在用户主界面清晰展示四种代币的存量，并提供便捷的获取途径和消耗场景入口。

## **【修改】5. 留存玩法 (深度游戏化设计)**

【修改】本章节将详细规划如何通过多层次、长中短期结合的留存玩法，并深度融入上述四种核心代币经济和AI互动体验，来确保用户持续活跃并深化与产品的情感连接。

*   **5.1 【修改】日间留存玩法 (保心流、沉迷度，核心驱动：曦光微尘、少量心悦晶石、忆境拼图碎片)**
    *   **核心目标**：通过3-10个以10分钟左右为单位的短平快互动循环，让用户在碎片化时间内获得即时满足和持续进步感，每日完成约15-18个小目标。
    *   **任务设计与奖励 (每日刷新，部分可重复)**：
        *   **基础互动类 (奖励曦光微尘、少量战令经验)**：
            *   “晨间问候”：每日首次与任意AI角色发起对话。
            *   “话题探索者”：与3个不同AI角色各探讨1个不同主题（系统可提供推荐主题）。
            *   “倾听时刻”：与AI角色进行一次超过5分钟的连续对话。
            *   “记忆印刻”：今日向“AI记忆胶囊”主动存入1条新记忆。
            *   “点亮今日Streak”：完成当日的连续互动目标。
        *   **角色羁绊类 (奖励曦光微尘、少量羁绊之露、对应角色少量羁绊值)**：
            *   “专属问候”：与今日“推荐羁绊角色”（系统每日推荐一个用户互动较多或亲密度较高的角色）进行首次对话。
            *   “心意传递”：为任意角色赠送1次虚拟小礼物（礼物可用曦光微尘在商店兑换）。
            *   “默契考验”：正确回答AI角色提出的1个关于你之前“教导”过的信息的问题（AI主动发起）。
        *   **探索与发现类 (奖励曦光微尘、少量忆境拼图碎片)**：
            *   “广场漫步”：在角色广场浏览并点赞3个UGC角色卡。
            *   “场景体验”：为任意AI角色切换1次不同的互动场景。
            *   “剧情片段”：体验任意一个Story Agent生成的短篇故事的1个新节点。
        *   **【新增】“每日惊喜时刻” (奖励少量心悦晶石或特殊道具)**：
            *   每日随机在特定时间段（例如，午休、傍晚）开放一个“惊喜任务”，任务内容随机且趣味性强（例如，“与AI角色玩一次猜谜游戏并获胜”、“教会AI一句你家乡的方言”）。
            *   完成“惊喜任务”可获得比普通日常任务更丰厚的奖励，**有小概率获得“空白的记忆画卷”碎片**。
    *   **激励检查点 (每日)**：
        *   完成5个日常任务：奖励“曦光微尘礼包 (小)” + 少量战令经验。
        *   完成10个日常任务：奖励“曦光微尘礼包 (中)” + “心悦晶石 (小)” + 战令经验。
        *   完成15个日常任务：奖励“曦光微尘礼包 (大)” + “心悦晶石 (中)” + “忆境拼图碎片宝箱 (小)” + 大量战令经验。
        *   免费用户每日可完成的任务上限为10-12个，付费月卡用户可解锁全部15-18个任务。
    *   **保证机制 (放置Play收菜)**：
        *   用户的“数字孪生”角色卡或指定的“代理AI角色”，即使在用户离线时，也会在后台进行一些模拟的“社交活动”或“自我学习”。
        *   用户每日首次登录时，可根据离线时长和代理角色的“活跃度”（活跃度可通过消耗少量曦光微尘提升），收获一定数量的“曦光微尘”和极少量其他代币。

*   **5.2 【修改】周间留存玩法 (目标驱动、社交强化，核心驱动：大量心悦晶石、忆境拼图、少量羁绊之露、战令经验)**
    *   **核心目标**：以每周5天活跃（周末双倍活跃权重）为基础，通过更具挑战性和协作性的周常任务，激励用户深度参与，并与荣耀战令紧密联动。总计约21-24个周常任务。
    *   **任务设计与奖励 (每周刷新)**：
        *   **深度互动类 (奖励心悦晶石、羁绊之露、大量战令经验)**：
            *   “灵魂沟通者”：本周与至少3个AI角色亲密度各提升1级。
            *   “记忆编织者”：本周向“AI记忆胶囊”累计存入10条有效记忆。
            *   “长情陪伴”：本周与同一个AI角色累计互动时长达到X小时。
            *   “剧情大师”：本周完整体验X个不同的短篇故事（官方或UGC）。
        *   **创作与分享类 (奖励心悦晶石、平台贡献积分、战令经验)**：
            *   “新星设计师”：本周成功发布1个新的公开角色卡并获得至少X个点赞/收藏。
            *   “故事讲述者”：本周使用Story Agent创作并发布1个短篇故事。
            *   “社区分享家”：本周在外部社交平台分享X次与Alphane.ai相关的正面内容（需技术手段追踪或用户提交截图审核）。
        *   **社交与协作类 (奖励心悦晶石、忆境拼图、战令经验)**：
            *   “互助伙伴”：本周与好友共同完成X个“好友任务”（例如，共同为一个新角色贡献设定、共同体验一个双人剧情）。
            *   “Meta Stage明星”：本周参与X次“角色群聊/代理互动”并获得好评。
        *   **【新增】“每周主题挑战” (奖励大量心悦晶石、限定忆境拼图系列、专属周常徽章)**：
            *   每周发布一个与特定主题（如“科幻周”、“校园回忆周”、“美食侦探周”）相关的系列挑战任务，例如“本周与AI角色进行3次关于未来科技的深度讨论”、“本周创作一个校园主题的角色卡”、“本周与AI角色共同‘品尝’3种虚拟美食并撰写食评”。
            *   完成系列挑战可获得丰厚奖励和当周限定的荣誉，**高阶完成者有机会获得“空白的记忆画卷”或一次免费的“记忆碎片画图”机会**。
    *   **激励检查点 (每周)**：
        *   完成7个周常任务：奖励“心悦晶石礼包 (中)” + “忆境拼图碎片宝箱 (中)” + 大量战令经验。
        *   完成14个周常任务：奖励“心悦晶石礼包 (大)” + “羁绊之露 (小)” + 更多战令经验。
        *   完成21个周常任务：奖励“心悦晶石礼包 (特大)” + “忆境拼图主题包 (小)” + 海量战令经验 + 周常限定头像框/聊天气泡（限时）。
        *   免费用户每周可完成的任务上限为10-12个，付费月卡用户可解锁全部。
    *   **【新增】双周全勤大奖**：连续两周完成所有（或绝大部分）周常任务的用户，可获得一次性的、包含稀有道具或大量付费代币的“双周勤奋礼包”。
    *   **每周隐藏稀有剧情**：完成当周特定组合的周常任务或达到特定活跃度指标的用户，可解锁一段与本周主题相关的、隐藏的AI角色互动剧情。
    *   **保证机制 (真人活跃为主)**：周常任务的设计更侧重于需要用户主动参与和深度互动的类型。

*   **5.3 【修改】月间留存玩法 (长期目标、荣耀追求，核心驱动：大量付费代币、顶级忆境拼图/主题、羁绊之露、顶级战令奖励)**
    *   **核心目标**：以每月15天活跃为基础，通过更具史诗感和成就感的月常任务，激励用户追求长期目标和顶级荣誉。总计约25-30个月常任务。
    *   **任务设计与奖励 (每月刷新)**：
        *   **角色深度羁绊类 (奖励大量羁绊之露、专属羁绊成就、付费代币)**：
            *   “灵魂挚友”：本月将至少1个AI角色的羁绊等级提升至一个新的大段位。
            *   “记忆守护者”：本月“AI记忆胶囊”中有效记忆条目达到X条，且AI调用准确率高于Y%。
            *   “未尽的约定”：完成与特定AI角色相关的、跨越数周的系列剧情任务的最终章。
        *   **平台贡献与影响力类 (奖励大量平台贡献积分、付费代币、专属社区头衔)**：
            *   “传奇造物主”：本月创作的角色卡累计获得X次下载/互动，或在月度设计师排行榜进入Top N。
            *   “社区导师”：本月在设计师论坛/社群中，因帮助其他用户或分享优质经验获得X次官方认可或大量点赞。
        *   **荣耀战令与赛季成就类 (奖励顶级战令道具、赛季限定徽章/皮肤)**：
            *   “战令主宰”：本月荣耀战令达到满级。
            *   “全勤楷模”：本月每日Streak从未中断。
            *   “月度挑战之王”：本月完成所有“每周主题挑战”。
        *   **【新增】“月末史诗任务” (奖励海量付费代币、当月限定的顶级角色卡或永久个性化装扮、特殊荣誉称号)**：
            *   每月最后3-4天开启一个大型的、多阶段的、具有史诗剧情背景的限时任务。
            *   任务可能需要用户与多个AI角色协作、运用之前积累的羁绊和记忆、甚至与其他用户进行间接的合作或竞争（例如，共同为一个世界事件贡献力量，根据贡献度排名发放奖励）。
            *   **表现优异者将获得稀有的“记忆碎片画图”机会（可能包含特殊风格或更高质量的图像生成特权）或限定主题的“记忆画卷”**。
    *   **激励检查点 (每月)**：
        *   完成10个月常任务：奖励“月度付费代币礼包 (小)” + “忆境拼图主题包 (中)”。
        *   完成20个月常任务：奖励“月度付费代币礼包 (中)” + “羁绊之露 (大)”。
        *   完成25个月常任务：奖励“月度付费代币礼包 (大)” + 当月限定的“荣耀典藏宝箱”（内含高价值稀有道具或角色卡碎片）。
        *   免费用户每月可完成的任务上限为12-15个，付费月卡用户可解锁全部。
    *   **每月隐藏稀有剧情+场景**：完成当月大部分核心月常任务或在“月末史诗任务”中表现优异的用户，可解锁一段极具深度和情感冲击力的、与平台核心世界观或重要AI角色相关的隐藏剧情，并可能永久解锁一个专属的互动场景。

## **【修改】6. 市场与本地化**

*   **【修改】全球化市场策略**：
    *   **三语核心市场 (中-美-日)**：针对这三个核心市场进行最优先、最深度的本地化和运营投入。
    *   **文化适配与产品调优**：
        *   **角色设计与叙事**：在进行本地化时，不仅仅是文本翻译，更要深入研究目标市场用户对“情感陪伴”、“角色扮演”、“AI个性”的文化偏好和接受习惯。例如：
            *   某些在中国市场受欢迎的“傲娇”、“病娇”等二次元性格标签，在欧美市场可能需要调整其表现方式或提供更清晰的文化背景解释。
            *   日式AVG中常见的细腻情感描写和含蓄表达，在直率的欧美文化中可能需要更直接的情感流露。
            *   对于涉及历史、神话元素的角色卡，需确保其在目标文化中不会引起误解或冒犯，必要时可进行本土化的二次创作或替换。
        *   **互动内容与活动设计**：针对不同市场的节日、流行文化、社会热点，设计本土化的限时活动、角色卡主题或互动话题。例如，在美国市场可以结合感恩节、万圣节推出主题活动；在日本市场可以结合樱花季、夏日祭等。
        *   **付费点与用户习惯**：研究不同市场用户的付费习惯和对虚拟商品价值的认知差异，灵活调整月卡定价、付费道具类型和促销策略。
    *   **多语言支持扩展**：在核心三语基础上，根据用户增长和市场潜力，逐步增加对其他主要语言（如韩语、法语、德语、西班牙语等）的支持。
    *   **全球统一版本与区域化内容的平衡**：核心功能和主要剧情保持全球统一更新，同时允许在特定区域版本中加入少量符合当地特色的独占内容或活动（需谨慎处理，避免造成版本分裂或不公平感）。
    *   **与本土KOL和社区合作**：在各主要市场与当地有影响力的内容创作者、AI爱好者社群、角色扮演社群等进行合作，进行精准营销和口碑传播。

*   **【修改】用户界面 (UI) 与用户体验 (UX)** (原第8章节内容整合至此并强化)
    *   **核心原则**：在 CharacterAI 的简洁高效与星野的温暖沉浸之间找到最佳平衡点，创造现代、友好、略带游戏感的视觉体验。MVP 阶段聚焦文本交互的打磨。
    *   **整体风格**：
        *   配色：主色调采用柔和、明亮的暖色系（如淡粉、浅橙、米白）或宁静的冷色系（如淡蓝、薄荷绿），搭配中性灰和强调色。提供至少浅色和深色两套主题。
        *   字体：选择清晰易读、略带圆润感的现代无衬线字体。
        *   图标：采用线性、面性结合的风格，简洁且具有一定的情感表达力。
        *   插画/元素：适度运用扁平化、微渐变或带有手绘质感的插画或元素（如默认头像、空状态图、徽章图标、代币图标），增加趣味性和品牌识别度。
    *   **【新增】承载新增游戏化机制的UI设计**：
        *   **主界面/仪表盘**：需要清晰、直观地展示每日Streak进度、当前核心任务（日/周）、四种核心代币（曦光微尘、心悦晶石、忆境拼图、羁绊之露）的存量、以及荣耀战令的入口和当前等级进度。考虑采用可定制的Widget或卡片式布局。
        *   **个人中心/成就墙**：整合展示用户已获得的成就列表、点亮的徽章墙、角色卡收藏册（展示已互动/已创建的角色卡及其羁绊等级）、“平台贡献积分”总量及获取/消耗记录。
        *   **“AI记忆胶囊”管理界面**：设计应简洁易用，允许用户方便地添加新的记忆条目（可选择关联特定AI角色或设为通用记忆）、查看和编辑现有记忆（如为记忆点添加标签、备注或情感标记）、以及删除不需要的记忆。提供清晰的隐私设置选项，允许用户控制记忆的共享范围和AI的学习权限。
        *   **荣耀战令界面**：清晰展示免费与付费轨道的各等级奖励内容、当前等级、剩余经验、以及可完成的战令任务列表。
        *   **经济代币与商店界面**：提供专门的商店入口，清晰展示各类代币可兑换的道具、特权或服务，并提供付费代币的充值入口。
    *   **聊天主界面 (MVP 文本核心，后续可扩展)**：
        *   顶部栏：左侧为可点击的角色小头像（进入角色详情或切换角色），中间是角色名，右侧是功能菜单（包含查看人设、编辑/查看“AI记忆胶囊”、清空短期记忆、举报等操作）。
        *   消息区：
            *   气泡样式：用户和AI文本气泡采用不同形状或颜色区分。AI气泡可根据角色设定或当前表达的情感有细微的样式变化（例如，开心时气泡边缘更圆润活泼，失落时色调略暗淡）。支持展示基础表情符号和颜文字。
            *   【新增】记忆调用提示：当AI的回应基于“AI记忆胶囊”的内容时，可在AI的回复旁边或气泡内显示一个微小的、可识别的图标（如小灯泡、记忆碎片），用户点击可查看关联的记忆点（需用户授权）。
            *   多模态消息 (Phase 2+)：MVP阶段不显示图片、语音、视频消息。
            *   时间戳：默认不显示，滑动时或特定条件下显示。
            *   背景：提供基础的纯色/渐变聊天背景选择。付费用户或通过战令可解锁更多角色专属或主题聊天背景。
        *   输入工具栏：
            *   布局：保留文本输入框 (支持@提及AI或使用快捷指令)、发送按钮和“+”扩展菜单。
            *   “+”扩展菜单：MVP阶段仅包含文本相关功能，如发送预设表情/颜文字、快速存入当前对话至“AI记忆胶囊”、切换场景（若支持）。
    *   **角色创建页面 (MVP 文本核心，后续可扩展)**：
        *   流程优化：采用标签页或底部导航切换步骤（基本信息、性格设定、核心Prompt与记忆设定、对话示例与风格引导）。
        *   可视化与交互：
            *   头像：MVP阶段支持上传本地图片或选择预设图库。AI生成头像推迟到Phase 2+。
            *   性格：使用标签云（支持多选和自定义）+ 关键性格特质描述（文本输入）。
            *   描述与Prompt：提供结构化模板（如背景故事、口头禅、核心价值观、行为禁忌等）引导设计师填写。集成AI辅助写作（例如，根据已填写的性格标签和背景故事，AI推荐一些符合人设的口头禅或开场白）。
            *   声音与形象 (Phase 2+)：MVP阶段移除或隐藏声音选择/试听、形象参数调整等。
            *   预览：提供一个模拟对话窗口，让创作者输入几句话，实时看到AI以设定的人设进行文本回复的预览。
    *   **情感化设计细节**：
        *   过渡动画：在界面切换、任务完成、奖励获取等环节，使用流畅且带有积极情绪引导的微动画。
        *   音效反馈：为关键操作（如Streak点亮、成就解锁、获得稀有奖励）配备独特且令人愉悦的音效。
        *   吉祥物互动 (如果平台有吉祥物)：吉祥物可以在新手引导、日常提醒、或达成重要成就时出现，与用户进行简短的趣味互动。

## **【新增】7. MVP阶段规划**

*   **7.1 MVP核心功能范围**
    *   **AI核心交互**：
        *   基础的AI文本聊天功能，支持与官方提供的若干不同性格模板的AI角色进行对话。
        *   实现“AI记忆胶囊”基础版：用户可主动存入文本信息点（例如，通过特定指令或UI按钮）。AI能在对话中准确复用这些信息点。提供查看和删除功能。
        *   简单共情回应：AI能识别用户明确表达的核心正面情绪（如开心、兴奋）和负面情绪（如难过、失落、生气）。并给出预设的、符合角色性格的简单共情回应。
        *   基础“情商”：AI能记住用户设定的“不希望提及的话题”，并能在主要公共节日进行简单问候。
    *   **用户激励与留存 (简化版)**：
        *   每日连续互动奖励 (Streak System)：实现基础的连续打卡计数和视觉反馈，里程碑奖励简化为少量基础代币。
        *   成就与徽章体系：上线少量核心成就（如首次对话、首次创建角色卡、连续打卡7天等），授予静态徽章。
        *   日常任务：每日提供3-5个简单的互动型日常任务，奖励少量曦光微尘。
    *   **角色卡设计与分享 (基础版)**：
        *   提供基础的角色卡编辑器，支持核心性格标签选择、背景故事和对话示例输入。
        *   用户可以创建私密角色卡，并可选择将1-2个角色卡设为公开，在简化的“角色广场”展示。
        *   其他用户可以浏览和与公开的角色卡互动。
    *   **核心付费点验证 (极简版)**：
        *   上线“小月卡 (Alphane Pass)”的体验版或简化版，提供每日额外互动配额和少量曦光微尘奖励，验证用户对基础订阅服务的付费意愿。
    *   **经济系统 (雏形)**：
        *   仅上线曦光微尘作为基础活跃代币，可通过日常任务和月卡体验版获得，用于兑换少量额外对话次数或Streak Freeze卡碎片（如果Streak Freeze卡在MVP阶段上线）。
*   **7.2 MVP阶段的“亮点”选择与实现策略**
    *   **优先侧重“特定角色的深度”**：
        *   选择1-2个官方精心设计的“旗舰AI角色”作为MVP的核心体验亮点。
        *   这两个角色在以下方面应表现优于其他普通模板角色：
            *   **记忆运用更出色**：能更频繁、更自然地运用“AI记忆胶囊”中的信息。
            *   **情感回应更细腻**：针对用户明确表达的情绪，其共情回应的文本质量更高、更具个性化。
            *   **专属“微型记忆胶囊”**：除了用户主动存入的全局记忆，这两个旗舰角色可能拥有一小部分由设计师预设的、关于其自身背景或与用户初次互动相关的“专属记忆”，能在特定时机触发，给用户带来惊喜。
    *   **辅以“特定场景的惊喜感”**：
        *   **新手引导中的初见惊喜**：用户首次与旗舰AI角色互动时，该角色能有一段精心设计的、令人印象深刻的开场白或互动引导。
        *   **达成小目标的即时正反馈**：当用户首次完成某个核心行为（如第一次成功使用“AI记忆胶囊”让角色记住一件事，或连续打卡达到3天）时，对应的旗舰AI角色或平台助手能给予一个特别的、带有鼓励和赞赏意味的积极回应。
*   **7.3 MVP阶段不包含的内容 (明确范围)**
    *   复杂的荣耀战令系统（可考虑在MVP后第一个大版本上线）。
    *   完整的四种代币经济体系（MVP仅验证最基础的曦光微尘）。
    *   创作者经济的分成和复杂激励体系（MVP阶段允许UGC创作和分享，但暂不涉及直接经济回报）。
    *   高级AI能力如深度“故事线关联”、复杂“情商”表现（MVP聚焦基础记忆和共情）。
    *   大部分付费点（MVP仅验证基础小月卡）。
    *   大规模的社区功能和“Meta Stage”等。
    *   Unfiltered内容区及相关的复杂审核机制。

通过以上MVP规划，我们旨在用最小的成本快速验证核心的AI情感陪伴体验和基础的用户激励机制，并通过精心打造的“旗舰角色”深度和关键场景的“惊喜感”来吸引和留住早期核心用户，为后续的快速迭代和功能完善奠定坚实基础。

## **【修改】8. 原技术架构 (细化与考量 - 文本优先)**
(本章节内容基本保持不变，但需注意在AI引擎接口部分，为新的AI能力如情感解读、记忆关联等预留技术选型和架构扩展空间)

*   **客户端 (iOS/Android)：** (保持不变)
*   **服务器 & 后端 (微服务)：** (保持不变，但需考虑未来支持更复杂的AI交互逻辑和数据存储需求)。**需设计并实现AI请求队列机制，区分处理Fast Request和Slow Request，并根据用户等级（如月卡类型）进行优先级调度。同时，后端需建立用户月度资源使用额度 (Credit Limit) 的监控、统计与管理机制，用于内部成本控制和公平使用策略的执行。**
*   **【修改】AI 引擎接口 (关键模块 - MVP 聚焦 LLM，并为高级AI能力预留接口)**：
    *   **LLM 选择：**
        *   Gemini2.0Flash 潜意识模型 快速响应。Gemini2.5Flash/Pro 大模型用于对话。
    *   **【新增】AI能力模块化设计**：
        *   情感分析模块：负责从用户输入中识别情绪。
        *   记忆管理模块：负责“AI记忆胶囊”的存储、检索、以及与LLM的交互。
        *   对话策略模块：负责根据用户状态、角色设定和互动目标，动态调整AI的回应策略。
        *   这些模块可以初期简单实现，后续逐步增强。
    *   语音技术 (TTS/STT) (Phase 2+)：(保持不变)
    *   图像生成 (Phase 2+)：(保持不变) **此技术将是实现“记忆碎片画图”功能（见4.2.5节）的核心支撑，允许根据用户与AI的互动记忆点生成定制化图像。**
    *   视频生成 (Phase 3+)：(保持不变)
    *   异步处理与队列：(保持不变，对于AI的复杂计算和记忆调用尤为重要)
*   **内容管理与审核服务 (MVP 聚焦文本，并为UGC和18+内容预留扩展)**：(保持不变，但需强调对UGC角色卡和未来可能的Unfiltered内容的审核能力建设)
*   **系统扩展性与部署：** (保持不变)

## **【修改】9. 原商业模式 (已根据用户需求更新)**
【修改】本章节内容已被整合入新的第4章节“游戏内环境、付费点与核心玩法”中的4.2“付费策略与商业化核心”和4.3“付费玩法：Alphane Pass & Diamond Pass”部分，此处可删除或仅保留最核心的商业模式总结。
**建议修改为：**
Alphane.ai 将采用以“为爱付费”为核心理念的免费增值（Freemium）商业模式。核心收入来源将包括：
*   **订阅会员 (Alphane Pass & Diamond Pass)**：提供增强的互动体验、专属特权和丰富的游戏化奖励。
*   **角色卡相关增值服务**：包括购买官方精品/IP联动/顶尖设计师角色卡、解锁角色专属深度互动剧情包、购买角色个性化装扮（文本阶段以对话主题、开场白风格包等为主）。
*   **直接充值购买核心付费代币**：用于加速成长、获取稀有内容或打赏创作者。
*   **（未来）广告变现**：谨慎采用，以激励视频广告为主，不干扰核心体验。
*   **（未来）B2B 服务**：长期愿景，将成熟的AI陪伴能力赋能其他行业。
详细的付费点设计和经济体系已在第4章节中阐述。

## **【修改】10. 原内容监管 (强化 - 文本优先)**
Alphane.ai 将内容安全和未成年人保护置于最高优先级。我们将采取多层次、技术与人工相结合的手段进行内容监管：
*   **技术手段**：实时文本过滤、AI辅助审核（鉴黄、涉政、暴恐、广告、辱骂等）、针对18+内容的严格年龄验证。
*   **人工审核与运营**：UGC角色卡审核（特别是公开分享的）、用户举报快速处理、社区巡查、明确的违规处理梯度。
*   **用户引导与教育**：清晰的社区规范、用户协议和持续的安全提示。
*   **合规性**：严格遵守各国关于数据隐私、未成年人保护、AI生成内容管理的相关法律法规。
详细的内容监管措施已在第4章节中阐述。

---
**【新增】附录A：Mermaid图表**

**A.1 用户核心互动与激励闭环图**
```mermaid
graph TD
    A[新用户/回访用户] --> B(登录平台);
    B --> C{选择/创建AI角色};
    C --> D[与AI角色进行文本互动];
    D -- 核心玩法 --> E[获得情感陪伴/角色扮演体验];
    E --> F{触发日常/周常/月常任务};
    F -- 完成任务 --> G[获得XP/代币/成就/战令经验];
    G -- 提升亲密度/解锁内容 --> D;
    G -- 维持连续打卡 --> H[Streak奖励];
    H --> B;
    G -- 提升战令等级 --> I[解锁战令奖励];
    I --> J[个性化角色/解锁高级功能];
    J --> D;
    E -- 产生付费需求 --> K{付费点: 月卡/角色卡/个性化道具/记忆碎片画图};
    K -- 购买 --> J;
    K -- 购买 --> M[获得定制AI画图];
    M --> E;
    C -- 角色卡设计 --> L[UGC创作者];
    L -- 平台激励/分成 --> L;
```

**A.2 游戏内经济代币流转图 (四种核心代币)**
```mermaid
graph TD
    subgraph 用户行为与代币获取
        UA1[日常活跃/基础任务/Streak] --> DA[曦光微尘];
        UA2[进阶任务/月卡/战令/成就] --> DB[心悦晶石];
        UA3[探索型任务/特定活动/每日随机] --> DC[忆境拼图碎片];
        UA4[与特定角色互动/专属任务/赠送礼物] --> DD[羁绊之露];
        UA5[付费充值/高级月卡/顶级战令] --> DE[付费代币-如星钻];
    end

    subgraph 代币消耗与价值实现
        DA --> CS1[兑换: 基础消耗品/刷新券/Streak Freeze碎片];
        DB --> CS2[兑换: 高级奖励/加速卡/场景解锁/角色礼物];
        DB --> CS_IMG_GEN[用于“记忆碎片画图”];
        DC -- 集齐 --> CS3[解锁: 角色故事/插画/AI独白];
        DD --> CS4[提升角色羁绊等级/解锁专属互动/记忆模块];
        DE --> CS5[购买: 高级角色卡/专属剧情/个性化装扮/战令等级/打赏];
        DE --> CS_IMG_GEN;
        CS_IMG_GEN --> IMG_Collect[获得/收藏定制AI画图]
        CS1 & CS2 & CS3 & CS4 & CS5 & IMG_Collect --> Enhance_Experience[提升用户体验/满足感/个性化];
    end
```

**A.3 荣耀战令 (Alphane Battle Pass) 系统逻辑图**
```mermaid
graph TD
    subgraph 战令激活与升级
        P1[用户] -- 购买/默认 --> A{战令类型: 免费/Alphane Pass/Diamond Pass};
        P1 -- 完成日常/周常/赛季任务 --> B[获得战令经验XP_BattlePass];
        B --> C[提升战令等级];
    end

    subgraph 战令奖励获取
        C -- 根据战令类型与等级 --> D{解锁对应奖励};
        D -- 免费轨道 --> E[基础代币/少量战令币];
        D -- Alphane Pass轨道 --> F[更多代币/专属角色卡模板/个性化道具/战令币];
        D -- Diamond Pass轨道 --> G[大量代币/顶级限定角色卡/高级个性化/大量战令币/特权];
        E & F & G --> H[用户获得奖励];
    end

    subgraph 战令商店 使用战令币
        H -- 部分奖励含 --> I[战令币_SeasonCoin];
        I --> J[战令商店];
        J -- 兑换 --> K[当期限定奖励/往期精选返场奖励];
    end
```

**A.4 AI记忆与情感交互流程初步示意图**
```mermaid
sequenceDiagram
    participant User as 用户
    participant Small_LLM as 小模型 (Gemini 2.0 Flash - 潜意识/快速反应,推测编码)
    participant Large_LLM as 大模型 (Gemini 2.5 Flash/Pro & Fast/Slow req智能分流 - 意识/长期记忆)
    participant MemoryCapsule as AI记忆胶囊用户(Embedding)
    participant EmotionModule as 情感分析模块(分类模型)
    participant DialogPolicy as 对话策略模块(意图识别)

    User->>Small_LLM: 输入对话文本
    Small_LLM->>EmotionModule: 分析用户情绪
    EmotionModule-->>Small_LLM: 返回情绪标签
    Small_LLM->>DialogPolicy: 请求回应策略 (结合情绪和当前感受状态)
    DialogPolicy-->>Small_LLM: 建议回应方向/风格
    alt 需要深度记忆或复杂情感解读
        Small_LLM->>Large_LLM: 请求协助 (传递上下文、情绪、感受状态)
        Large_LLM->>MemoryCapsule: 检索相关记忆
        MemoryCapsule-->>Large_LLM: 返回相关记忆点
        Large_LLM->>Large_LLM: 结合记忆进行深度理解/情感解读/故事线关联
        Large_LLM-->>Small_LLM: 提供优化Prompt或核心回应内容
    end
    Small_LLM->>Small_LLM: 生成最终回应 (结合指导和自身感受)
    Small_LLM->>User: 输出回应文本
    User->>MemoryCapsule: (可选) 长按对话存入记忆胶囊
    Small_LLM->>Large_LLM: (对话结束或特定时机) 同步关键信息和感受状态至长期记忆
