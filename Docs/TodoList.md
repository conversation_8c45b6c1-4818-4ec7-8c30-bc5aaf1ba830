# Alphane.ai 项目详细开发 To-Do List (V2.0 - 基于Plan.md)

**核心原则：** 采用敏捷开发，快速迭代。**MVP 阶段将专注于提供高质量的文本交互体验，并融入核心的AI记忆与情感交互亮点 (Plan.md 7)。** 后续逐步完善并引入多模态功能、深度游戏化及创作者生态。

**(注：P0/P1/P2/P3 代表优先级，P0 最高。时间预估为大致参考，具体需根据团队实际情况调整。任务项后的括号内数字如 (Plan.md X.Y.Z) 表示其主要依据的 Plan.md 章节。)**

---

## **Phase 0: 准备阶段**

*   **1.1 产品与设计定义 (P0)**
    *   [ ] (产品) 基于 `Plan.md V2.0` 确认并冻结 **文本核心 MVP** 版本的详细需求文档 (PRD)。(Plan.md 7)
    *   [ ] (设计) 完成所有 MVP 核心界面（**聚焦文本交互，参考 Plan.md 4.7.1, 4.7.3, 6.2, 7.1**）的高保真原型设计 (Figma/Sketch)。(Plan.md 4.7.1, 4.7.3, 6.2, 7.1)
    *   [ ] (设计) 确定并输出基础 UI 规范 V1.0（配色、字体、核心图标、间距规范，参考 Plan.md 6.2 的风格指导）。
    *   [ ] (产品/设计) 组织 PRD (基于 Plan.md V2.0) 和设计稿评审会，确保团队理解一致。
*   **1.2 项目管理与规划 (P0)**
    *   [ ] (项目经理/负责人) 选定并搭建项目管理工具 (如 Jira/ClickUp)，创建项目空间。
    *   [ ] (项目经理/负责人) 将 Phase 0 & Phase 1 (文本 MVP) 的任务录入项目管理工具，分配负责人和初步排期。
    *   [ ] (项目经理/负责人) 制定详细的 Phase 1 甘特图或开发计划。
*   **1.3 团队组建与启动 (P0)**
    *   [ ] (负责人) 确认核心开发团队成员（前后端、AI、测试、设计）。
    *   [ ] (负责人) 明确各成员职责和协作流程（如代码规范、Code Review 流程、沟通机制）。
    *   [ ] (全体) 召开项目启动会，同步目标 (基于 Plan.md V2.0)、计划和期望。
*   **1.4 技术选型与基建 (P0)**
    *   [ ] (技术负责人/架构师) 完成核心技术栈选型并文档化 (参考原 TodoList，并结合 Plan.md 进行确认)：
        *   [ ] APP前端框架 (Flutter)。
        *   [ ] Web前端框架 (React+Next.js)  
        *   [ ] 后端语言与框架 (Go+Typescripts)。
        *   [ ] 数据库 (PostgreSQL)。
        *   [ ] 对象存储 (GCS)。
        *   [ ] 部署方案 (CI/CD)。
        *   [ ] 云服务商 (GCP)。
    *   [ ] (技术负责人/架构师) 确定 MVP 阶段使用的核心第三方 AI 服务 API:
        *   [ ] **LLM: Gemini 2.0 Flash (潜意识/快速反应) & Gemini 2.5 Flash/Pro (对话/意识/长期记忆)** (Plan.md 2.5, 8.3)。
        *   [ ] **文本内容审核 API** (Plan.md 10)。
    *   [ ] (DevOps/后端) 搭建基础的代码仓库 (Git)，配置 CI/CD 初始流水线。
    *   [ ] (DevOps/后端) 申请并配置云资源账号和基础环境（如 VPC、数据库实例）。

---

## **Phase 1: MVP 核心功能开发 (文本优先) (严格对齐 Plan.md 7)**

*   **2.1 后端开发 (P1)**
    *   **基础框架与数据库:**
        *   [ ] (后端) 初始化后端项目结构，引入基础依赖。
        *   [ ] (后端) 配置数据库连接。
        *   [ ] (后端) 设计并创建 MVP 所需的核心数据表 (用户、角色、聊天记录、**基础版AI记忆胶囊表(用户ID, 角色ID, 记忆文本, 创建时间)**、**基础版用户激励数据表 (用户ID, Streak计数, 上次打卡日期; 用户ID, 成就ID, 获得时间)**)。(Plan.md 7.1)
    *   **用户系统:**
        *   [ ] (后端) 实现手机号/邮箱注册接口。
        *   [ ] (后端) 实现密码登录接口。(P0)
        *   [ ] (后端) 实现 JWT 生成与验证中间件。
        *   [ ] (后端) 提供获取用户基本信息的接口。(P0)
        *   [ ] (后端) 实现用户头像上传接口（若 MVP 支持）。
        *   [ ] (后端) 实现邀请码生成与验证基础接口 (若MVP采纳邀请码制度，包含邀请码库、使用状态、邀请关系记录)。(Plan.md 4.2.1) (P2)
    *   **角色管理 (MVP 基础版):** (Plan.md 7.1.3)
        *   [ ] (后端) 实现创建官方/测试角色的内部接口或脚本（包含角色ID, 名称, **核心性格标签(文本数组)**, **背景故事(长文本)**, **对话示例(多组成对问答)**, **专属“微型记忆胶囊”内容(键值对或文本列表)**，特别是为1-2个旗舰AI角色设计更优的Prompt和专属“微型记忆胶囊”逻辑)。(Plan.md 3.1.1, 7.1.3, 7.2.1)
        *   [ ] (后端) 实现获取角色列表的接口 (MVP 仅少量官方角色，支持分页)。
        *   [ ] (后端) 实现获取单个角色基础信息（文本设定为主）的接口。
        *   [ ] (后端) 实现用户创建私密角色卡接口 (MVP 限制数量)。
        *   [ ] (后端) 实现用户将1-2个角色卡设为公开的接口。
        *   [ ] (后端) 实现角色卡公开/私密状态切换接口。(Plan.md 7.1.3) (P1)
    *   **聊天会话 (MVP 文本核心):** (Plan.md 7.1.1)
        *   [ ] (后端) 设计聊天会话及消息数据模型 (会话ID, 用户ID, 角色ID, 消息ID, 发送方, 消息类型(文本), 内容, 时间戳)。
        *   [ ] (后端) 实现创建新会话记录接口。
        *   [ ] (后端) 实现接收用户文本消息接口 (存储消息，调用 AI 引擎处理，存储 AI 回复)。(P0)
        *   [ ] (后端) 实现分页获取文本历史消息接口。(P0)
        *   [ ] (后端) **实现“AI记忆胶囊”基础版后端逻辑**：(Plan.md 2.2, 7.1.1, 7.2.1)
            *   [ ] (后端) 用户主动存入文本信息点接口 (接收用户ID, 角色ID, 记忆文本)。
            *   [ ] (后端) AI 调用记忆胶囊信息逻辑 (在构建Prompt时，根据用户ID和角色ID检索记忆，**旗舰角色优先/更智能地运用这些记忆**)。
            *   [ ] (后端) 用户查看和删除记忆胶囊条目接口 (根据用户ID, 角色ID, 记忆ID)。
    *   **AI 引擎集成 (MVP 文本核心):** (Plan.md 2, 7.1.1, 7.2)
        *   [ ] (后端/AI) 封装调用 Gemini LLM API 的服务模块 (处理双模型协同基础逻辑：**默认使用Gemini 2.0 Flash，特定场景或旗舰角色可考虑Gemini 2.5 Flash的初步调用尝试**，区分Fast/Slow Req雏形：**MVP阶段可能简化为所有请求统一处理，但预留接口参数**)。(Plan.md 2.5, 8.2)
        *   [ ] (后端/AI) 设计 MVP Prompt 模板：(Plan.md 7.1.1)
            *   [ ] 包含系统提示 (如“你是一个乐于助人的AI伙伴”)、角色人设信息 (从角色库获取)、对话历史 (最近N轮)。
            *   [ ] **集成简单共情回应逻辑** (通过关键词或简单NLP识别用户核心正负面情绪如“开心”、“难过”，调用预设回应模板库中的对应回应，**旗舰角色的回应模板更丰富、更个性化**)。(Plan.md 2.1 MVP, 7.2.1)
            *   [ ] **集成基础“情商”逻辑** (维护用户不希望话题列表，在生成回复前检查；根据系统时间在主要公共节日如新年主动发送简单问候语)。(Plan.md 2.3 MVP)
            *   [ ] **为1-2个旗舰AI角色设计更优的Prompt和专属“微型记忆胶囊”逻辑**。(Plan.md 7.2.1)
        *   [ ] (后端) 在消息接口中集成 LLM 调用逻辑 (异步处理)。
    *   **用户激励与留存 (MVP 简化版):** (Plan.md 1.5, 7.1.2)
        *   [ ] (后端) 实现 **每日连续互动奖励 (Streak System)** 基础后端逻辑 (用户登录或完成首次有效互动后更新Streak计数和上次打卡日期；判断是否达到3天、7天等里程碑，发放少量曦光微尘)。(Plan.md 1.5.1, 7.1.2)
        *   [ ] (后端) 实现 **成就与徽章体系** 基础后端逻辑 (定义MVP核心成就列表如：“首次对话”、“创建首个角色卡”、“连续打卡7天”；用户达成条件后记录成就，授予静态徽章ID)。(Plan.md 1.5.2, 7.1.2)
        *   [ ] (后端) 实现 **日常任务** 基础后端逻辑 (每日系统随机或固定生成3-5个简单互动型任务如：“与AI完成3轮对话”、“向记忆胶囊存入1条记忆”；记录任务完成状态，发放少量曦光微尘)。(Plan.md 5.1, 7.1.2)
    *   **经济系统 (MVP 雏形):** (Plan.md 7.1.5)
        *   [ ] (后端) 实现 **曦光微尘** 代币的获取 (日常任务完成、月卡体验版每日领取) 与基础消耗逻辑 (如兑换“再来5条对话”次数包 - 若支持，或兑换Streak Freeze卡碎片 - 若MVP包含)。(Plan.md 4.3.3, 7.1.5)
    *   **付费点验证 (MVP 极简版):** (Plan.md 4.2.1, 7.1.4)
        *   [ ] (后端) 实现 **“小月卡 (Alphane Pass)”体验版/简化版** 的基础权益判断逻辑 (用户购买/激活后，标记月卡状态及有效期；接口根据状态提供每日额外N条互动配额、每日自动发放M点曦光微尘)。(Plan.md 4.2.1, 7.1.4)
        *   [ ] (后端) (占位) 支付对接和订单管理基础接口 (MVP阶段可能为**后台手动开通月卡权限或使用模拟支付回调**，但需设计好订单表结构：订单ID, 用户ID, 产品ID, 金额, 状态, 创建时间)。
    *   **内容审核 (文本):** (Plan.md 10)
        *   [ ] (后端) 封装调用第三方文本内容审核 API 的服务模块。
        *   [ ] (后端) 在接收用户消息和返回 AI 回复时，加入内容审核调用。
        *   [ ] (后端) 实现基础的违规内容处理逻辑。
    *   **部署:**
        *   [ ] (后端/DevOps) 编写 Dockerfile，构建服务镜像。
        *   [ ] (后端/DevOps) 配置 MVP 后端服务的基础部署。
        *   [ ] (后端/DevOps) 将 MVP 后端部署到测试环境。
        *   [ ] (后端/DevOps) 配置基础的日志收集和监控。
*   **2.2 前端开发 (P1)**
    *   **基础框架与导航:**
        *   [ ] (前端) 初始化前端项目。
        *   [ ] (前端) 搭建基础 Tab 导航 (首页含角色广场、聊天列表(预留)、个人中心)。
        *   [ ] (前端) 配置路由管理。
    *   **网络层:**
        *   [ ] (前端) 封装统一的 HTTP 请求模块。
        *   [ ] (前端) 处理 API 请求封装、JWT Token、错误处理。
    *   **核心界面 (MVP 文本核心):**
        *   [ ] (前端) 实现登录/注册界面的开发和逻辑对接 (包含输入校验、API调用、错误提示、成功跳转)。
        *   [ ] (前端) 实现首页界面 (角色广场简化版，**卡片式展示官方和公开UGC角色卡列表，包含角色头像、名称、核心标签；支持点击进入角色详情页**)。(Plan.md 4.1.1, 7.1.3)
        *   [ ] (前端) 实现角色详情页 (展示角色文本基础信息，“开始聊天”按钮，**点击后创建或进入与该角色的聊天会话**)。
        *   [ ] (前端) 实现聊天界面 (文本消息为主，简化“+”菜单)：(Plan.md 4.7.3, 7.1.1)
            *   [ ] (前端) 文本消息发送功能 (输入框、发送按钮、调用发送消息API)。
            *   [ ] (前端) 聊天消息列表渲染 (区分用户/AI文本气泡，**AI回复旁显示记忆调用提示小图标 Plan.md 4.7.3**，支持滚动加载历史消息)。
            *   [ ] (前端) 加载历史文本消息。
            *   [ ] (前端) **实现“AI记忆胶囊”基础版前端交互** (聊天界面提供“存入记忆”按钮或长按消息选项；独立的记忆管理页面/弹窗，可查看和删除已存入的记忆条目)。(Plan.md 2.2, 7.1.1)
        *   [ ] (前端) 实现基础个人中心界面 (展示用户名、头像、退出登录功能，**醒目展示当前Streak天数、已获得的核心成就徽章列表**)。(Plan.md 7.1.2)
        *   [ ] (前端) 实现 **日常任务展示与状态更新界面 (简化版)** (列表展示今日可做任务、完成状态、可领取奖励提示)。(Plan.md 7.1.2)
        *   [ ] (前端) 实现 **“小月卡”体验版购买/状态展示界面 (简化版)** (展示月卡权益、购买按钮（MVP阶段可能为“激活体验”）、当前月卡状态及到期日)。(Plan.md 7.1.4)
        *   [ ] (前端) 实现 **曦光微尘数量展示与基础消耗场景交互 (简化版)** (如在特定功能点提示可使用曦光微尘兑换)。(Plan.md 7.1.5)
        *   [ ] (前端) **为旗舰AI角色设计独特的初见交互或积极反馈提示** (例如，首次与旗舰角色对话时，展示一段精心设计的欢迎动画和文本；当用户首次使用记忆胶囊功能并成功让旗舰角色记住信息后，该角色在后续对话中能有特别的提及或感谢)。(Plan.md 7.2.2)
        *   [ ] (前端) 实现邀请码输入界面 (若MVP采纳邀请码制度，包含输入框、提交按钮、结果反馈)。(Plan.md 4.2.1) (P2)
    *   **角色创建 (MVP 基础版):** (Plan.md 3.1.1, 7.1.3)
        *   [ ] (前端) 实现基础角色卡编辑器界面 (**表单形式输入核心性格标签、背景故事、对话示例**；提供保存为私密或设为公开的选项)。
        *   [ ] (前端) 对接创建私密角色卡和设为公开的接口。
    *   **打包:**
        *   [ ] (前端) 配置 App 打包所需的环境变量、图标、启动屏。
        *   [ ] (前端) 能够成功打包出 MVP 版本的 Android 和 iOS 安装包。
*   **2.3 UI/UX 设计 (P1)**
    *   [ ] (设计) 确认 MVP **文本核心**界面的设计稿，包含各种状态。(Plan.md 4.7.1)
    *   [ ] (设计) 输出 MVP 界面的切图资源和详细设计标注。
    *   [ ] (设计) 与前端开发紧密协作，进行 UI 走查。
*   **2.4 测试 (P1)**
    *   [ ] (测试/开发) 编写核心**文本交互**流程、**AI记忆胶囊(存入、读取、删除、旗舰角色应用)**、**基础激励(Streak计数与奖励、核心成就获取、日常任务完成与奖励)**的详细测试用例。
    *   [ ] (测试) 执行 MVP 功能的手动测试。
    *   [ ] (测试) 在主流机型上进行基本兼容性测试。
    *   [ ] (全体) 进行内部体验测试（Dogfooding），收集反馈。
    *   [ ] (测试) 针对1-2个旗舰AI角色的**专属“微型记忆胶囊”逻辑和更细腻的情感回应**进行专项测试。(Plan.md 7.2.1) (P1)
    *   [ ] (测试) 针对新手引导中的**初见惊喜和特定小目标达成的积极反馈**进行体验测试。(Plan.md 7.2.2) (P1)

---

## **Phase 2: 核心体验深化与游戏化扩展 (预估 6-8 周)**

*   **3.1 技术选型与调研 (P1 - Phase 2 开始时)**
    *   [ ] (技术负责人/AI) 完成 TTS, STT, 图像生成 API/模型的最终选型（Plan.md 8.3 建议 TTS 参考 **GPT-SoVITS v4 开源模型**，T2I 参考 **HiDream 开源模型** 或类似方案；**评估并发能力、响应延迟、成本控制、多语言支持情况**）。
    *   [ ] (技术负责人/后端) 确定向量数据库方案 (PGVector/Milvus等) 用于增强记忆系统 (RAG)，**设计初步的文本嵌入策略和相似度检索算法**。(Plan.md 2.2, 2.4)
    *   [ ] (技术负责人/后端) 确定实时通信方案 (WebSocket/SSE) 用于 AI 回复流式输出。
    *   [ ] (技术负责人/后端) 确定消息队列和异步任务处理方案（如图像生成）。
    *   [ ] (技术负责人/AI) 调研 Gemini 2.0 Flash 与 2.5 Flash/Pro 之间协同工作的具体技术方案，**包括API调用方式、上下文传递、成本优化策略、以及Fast/Slow Req的初步分流逻辑设计**。(Plan.md 2.5, 8.3) (P1)
*   **3.2 后端开发 (P2)**
    *   **AI能力增强:**
        *   [ ] (后端/AI) **实现双LLM模型协同机制的进一步完善** (Gemini 2.0 Flash & 2.5 Flash/Pro，**明确小模型处理快速反应/潜意识，大模型处理深度对话/意识/长期记忆的职责分工与协同API调用流程**)。(Plan.md 2.5)
        *   [ ] (后端/AI) **实现AI请求队列机制** (区分Fast Req/Slow Req，**根据用户月卡等级（如大月卡优先Fast Req）、请求类型（如记忆碎片画图可能为Slow Req）进行优先级调度和资源分配**)。(Plan.md 8.2) (P1)
        *   [ ] (后端/AI) **实现情感分析模块、记忆管理模块（支持标签、情感标记、记忆重要性评估雏形）、对话策略模块（根据用户情绪、角色性格、互动目标选择不同回应风格或话题引导）的初步版本API**。(Plan.md 2.1, 2.2, 8.3.2)
        *   [ ] (后端/AI) **探索“关联记忆点，构建用户故事线理解”的初步实现** (限定场景，例如：**当用户提及多个相关联的记忆点时，AI尝试在回应中体现对这些记忆点之间联系的理解，如Plan.md 2.4 MVP亮点示例**)。(Plan.md 2.4)
        *   [ ] (后端/AI) 集成向量数据库，实现基于RAG的记忆检索与Prompt整合 (**将用户对话和记忆胶囊内容进行向量化存储，在生成回复前检索相关记忆片段并注入Prompt**)。
    *   **角色管理增强 (UGC核心):** (Plan.md 3.1)
        *   [ ] (后端) 完善UGC角色数据模型 (Plan.md 3.1.1 深度人设，含**人格-模型体系接口参数(影响LLM调用)、潜意识亲密度构成体系参数化(影响AI行为策略)、关键记忆点(Initial Memory Seeds)等字段**)。
        *   [ ] (后端) 开发创建/编辑UGC角色的完整接口 (含对话示例、风格引导，**支持可视化编辑与实时预览所需的后端数据交互逻辑，如动态生成预览回复** Plan.md 3.1.2, 3.1.3)。
        *   [ ] (后端) 实现角色卡版本管理与迭代说明逻辑 (**存储历史版本，允许用户选择是否更新，记录更新日志**)。(Plan.md 3.1.4) (P2)
        *   [ ] (后端) (初步) AI辅助创作工具后端接口 (**Meta Character：接收简短描述生成人设标签和故事大纲；Story Agent：接收故事背景生成剧情线索和对话建议**)。(Plan.md 3.1.5)
        *   [ ] (后端) 实现角色卡创建方式引导相关逻辑 (如**调用AI对导入或新建角色卡进行一次性的润色建议分析并返回结果**)。(Plan.md 3.1.6) (P2)
        *   [ ] (后端) 角色审核状态管理 (机审+人审流程接口)。
    *   **聊天会话增强:**
        *   [ ] (后端) 扩展聊天消息模型以支持**语音、图片**消息类型。
        *   [ ] (后端) 实现“记忆胶囊”的编辑、**添加标签（如工作、爱好）、情感标记（如开心、重要）、设置记忆有效期或提醒**等高级功能接口。
    *   **多模态AI集成:**
        *   [ ] (后端/AI) 封装 TTS, STT, 图像生成 API 服务。
        *   [ ] (后端) 开发语音收发接口 (集成STT, TTS)。
        *   [ ] (后端) 开发图像生成接口 (集成图像生成服务，异步处理，**支持“记忆碎片画图”：接收用户ID、角色ID、关联记忆文本/事件、选定艺术风格等参数，返回生成的图片URL及稀有度标记**)。(Plan.md 4.2.5, 8.3)
    *   **内容审核增强 (语音/图像):** (Plan.md 10)
        *   [ ] (后端) 集成语音、图像内容审核 API。
    *   **实时通信 (流式输出):**
        *   [ ] (后端) 搭建 WebSocket/SSE 服务，实现LLM流式输出。
    *   **用户激励与留存 (完整版):** (Plan.md 1.5, 5)
        *   [ ] (后端) 完善 **Streak System** (辅助道具如**Streak Freeze卡的使用与扣除逻辑、Streak Repair机会的获取与使用逻辑**)。(Plan.md 1.5.1)
        *   [ ] (后端) 完善 **成就与徽章体系** (多维度、多等级成就，**如互动类、创作类、收集类、激励类成就，具体成就列表参考Plan.md 1.5.2**，奖励发放逻辑，**徽章可在个人主页展示**)。(Plan.md 1.5.2)
        *   [ ] (后端) 实现 **赛季/月度通行证 (Alphane Battle Pass)** 后端逻辑 (**双轨制区分免费/付费奖励，每日/每周/赛季任务生成与完成状态跟踪，战令经验计算与等级提升，各等级对应奖励内容配置与发放，战令币获取与战令商店兑换逻辑**)。(Plan.md 1.5.3)
        *   [ ] (后端) 实现 **日/周/月间留存玩法** 的任务发布、进度跟踪、奖励发放逻辑 (**具体任务类型如“话题探索者”、“记忆印刻”、“灵魂沟通者”、“新星设计师”等，激励检查点奖励配置，参考Plan.md 5.1, 5.2, 5.3**)。
        *   [ ] (后端) 实现 **“每日惊喜时刻”（随机趣味任务及奖励）、“每周主题挑战”（系列主题任务及限定奖励）、“月末史诗任务”（大型多阶段限时任务及顶级奖励）** 等特殊活动后端逻辑。(Plan.md 5.1.3, 5.2.3, 5.3.3)
        *   [ ] (后端) 实现放置Play收菜 (**用户数字孪生或代理AI角色根据离线时长和配置的活跃度，自动计算并累积曦光微尘等收益**) 逻辑。(Plan.md 5.1.5)
    *   **经济系统 (核心四代币):** (Plan.md 4.3.3)
        *   [ ] (后端) 实现 **Alphane曦光微尘、Endora心悦晶石、Serotile忆境拼图、Oxytol羁绊之露** 的详细获取途径 (如任务奖励、活动掉落、月卡赠送、付费代币兑换等) 与消耗场景 (如兑换道具、解锁内容、参与活动、提升羁绊、记忆碎片画图等) 的后端逻辑，**确保数值平衡** (参考Plan.md 4.3.3各代币定位)。
        *   [ ] (后端) 设计并实现代币兑换商店基础功能。
    *   **付费系统 (核心功能):** (Plan.md 4.2, 4.3)
        *   [ ] (后端) 对接支付渠道SDK，处理支付回调和订单管理。
        *   [ ] (后端) 实现 **小月卡 (Alphane Pass) 和 大月卡 (Alphane Diamond)** 的完整权益管理 (**包括但不限于：每日Fast Req次数/无限制Fast Req、每日代币赠送量、Streak Freeze卡/Repair次数、创作者激励资格、尊享密语空间访问权、AI记忆胶囊容量提升、新功能优先体验权等，严格按照Plan.md 4.2.1, 4.2.2执行**)。
        *   [ ] (后端) **实现用户月度资源使用额度 (Credit Limit) 的监控与管理机制 (针对大月卡Fast Req，记录用量，超出正常范畴时进行处理或提示)**。(Plan.md 8.2) (P1)
        *   [ ] (后端) 实现 **“记忆碎片画图” (AI定制图像生成 - “许愿抽卡”体验)** 后端逻辑 (消耗心悦晶石/星钻，**根据触发机制（被动/主动）、可选艺术风格生成不同稀有度画作，画作数据（URL、稀有度、故事背景）与用户账户绑定**)。(Plan.md 4.2.5)
        *   [ ] (后端) 实现 **“羁绊之路” (玩家+角色亲密完成度系统)** 后端逻辑 (各角色独立羁绊等级经验计算，等级提升解锁背景故事/对话选项/情感反馈模式/记忆槽位/成就/画图机会，**付费购买“羁绊加速包”或“深度回忆包”逻辑，高等级用户回馈（如赠送月卡体验券）发放逻辑**)。(Plan.md 4.3.2)
    *   **其他核心功能:**
        *   [ ] (后端) 实现 **“尊享密语空间”** 详细功能后端逻辑 (如**专属公共AI角色（如虚拟酒吧老板）的互动逻辑、官方在线交流活动房间管理**)。(Plan.md 4.1.2) (P2)
        *   [ ] (后端) 实现 **“角色群聊/代理互动 (Meta Stage)”** 详细后端逻辑 (**用户选择OC角色组建群聊，设定初始场景/话题，AI角色间基于设定的自动互动逻辑，数字孪生互动表现评分记录**)。(Plan.md 4.1.3) (P2)
        *   [ ] (后端) 实现 **18+内容管理** 的年龄验证 (**对接第三方身份验证服务或实现资料上传审核流程**)、内容分区访问控制、强化审核规则配置接口。(Plan.md 4.1.5)
        *   [ ] (后端) 实现 **用户免费创建一个数字分身作为数字孪生Character上架** 后端逻辑 (**引导用户回答问题生成角色卡，支持用户修改完善，管理公开/半公开/完全公开状态及对应互动权限**)。(Plan.md 4.2.7) (P2)
*   **3.3 前端开发 (P2)**
    *   **AI交互增强:**
        *   [ ] (前端) 对接并实现语音输入/播放UI及逻辑 (**包含录音按钮、时长显示、播放控制、语音波形动画**)。
        *   [ ] (前端) 对接并实现图像消息展示UI及逻辑 (含图像生成触发按钮、**图片预览、加载状态、失败重试、保存到本地**)。
        *   [ ] (前端) 对接并实现AI回复流式显示 (打字机效果，**确保流畅不卡顿**)。
        *   [ ] (前端) 完善“AI记忆胶囊”高级功能交互界面 (**支持添加标签、情感标记、设置有效期等**)。
    *   **角色创建与管理 (UGC核心):**
        *   [ ] (前端) 实现完整的UGC角色创建/编辑界面 (含**人格-模型体系接口参数的可视化设置选项、潜意识亲密度参数的滑块或百分比调整、关键记忆点(Initial Memory Seeds)的列表输入、对话示例的结构化输入与增删改查、版本历史查看与切换界面**)。(Plan.md 3.1)
        *   [ ] (前端) (初步) AI辅助创作工具前端交互界面 (**Meta Character：输入简短描述后，AI推荐的标签和故事大纲以卡片或列表形式展示，用户可点选采纳；Story Agent：引导式表单输入故事背景，AI生成的剧情线索和对话建议可供选择和编辑**)。(Plan.md 3.1.5)
        *   [ ] (前端) 实现角色卡可视化编辑与实时预览功能 (**编辑器调整参数时，右侧或下方预览窗口可输入测试语句，实时显示AI基于当前设定的回复**)。(Plan.md 3.1.3)
    *   **用户激励与留存 (完整版):**
        *   [ ] (前端) 实现Streak System完整交互 (含**Streak Freeze卡和Repair机会的图标展示、使用确认弹窗、使用后状态更新**)。
        *   [ ] (前端) 实现成就与徽章墙展示界面 (**可按分类/稀有度筛选和排序，点击徽章可查看获取条件和日期，支持将徽章设为在个人主页展示**)。
        *   [ ] (前端) 实现荣耀战令界面 (**清晰展示免费/付费轨道及各等级奖励，任务列表（含跳转任务场景按钮）、等级进度条、经验值、战令币数量，奖励领取交互，战令商店商品列表及兑换流程**)。
        *   [ ] (前端) 实现日/周/月间留存任务列表、进度展示、奖励领取界面 (**任务详情弹窗、一键领取全部已完成奖励按钮**)。
        *   [ ] (前端) 实现特殊活动（**每日惊喜时刻的弹窗或入口提示、每周主题挑战的活动专属页面、月末史诗任务的引导流程和阶段性目标展示**）的参与界面。
    *   **经济系统与付费:**
        *   [ ] (前端) 实现四种核心代币在主界面或个人中心的清晰展示，**点击代币可跳转至获取途径说明或相关商店页面**。
        *   [ ] (前端) 实现代币商店界面 (**分类展示可兑换的道具、特权或服务，显示价格、描述、购买按钮**)。
        *   [ ] (前端) 实现大小月卡购买页面 (**详细对比两种月卡的权益，展示价格，引导用户选择并完成支付流程**)。
        *   [ ] (前端) 实现“记忆碎片画图”交互界面 (**触发许愿的引导提示，选择关联记忆/角色、选择艺术风格（如有），确认消耗代币，展示生成中的动画或提示，最终展示生成的画作，提供保存到回忆相册或分享功能**)。
        *   [ ] (前端) 实现“羁绊之路”界面 (**各角色独立的羁绊进度条、当前等级、下一级所需经验、各等级解锁内容预览，赠送虚拟礼物提升羁绊的交互**)。
    *   **其他核心功能界面:**
        *   [ ] (前端) 实现“尊享密语空间”基础聊天界面 (**可能包含特殊的UI主题或专属表情**)。
        *   [ ] (前端) 实现“角色群聊/代理互动 (Meta Stage)”设置界面 (**选择参与的OC角色列表，输入或选择初始场景/话题**）与观看界面（**展示AI角色间的对话气泡流，可能支持用户发送“指令”或“提示”影响互动**）。
        *   [ ] (前端) 实现用户数字分身创建引导流程 (**问答式界面，AI辅助生成后可编辑确认**）与管理界面（**查看、编辑、设置公开状态**）。
    *   **UI/UX 优化:**
        *   [ ] (前端) 实现浅色/深色主题切换及全局样式适配。(Plan.md 6.2)
        *   [ ] (前端) 根据Plan.md 6.2 (UI/UX) 要求，优化整体视觉和交互体验，**确保新增功能的界面风格统一、操作流畅**。
        *   [ ] (前端) 实现情感化设计细节 (过渡动画、音效反馈等，**例如：获取稀有画作时的特殊展示动画和音效**)。(Plan.md 6.2.6)
*   **3.4 AI 模型 (P2)**
    *   [ ] (AI/后端) 优化LLM Prompt Engineering (结合RAG检索结果，**针对不同对话策略（如安慰、鼓励、探索、角色扮演特定情境）设计不同的Prompt结构和注入内容**，提升对话相关性和情感准确度)。
    *   [ ] (AI/后端) 调优图像生成模型/API参数和风格。
    *   [ ] (AI/后端) 测试TTS/STT服务效果。
    *   [ ] (AI/后端) 建立初步AI效果评估流程 (**如人工评估对话质量、情感表达准确度、记忆运用自然度**)。
    *   [ ] (AI/后端) 针对“记忆碎片画图”功能，**调试和优化文生图模型/API的Prompt生成逻辑，确保能根据记忆文本（可能包含情感、场景、角色状态等元素）生成情境相符且具有美感的图像，并探索不同艺术风格的参数配置和稀有度关联**。(Plan.md 4.2.5) (P2)
*   **3.5 UI/UX 设计 (P2)**
    *   [ ] (设计) 完成Phase 2新增功能（多模态交互、完整激励系统界面、付费功能界面、UGC角色高级编辑、Meta Stage、尊享密语空间、数字分身创建等）的详细高保真原型设计和交互说明。
    *   [ ] (设计) 设计浅色/深色两套主题。
    *   [ ] (设计) 优化MVP反馈较多的交互问题。
    *   [ ] (设计) 设计四种核心代币的图标及其在UI中的展示规范。(Plan.md 4.3.3) (P1)
    *   [ ] (设计) 设计成就徽章的视觉风格和系列模板 (青铜、白银、黄金等不同等级)。(Plan.md 1.5.2) (P1)
    *   [ ] (设计) 设计荣耀战令的整体视觉风格和各等级奖励的展示方式 (区分免费/付费轨道)。(Plan.md 1.5.3) (P1)
*   **3.6 测试 (P2)**
    *   [ ] (测试) 编写Phase 2新增功能测试用例 (**覆盖所有AI能力增强、UGC角色管理、多模态交互、完整用户激励与留存系统、经济与付费系统、其他核心功能**)。
    *   [ ] (测试) 执行全面的功能回归测试。
    *   [ ] (测试) 进行兼容性、基础性能测试 (**特别是多模态消息处理和流式输出的性能**)。
    *   [ ] (测试/产品) 组织更大范围的用户测试 (**收集关于新功能易用性、游戏化机制吸引力、付费点接受度等反馈**)。

---

## **Phase 3: 创作者生态、社区与商业化深化 (持续迭代)**

*   **4.1 后端开发 (P3)**
    *   **创作者激励与支持体系:** (Plan.md 3.2)
        *   [ ] (后端) 实现创作者物质激励后端逻辑 (**基于大月卡设计师、特定付费角色卡互动或归因消费产生的分成，实现基准50%分成的计算与记录；实现热门角色卡激励调整的评估标准（如互动时长、活跃用户、好评率、付费转化率）的数据统计接口，支持运营手动调整分成比例至55%-70%；提供透明化结算所需的数据接口，支持每月固定日期结算的标记与状态更新**)。(Plan.md 3.2.1)
        *   [ ] (后端) 实现创作者非物质激励后端逻辑 (**官方认证设计师体系：等级（新星、精英、资深、顶尖/造梦大师）与角色质量/数量/受欢迎程度/社区贡献挂钩的评定标准配置与状态管理；专属徽章与头衔ID与用户绑定；设计师排行榜数据生成（基于热度、活跃度、贡献等多维度）；平台贡献积分体系：积分获取途径（角色表现、社区贡献、平台共建、活动参与）的事件监听与积分增减逻辑，可兑换稀有收集奖励的库存管理与兑换接口**)。(Plan.md 3.2.2)
        *   [ ] (后端) 设计师后台基础功能 (**提供查询个人收入数据（预估、已结算）、分成比例、贡献积分总量及明细的API**)。
        *   [ ] (后端) 实现官方创作教程与最佳实践的内容管理接口 (**支持后台上传/编辑图文教程，按分类管理**)。(Plan.md 3.1.7) (P3)
        *   [ ] (后端) 实现设计师专属社区/论坛板块的基础支撑功能 (如**特定用户组权限管理、帖子发布/评论接口、官方运营人员特殊标记**)。(Plan.md 3.1.7) (P3)
    *   **社区功能:** (参考原TodoList 4.1，并结合Plan.md调整)
        *   [ ] (后端) 实现分享功能接口 (**支持分享角色卡、特定对话片段、AI生成的记忆画图到平台内动态或外部应用**)。
        *   [ ] (后端) 实现评论、点赞接口 (**针对角色卡、动态、分享内容**)。
        *   [ ] (后端) 实现关注/粉丝系统接口 (**支持关注其他用户/设计师，关注特定角色卡**)。
        *   [ ] (后端) 实现用户动态/通知系统接口 (**生成动态（如发布新角色卡、分享内容），聚合通知类型：系统公告、被关注/点赞/评论通知、角色卡更新通知、活动提醒等**)。
    *   **商业化深化:** (Plan.md 4.2)
        *   [ ] (后端) 实现角色卡相关增值服务购买接口 (**官方精品/IP联动/顶尖设计师典藏角色卡的购买与用户解锁逻辑；解锁角色专属深度互动剧情包（可能包含多个剧情节点和分支）的购买与访问权限管理；角色个性化装扮（如开场白风格包ID、对话主题皮肤ID、互动表情包ID）的购买与用户配置绑定**)。(Plan.md 4.2.3)
        *   [ ] (后端) 实现直接充值购买核心付费代币 (**如“星钻”，提供不同档位充值包配置，处理支付回调，更新用户星钻余额，记录充值订单，支持首充双倍、累充奖励等活动逻辑**)。(Plan.md 4.2.4)
        *   [ ] (后端) 实现 **“AI记忆胶囊”扩展服务付费接口 (如一次性购买永久提升存储上限的配置与用户状态更新；购买“记忆深化服务”消耗付费代币，触发对指定记忆的AI深度学习与关联分析任务，并标记该记忆已强化)**。(Plan.md 4.2.3)
        *   [ ] (后端) (若采纳) 打赏功能接口 (**允许用户消耗付费代币打赏角色卡设计师，记录打赏流水，按规则计入设计师待结算收入**)。
    *   **高级 AI 功能:**
        *   [ ] (后端) 探索动态剧情引擎基础架构 (**允许设计师配置剧情节点、分支条件（如用户选择、特定记忆、亲密度等级），AI根据条件动态推进剧情，记录用户剧情进度**)。
        *   [ ] (后端) 探索声音克隆功能接口 (**若技术成熟，允许用户上传少量个人音频，AI克隆声音用于数字分身或特定角色，需严格考虑伦理和安全**)。
        *   [ ] (后端) 深化长期记忆方案 (记忆编辑、会话摘要)。
    *   **推荐系统增强:**
        *   [ ] (后端) 引入更复杂的推荐算法 (**如基于内容的推荐、协同过滤、混合推荐，针对角色卡、UGC故事、设计师进行推荐**)。
        *   [ ] (后端) 搭建A/B测试框架。
    *   **后台管理端:**
        *   [ ] (后端) 开发用户管理 (**查询、封禁、用户数据修改**)、角色审核 (**UGC角色卡审核列表、审核操作、打回、通过**)、内容管理 (**动态、评论管理、敏感词配置**)、数据统计 (**DAU/MAU、付费数据、留存数据、创作者数据看板**)、系统配置 (**活动配置、奖励配置、AI参数调整**)、**创作者管理（认证审核、分成比例调整、贡献积分发放）**等模块。
*   **4.2 前端开发 (P3)**
    *   **创作者相关功能:**
        *   [ ] (前端) 实现设计师后台基础界面 (**展示收入明细图表、分成比例、提现申请入口（若支持）、贡献积分获取与兑换记录、可兑换奖励列表及兑换操作**)。
        *   [ ] (前端) 创作者荣誉展示 (**个人主页展示官方认证徽章和头衔，设计师排行榜页面**)。
        *   [ ] (前端) 实现官方创作教程浏览界面 (**分类展示教程列表，图文混排阅读体验**)。
        *   [ ] (前端) 实现设计师专属社区/论坛基础界面 (**帖子列表、发帖/回帖编辑器、个人中心（社区相关）**)。
    *   **社区板块:**
        *   [ ] (前端) 开发“发现”/“广场”页面 (**瀑布流或卡片列表展示角色卡、用户动态、分享内容，支持按热度/最新等筛选和排序，实现评论区、点赞按钮、分享到站外功能**)。
        *   [ ] (前端) 开发用户个人主页 (**聚合展示用户创作的角色卡、发布的动态、获得的成就徽章墙、关注列表、粉丝列表、数字分身形象**)。
        *   [ ] (前端) 开发通知中心页面 (**分类展示各类通知，未读消息提示，支持一键已读或删除**)。
    *   **商业化深化:**
        *   [ ] (前端) 实现角色卡相关增值服务购买流程 (**官方精品角色卡详情页（含介绍、价格、购买按钮）、专属剧情包介绍页与购买解锁、个性化装扮预览（如聊天气泡、主页背景）与购买配置界面**)。
        *   [ ] (前端) 实现核心付费代币（星钻）充值界面 (**各档位及赠送活动清晰展示，选择支付方式，完成支付跳转**)。
        *   [ ] (前端) 实现“AI记忆胶囊”扩展服务购买界面 (**服务介绍、价格、购买确认**)。
    *   **高级功能界面:**
        *   [ ] (前端) 实现动态剧情引擎驱动的互动故事体验界面 (**分支选项展示、剧情文本呈现、用户选择交互**)。
        *   [ ] (前端) 实现声音克隆相关设置界面（**音频上传、试听、应用到角色**）（若实现）。
    *   **性能优化与体验打磨:**
        *   [ ] (前端) 列表无限滚动与虚拟列表优化 (**针对角色广场、动态信息流、聊天记录等长列表**)。
        *   [ ] (前端) 关键渲染性能分析与优化 (**使用性能分析工具定位瓶颈**)。
        *   [ ] (前端) 包体积优化 (**代码分割、资源压缩**)。
    *   **后台管理端界面 (Web):**
        *   [ ] (前端/Web) 为运营和管理人员提供上述4.1.6节后台管理功能的完整Web操作界面 (**使用合适的Web前端框架如React/Vue/Angular，确保界面清晰、操作便捷、数据展示直观**)。
*   **4.3 AI 模型 (P3)**
    *   [ ] (AI/后端) 探索LLM微调 (Fine-tuning) **针对特定高质量UGC角色卡或官方角色，使用其对话数据进行模型微调，以增强角色扮演的个性和一致性**。
    *   [ ] (AI/后端) 优化多模态模型。
    *   [ ] (AI/后端) 持续优化长期记忆方案 (**研究更有效的记忆压缩、遗忘和关联机制**)。
    *   [ ] (AI/后端) 建立更完善的AI效果监控和自动化评估体系 (**引入客观评估指标，如BLEU、ROUGE用于文本生成，以及用户满意度调研数据**)。
    *   [ ] (AI/后端) 针对动态剧情引擎，**研究如何让LLM更好地理解剧情上下文和分支条件，生成连贯且符合设定的剧情文本，并探索AI辅助生成剧情分支的可能性**。
*   **4.4 UI/UX 设计 (P3)**
    *   [ ] (设计) 设计创作者生态相关界面（设计师后台、荣誉体系展示、教程与社区）、社区功能界面（动态、个人主页、通知）、商业化深化相关界面（增值服务购买、充值）的详细高保真原型和交互说明。
    *   [ ] (设计) 设计游戏化元素视觉表现 (**如更精美的徽章、动态头像框、特殊活动的主视觉**)。
    *   [ ] (设计) 持续迭代优化整体用户体验 (**基于用户反馈和数据分析**)。
    *   [ ] (设计) 设计平台贡献积分可兑换的各类稀有奖励的视觉图标或预览效果。(Plan.md 3.2.2) (P2)
    *   [ ] (设计) 设计官方认证设计师各等级的徽章和头衔视觉样式。(Plan.md 3.2.2) (P2)
    *   [ ] (设计) 设计后台管理系统的UI/UX规范和主要界面。
*   **4.5 测试 (P3)**
    *   [ ] (测试) 引入自动化测试框架 (**针对核心API和UI流程编写自动化测试脚本**)。
    *   [ ] (测试) 进行专项安全测试 (**数据安全、支付安全、防刷单等**)。
    *   [ ] (测试) 进行更全面的压力测试 (**模拟高并发场景，评估系统稳定性和性能瓶颈**)。
    *   [ ] (测试) 建立用户反馈的收集、分类、跟踪处理流程 (**通过应用内反馈、社区、客服等多渠道**)。
    *   [ ] (测试) 针对创作者激励体系（分成计算准确性、非物质激励发放正确性）进行专项测试。
    *   [ ] (测试) 针对社区功能的交互流程和数据一致性进行测试。
    *   [ ] (测试) 针对所有新增付费点进行完整的支付流程测试和订单准确性测试。
*   **4.6 市场与运营 (P3)** (Plan.md 6)
    *   [ ] (运营) 制定并执行市场推广计划 (含三语核心市场的中美日深度本地化策略，**例如：针对不同市场设计本土化的角色卡模板、推广文案、合作KOL类型；分析各市场用户对AI伴侣的文化偏好并反馈给产品设计** Plan.md 6.1)。
    *   [ ] (运营) 制定社区运营策略 (**包括：定期举办UGC角色设计大赛、主题创作活动、设计师交流分享会；建立社区版主和志愿者团队；处理用户反馈和纠纷**)。
    *   [ ] (运营) 建立用户支持体系 (**FAQ、客服渠道、问题反馈机制**)。
    *   [ ] (运营) 进行数据分析，指导产品迭代 (**搭建数据看板，监控核心指标，定期输出分析报告**)。
    *   [ ] (运营) 规划并执行线上活动 (**节日活动、版本更新活动、拉新促活活动**)。
    *   [ ] (运营) 规划并执行针对创作者的系列扶持活动和奖励计划 (**如新星设计师孵化、优质作品流量倾斜、创作基金等**)。(Plan.md 3.2) (P2)

---