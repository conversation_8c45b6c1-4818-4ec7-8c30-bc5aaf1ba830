<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alphane.ai - 认证</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans', system-ui, -apple-system, sans-serif;
            font-feature-settings: 'cv11', 'ss01';
            font-variation-settings: 'opsz' 32;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .auth-section {
            display: none;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .auth-section.active {
            display: flex;
            opacity: 1;
            transform: translateY(0);
        }

        /* Launch Screen Styles */
        .launch-background {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            position: relative;
            overflow: hidden;
        }

        .launch-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.2) 0%, transparent 50%);
        }

        .logo-glow {
            filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.5));
            animation: logoFloat 6s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(2deg); }
            66% { transform: translateY(-5px) rotate(-2deg); }
        }

        .loading-dots {
            display: inline-flex;
            gap: 4px;
        }

        .loading-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: loadingDot 1.4s ease-in-out infinite both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loadingDot {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1.2); opacity: 1; }
        }

        /* Auth Form Styles */
        .auth-background {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            position: relative;
        }

        .auth-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
        }

        .form-container {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            width: 100%;
            max-width: 420px;
            padding: 48px 40px;
            position: relative;
            z-index: 10;
        }

        .form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: 24px;
            z-index: -1;
        }

        .brand-logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 24px;
            color: var(--primary-600);
            filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
        }

        .brand-title {
            font-size: 32px;
            font-weight: 800;
            line-height: 1.2;
            background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-700) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .brand-subtitle {
            font-size: 16px;
            color: var(--gray-600);
            font-weight: 500;
            margin-bottom: 32px;
        }

        .welcome-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(139, 92, 246, 0.06) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 32px;
            text-align: center;
        }

        .welcome-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-700);
            margin-bottom: 6px;
        }

        .welcome-subtitle {
            font-size: 12px;
            color: var(--primary-600);
            font-weight: 500;
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .form-label .material-icons-outlined {
            font-size: 18px;
            color: var(--primary-500);
        }

        .form-input {
            width: 100%;
            height: 48px;
            padding: 0 16px;
            border: 1.5px solid var(--gray-300);
            border-radius: 12px;
            font-size: 15px;
            font-weight: 500;
            color: var(--gray-900);
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
        }

        .form-input:focus {
            border-color: var(--primary-500);
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }

        .form-input::placeholder {
            color: var(--gray-400);
            font-weight: 400;
        }

        .input-group {
            display: flex;
            border-radius: 12px;
            overflow: hidden;
            border: 1.5px solid var(--gray-300);
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .input-group:focus-within {
            border-color: var(--primary-500);
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        }

        .input-group input {
            border: none;
            background: none;
            height: 48px;
        }

        .input-group button {
            padding: 0 20px;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            border: none;
            border-left: 1px solid var(--gray-200);
            color: var(--gray-600);
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .input-group button:hover {
            background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
            color: var(--gray-700);
        }

        .form-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 32px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid var(--gray-300);
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .checkbox:checked {
            background: var(--primary-500);
            border-color: var(--primary-500);
        }

        .checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .checkbox-label {
            font-size: 14px;
            color: var(--gray-600);
            font-weight: 500;
            cursor: pointer;
        }

        .link {
            color: var(--primary-600);
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: color 0.2s;
        }

        .link:hover {
            color: var(--primary-700);
            text-decoration: underline;
        }

        .submit-button {
            width: 100%;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .submit-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.5);
        }

        .submit-button:active {
            transform: translateY(0);
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 32px 0;
            color: var(--gray-500);
            font-size: 14px;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--gray-200);
        }

        .divider span {
            padding: 0 16px;
            background: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .social-button {
            width: 100%;
            height: 48px;
            background: rgba(255, 255, 255, 0.9);
            border: 1.5px solid var(--gray-200);
            border-radius: 12px;
            color: var(--gray-700);
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
        }

        .social-button:hover {
            background: white;
            border-color: var(--gray-300);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .footer-text {
            text-align: center;
            font-size: 14px;
            color: var(--gray-600);
            font-weight: 500;
            margin-top: 32px;
        }

        .footer-link {
            font-size: 12px;
            color: var(--gray-500);
            margin-top: 12px;
        }

        .footer-link a {
            color: var(--primary-600);
            text-decoration: none;
            font-weight: 500;
        }

        .footer-link a:hover {
            text-decoration: underline;
        }

        .gift-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
            color: white;
            font-size: 11px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 6px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            margin-top: 8px;
        }

        .success-icon {
            color: #22c55e;
            font-size: 48px;
            margin-bottom: 16px;
            animation: successBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes successBounce {
            0% { transform: scale(0) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
            100% { transform: scale(1) rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            .form-container {
                margin: 16px;
                padding: 32px 24px;
                max-width: none;
            }
            
            .brand-title {
                font-size: 28px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-6">

    <!-- Launch Screen -->
    <div id="auth-launch" class="auth-section active launch-background w-full h-screen flex-col items-center justify-center text-center relative">
        <div class="relative z-10">
            <svg class="logo-glow w-20 h-20 mx-auto mb-8" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                <path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path>
            </svg>
            
            <h1 class="text-5xl font-black text-white mb-4 tracking-tight">
                <span class="bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                    Alphane.ai
                </span>
            </h1>
            
            <p class="text-xl text-blue-100 mb-12 max-w-md mx-auto font-medium">
                🚀 开启您的AI伙伴之旅
            </p>
            
            <div class="flex items-center justify-center gap-4 text-blue-200">
                <span class="text-base font-medium">正在连接未来</span>
                <div class="loading-dots">
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Form -->
    <div id="auth-login" class="auth-section auth-background w-full h-screen items-center justify-center relative">
        <div class="form-container">
            <div class="text-center mb-8">
                <svg class="brand-logo" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path>
                </svg>
                <h1 class="brand-title">登录 Alphane.ai</h1>
                <p class="brand-subtitle">欢迎回到您的AI伙伴世界</p>
            </div>
            
            <div class="welcome-card">
                <p class="welcome-title">🎮 准备好收集神秘代币了吗？</p>
                <p class="welcome-subtitle">曦光微尘 • 心悦晶石 • 忆境拼图 • 羁绊之露</p>
            </div>
            
            <form class="space-y-6">
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">person</span>
                        邮箱 / 手机号
                    </label>
                    <input type="email" class="form-input" placeholder="<EMAIL> 或 13800138000" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">lock</span>
                        密码 / OTP
                    </label>
                    <input type="password" class="form-input" placeholder="请输入密码或验证码" required>
                </div>
                
                <div class="form-row">
                    <div class="checkbox-group">
                        <input type="checkbox" class="checkbox" id="remember">
                        <label for="remember" class="checkbox-label">记住我</label>
                    </div>
                    <a href="#" onclick="showAuthSection('auth-forgot')" class="link">忘记密码?</a>
                </div>
                
                <button type="submit" class="submit-button">
                    <span class="material-icons-outlined">rocket_launch</span>
                    开始冒险
                </button>
            </form>
            
            <div class="divider">
                <span>或通过以下方式登录</span>
            </div>
            
            <a href="#" class="social-button">
                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Google 快速登录
            </a>
            
            <p class="footer-text">
                还没有账户? <a href="#" onclick="showAuthSection('auth-register')" class="link">立即注册</a>
            </p>
            <p class="footer-link text-center">
                登录即表示您同意我们的 <a href="#">服务条款</a> 和 <a href="#">隐私政策</a>
            </p>
        </div>
    </div>

    <!-- Register Form -->
    <div id="auth-register" class="auth-section auth-background w-full h-screen items-center justify-center relative">
        <div class="form-container">
            <div class="text-center mb-8">
                <svg class="brand-logo" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path>
                </svg>
                <h1 class="brand-title">加入 Alphane.ai</h1>
                <p class="brand-subtitle">创建您的专属AI伙伴账户</p>
            </div>
            
            <div class="welcome-card">
                <p class="welcome-title">🎁 新用户专享福利</p>
                <p class="welcome-subtitle">注册即送丰厚代币奖励 + 专属新手指导</p>
                <div class="gift-badge">
                    <span class="material-icons-outlined" style="font-size: 12px;">redeem</span>
                    首充双倍奖励
                </div>
            </div>
            
            <form class="space-y-5">
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">contact_mail</span>
                        邮箱 / 手机号
                    </label>
                    <input type="email" class="form-input" placeholder="<EMAIL> 或 13800138000" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">verified</span>
                        验证码 (OTP)
                    </label>
                    <div class="input-group">
                        <input type="text" placeholder="请输入验证码" required>
                        <button type="button">获取验证码</button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">lock_open</span>
                        设置密码
                    </label>
                    <input type="password" class="form-input" placeholder="请输入至少8位密码" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">lock</span>
                        确认密码
                    </label>
                    <input type="password" class="form-input" placeholder="请再次输入密码" required>
                </div>
                
                <div class="checkbox-group" style="margin-bottom: 32px;">
                    <input type="checkbox" class="checkbox" id="terms" required>
                    <label for="terms" class="checkbox-label">
                        我已阅读并同意 <a href="#" class="link">服务条款</a> 和 <a href="#" class="link">隐私政策</a>
                    </label>
                </div>
                
                <button type="submit" class="submit-button">
                    <span class="material-icons-outlined">auto_awesome</span>
                    开启AI冒险
                </button>
            </form>
            
            <div class="divider">
                <span>或通过以下方式注册</span>
            </div>
            
            <a href="#" class="social-button">
                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Google 快速注册
            </a>
            
            <p class="footer-text">
                已有账户? <a href="#" onclick="showAuthSection('auth-login')" class="link">立即登录</a>
            </p>
        </div>
    </div>

    <!-- Forgot Password -->
    <div id="auth-forgot" class="auth-section auth-background w-full h-screen items-center justify-center relative">
        <div class="form-container">
            <div class="text-center mb-8">
                <svg class="brand-logo" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path>
                </svg>
                <h1 class="brand-title">找回密码</h1>
                <p class="brand-subtitle">别担心，让我们帮您重新进入AI世界</p>
            </div>
            
            <form class="space-y-6">
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">alternate_email</span>
                        邮箱 / 手机号
                    </label>
                    <input type="email" class="form-input" placeholder="<EMAIL> 或 13800138000" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">security</span>
                        验证码 (OTP)
                    </label>
                    <div class="input-group">
                        <input type="text" placeholder="请输入验证码" required>
                        <button type="button">获取验证码</button>
                    </div>
                </div>
                
                <button type="button" onclick="showAuthSection('auth-reset')" class="submit-button">
                    <span class="material-icons-outlined">arrow_forward</span>
                    下一步
                </button>
            </form>
            
            <p class="footer-text">
                记起密码了? <a href="#" onclick="showAuthSection('auth-login')" class="link">返回登录</a>
            </p>
        </div>
    </div>

    <!-- Reset Password -->
    <div id="auth-reset" class="auth-section auth-background w-full h-screen items-center justify-center relative">
        <div class="form-container">
            <div class="text-center mb-8">
                <span class="success-icon material-icons-outlined">check_circle</span>
                <h1 class="brand-title">设置新密码</h1>
                <p class="brand-subtitle">最后一步，设置一个强密码保护您的账户</p>
            </div>
            
            <form class="space-y-6">
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">lock_reset</span>
                        新密码
                    </label>
                    <input type="password" class="form-input" placeholder="请输入至少8位的新密码" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <span class="material-icons-outlined">lock</span>
                        确认新密码
                    </label>
                    <input type="password" class="form-input" placeholder="请再次输入新密码" required>
                </div>
                
                <button type="button" onclick="showAuthSection('auth-login'); alert('🎉 密码重置成功！欢迎回到Alphane.ai!');" class="submit-button">
                    <span class="material-icons-outlined">check_circle</span>
                    完成重置
                </button>
            </form>
            
            <p class="footer-text">
                <a href="#" onclick="showAuthSection('auth-login')" class="link">返回登录</a>
            </p>
        </div>
    </div>

    <script>
        const sections = ['auth-launch', 'auth-login', 'auth-register', 'auth-forgot', 'auth-reset'];

        function showAuthSection(sectionIdToShow) {
            sections.forEach(id => {
                const section = document.getElementById(id);
                if (id === sectionIdToShow) {
                    section.classList.add('active');
                } else {
                    section.classList.remove('active');
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            showAuthSection('auth-launch');
            setTimeout(() => {
                const launchScreen = document.getElementById('auth-launch');
                if (launchScreen.classList.contains('active')) {
                    showAuthSection('auth-login');
                }
            }, 3000);
        });
    </script>
</body>
</html>