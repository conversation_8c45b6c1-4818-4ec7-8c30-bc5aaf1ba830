<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成就与徽章 - Alphane.ai</title>
    
    <!-- 字体和图标 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        :root {
            /* Alphane品牌色彩 */
            --alphane-primary: #6366f1;
            --alphane-secondary: #8b5cf6;
            --alphane-accent: #ec4899;
            --alphane-warm: #f59e0b;
            --alphane-success: #10b981;
            --alphane-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 动画效果 */
        .hover-lift {
            transition: all 0.3s ease;
        }
        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, var(--alphane-primary), var(--alphane-accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 侧边栏样式 */
        .achievement-sidebar a.active {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            color: var(--alphane-primary);
            border-left-color: var(--alphane-primary);
            font-weight: 600;
        }
        .achievement-sidebar a {
            @apply border-transparent hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-600 transition-all duration-200;
        }

        /* Tab按钮样式 */
        .achievement-tab-button.active {
            background: linear-gradient(135deg, var(--alphane-primary), var(--alphane-secondary));
            color: white;
            border-color: var(--alphane-primary);
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
        .achievement-tab-button {
            @apply border-gray-300 text-slate-600 hover:text-indigo-600 hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200;
        }

        /* Tab内容 */
        .achievement-tab-content { 
            display: none; 
            animation: fadeInUp 0.3s ease-out;
        }
        .achievement-tab-content.active { display: block; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 成就卡片样式 */
        .achievement-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .achievement-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .achievement-card.locked { 
            opacity: 0.6; 
            filter: grayscale(50%);
        }
        .achievement-card.claimable { 
            border: 2px solid #f59e0b;
            background: linear-gradient(135deg, #fffbeb, #fef3c7);
            box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(245, 158, 11, 0.3); }
            to { box-shadow: 0 0 30px rgba(245, 158, 11, 0.6); }
        }

        /* 成就图标背景 */
        .achievement-icon-bg {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }
        .achievement-icon-bg .material-icons-outlined { 
            font-size: 2rem;
        }
        .achievement-card:hover .achievement-icon-bg {
            transform: scale(1.1);
        }

        /* 奖励标签样式 */
        .reward-tag {
            @apply flex items-center gap-1.5 text-xs px-3 py-1.5 rounded-full font-medium border shadow-sm;
            transition: all 0.2s ease;
        }
        .reward-tag:hover {
            transform: scale(1.05);
        }

        /* 代币样式 */
        .token-alphane { 
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #d97706;
            border-color: #f59e0b;
        }
        .token-endora { 
            background: linear-gradient(135deg, #fce7f3, #fbcfe8);
            color: #be185d;
            border-color: #ec4899;
        }
        .token-serotile { 
            background: linear-gradient(135deg, #e6fffa, #ccfbf1);
            color: #0f766e;
            border-color: #14b8a6;
        }
        .token-oxytol { 
            background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
            color: #7c3aed;
            border-color: #8b5cf6;
        }
        .token-diamond { 
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1d4ed8;
            border-color: #3b82f6;
        }
        .token-exp { 
            background: linear-gradient(135deg, #fed7aa, #fdba74);
            color: #ea580c;
            border-color: #f97316;
        }
        .special-reward { 
            background: linear-gradient(135deg, #fef3c7, #fde047);
            color: #a16207;
            border-color: #eab308;
        }

        /* 徽章展示卡片 */
        .badge-display-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            aspect-ratio: 1;
        }
        .badge-display-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        }
        .badge-display-card.selected { 
            border-color: var(--alphane-primary);
            background: linear-gradient(135deg, #eef2ff, #e0e7ff);
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
        }
        .badge-display-card.locked { 
            opacity: 0.5; 
            cursor: not-allowed; 
            background: #f1f5f9;
            filter: grayscale(100%);
        }
        .badge-display-card img { 
            width: 4rem; 
            height: 4rem; 
            margin-bottom: 0.5rem; 
            object-fit: contain;
            transition: all 0.3s ease;
        }
        .badge-display-card:hover img {
            transform: scale(1.1);
        }
        .badge-display-card .material-icons-outlined.badge-icon { 
            font-size: 4rem; 
            margin-bottom: 0.5rem; 
            color: #cbd5e1;
        }

        /* 进度条样式 */
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.5s ease;
        }

        /* 总览卡片动画 */
        .overview-card {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 50%, #dc2626 100%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* 响应式布局 */
        @media (max-width: 1023px) {
            .desktop-sidebar {
                display: none;
            }
            .main-content {
                margin-left: 0 !important;
            }
        }

        /* 成就等级样式 */
        .achievement-bronze { color: #cd7f32; }
        .achievement-silver { color: #c0c0c0; }
        .achievement-gold { color: #ffd700; }
        .achievement-platinum { color: #e5e4e2; }
        .achievement-diamond { color: #b9f2ff; }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- 侧边栏 -->
    <aside class="achievement-sidebar desktop-sidebar fixed left-0 top-0 z-50 h-full w-80 bg-white shadow-xl border-r border-gray-200">
        <!-- 品牌头部 -->
        <div class="flex items-center justify-between px-6 py-5 border-b border-gray-100">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <span class="text-white text-xl font-bold">A</span>
                </div>
                <div>
                    <h1 class="text-lg font-bold gradient-text">Alphane.ai</h1>
                    <p class="text-xs text-gray-500">有温度的AI伴侣</p>
                </div>
            </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="flex-1 space-y-2 overflow-y-auto px-4 py-6">
            <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4" href="2-PC-首页-最终版.html">
                <span class="material-icons-outlined text-xl">cottage</span> 
                <span>主页</span>
            </a>
            <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4" href="8-PC-个人中心页.html">
                <span class="material-icons-outlined text-xl">account_circle</span> 
                <span>个人中心</span>
            </a>
            
            <div class="pt-4">
                <h3 class="mb-3 px-3 text-xs font-semibold uppercase text-gray-500 tracking-wider">激励与成长</h3>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4" href="9-PC-任务中心页.html">
                    <span class="material-icons-outlined text-xl">checklist_rtl</span> 
                    <span>任务中心</span>
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4 active" href="10-PC-成就徽章页.html">
                    <span class="material-icons-outlined text-xl">emoji_events</span> 
                    <span>成就徽章</span>
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4" href="11-PC-荣耀战令页.html">
                    <span class="material-icons-outlined text-xl">military_tech</span> 
                    <span>荣耀战令</span>
                </a>
            </div>
        </nav>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content ml-80 overflow-y-auto min-h-screen">
        <!-- 页面头部 -->
        <header class="sticky top-0 z-30 bg-white/90 backdrop-blur-sm border-b border-gray-200 px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold gradient-text">成就殿堂</h1>
                    <p class="text-gray-600 mt-1">记录你的每一个闪耀时刻！</p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">
                        成就点: <span class="font-bold text-yellow-600 text-xl">1,250</span>
                    </p>
                    <p class="text-xs text-gray-500">
                        已点亮徽章: <span class="font-semibold text-indigo-600">15</span> / 50
                    </p>
                </div>
            </div>
        </header>

        <div class="px-8 py-6">
            <!-- 成就总览卡片 -->
            <div class="mb-8 p-8 rounded-2xl overview-card text-white shadow-xl relative overflow-hidden">
                <!-- 动态背景粒子 -->
                <div class="absolute inset-0">
                    <div class="absolute top-10 left-10 w-4 h-4 bg-white/20 rounded-full animate-ping"></div>
                    <div class="absolute top-1/3 right-20 w-3 h-3 bg-yellow-300/30 rounded-full animate-bounce"></div>
                    <div class="absolute bottom-20 left-1/4 w-5 h-5 bg-orange-300/20 rounded-full animate-pulse"></div>
                    <div class="absolute top-1/2 left-1/2 w-2 h-2 bg-pink-300/40 rounded-full animate-ping" style="animation-delay: 1s"></div>
                </div>
                
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <div class="flex items-center gap-4 mb-2">
                                <h2 class="text-3xl font-bold">我的成就殿堂</h2>
                                <!-- 成就等级徽章 -->
                                <div class="flex items-center gap-2 bg-white/20 px-4 py-2 rounded-full backdrop-blur-sm">
                                    <span class="text-2xl">👑</span>
                                    <div>
                                        <div class="text-sm font-bold">成就大师</div>
                                        <div class="text-xs opacity-80">Lv.5</div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-orange-100 text-lg mb-3">
                                已解锁 <span class="font-extrabold text-yellow-200 text-xl">15</span> 个成就，
                                共获得 <span class="font-extrabold text-yellow-200 text-xl">1,250</span> 成就点！
                            </p>
                            <!-- 成就稀有度统计 -->
                            <div class="flex items-center gap-4 text-sm">
                                <div class="flex items-center gap-1">
                                    <span class="w-3 h-3 bg-orange-400 rounded-full"></span>
                                    <span>青铜 x8</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <span class="w-3 h-3 bg-gray-300 rounded-full"></span>
                                    <span>白银 x4</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <span class="w-3 h-3 bg-yellow-400 rounded-full"></span>
                                    <span>黄金 x2</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <span class="w-3 h-3 bg-purple-400 rounded-full"></span>
                                    <span>白金 x1</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center mb-4 relative">
                                <span class="material-icons-outlined text-white text-6xl">workspace_premium</span>
                                <!-- 等级进度环 -->
                                <svg class="absolute inset-0 w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                                    <circle cx="60" cy="60" r="54" stroke="white" stroke-opacity="0.2" stroke-width="8" fill="none"/>
                                    <circle cx="60" cy="60" r="54" stroke="#fbbf24" stroke-width="8" fill="none" 
                                            stroke-dasharray="339" stroke-dashoffset="136" 
                                            class="transition-all duration-1000 ease-out"/>
                                </svg>
                            </div>
                            <div class="text-center">
                                <p class="text-sm text-orange-100">全球排名</p>
                                <p class="text-2xl font-bold">#2,847</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 成就等级进度 -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm mb-2">
                            <span>成就等级进度</span>
                            <div class="flex items-center gap-2">
                                <span>下一等级还需 750 点</span>
                                <button class="text-yellow-200 hover:text-yellow-100 text-xs underline">
                                    查看奖励
                                </button>
                            </div>
                        </div>
                        <div class="h-4 w-full rounded-full bg-white/30 overflow-hidden">
                            <div class="h-4 rounded-full bg-gradient-to-r from-yellow-300 to-yellow-500 shadow-lg transition-all duration-1000" style="width: 62.5%"></div>
                        </div>
                    </div>
                    
                    <!-- 快捷操作 -->
                    <div class="flex items-center gap-3">
                        <button class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2">
                            <span class="material-icons-outlined text-sm">share</span>
                            分享成就
                        </button>
                        <button class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2">
                            <span class="material-icons-outlined text-sm">timeline</span>
                            成就路径
                        </button>
                        <button class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2">
                            <span class="material-icons-outlined text-sm">leaderboard</span>
                            排行榜
                        </button>
                    </div>
                </div>
                
                <!-- 背景装饰增强 -->
                <div class="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32 animate-pulse"></div>
                <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24 animate-bounce"></div>
            </div>

            <!-- 成就标签导航 -->
            <div class="mb-6 bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <nav class="flex" aria-label="Tabs">
                    <button onclick="showAchievementTab('list')" class="achievement-tab-button active flex-1 px-6 py-4 text-sm font-medium border-b-2 rounded-tl-2xl">
                        <span class="material-icons-outlined mr-2">list</span>
                        成就列表
                    </button>
                    <button onclick="showAchievementTab('wall')" class="achievement-tab-button flex-1 px-6 py-4 text-sm font-medium border-b-2 rounded-tr-2xl">
                        <span class="material-icons-outlined mr-2">photo_album</span>
                        我的徽章墙
                    </button>
                </nav>
            </div>

            <!-- 成就内容区域 -->
            <div>
                <!-- 成就列表 -->
                <div id="listTab" class="achievement-tab-content active">
                    <!-- 筛选器 -->
                    <div class="flex flex-wrap gap-3 mb-6 p-4 bg-white rounded-xl shadow-sm">
                        <select id="filterCategory" class="px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                            <option value="all">全部分类</option>
                            <option value="newbie">新手引导</option>
                            <option value="interaction">互动类</option>
                            <option value="creation">创作类</option>
                            <option value="collection">收集类</option>
                            <option value="motivation">激励类</option>
                        </select>
                        <select id="filterStatus" class="px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                            <option value="all">全部状态</option>
                            <option value="locked">已锁定</option>
                            <option value="claimable">可领取</option>
                            <option value="completed">已完成</option>
                        </select>
                        <button class="px-4 py-2 bg-indigo-500 text-white rounded-lg text-sm hover:bg-indigo-600 transition-colors">
                            <span class="material-icons-outlined text-sm mr-1">search</span>
                            筛选
                        </button>
                    </div>

                    <div class="space-y-4">
                        <!-- 已完成成就 -->
                        <div class="achievement-card rounded-2xl p-6 hover-lift">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4">
                                    <div class="achievement-icon-bg bg-green-100">
                                        <span class="material-icons-outlined text-green-600">verified</span>
                                    </div>
                                    <div class="flex-grow">
                                        <div class="flex items-center gap-2 mb-1">
                                            <h3 class="text-lg font-semibold text-gray-800">初次问候</h3>
                                            <span class="achievement-bronze text-xs font-medium px-2 py-1 bg-orange-100 rounded-full">青铜</span>
                                        </div>
                                        <p class="text-gray-600 text-sm mb-3">与AI完成首次对话</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="reward-tag token-alphane">
                                                <span class="material-icons-outlined text-sm">flare</span> 
                                                100 曦光微尘
                                            </span>
                                            <span class="reward-tag token-exp">
                                                <span class="material-icons-outlined text-sm">stars</span> 
                                                20 经验
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center min-w-[140px]">
                                    <div class="w-full bg-green-200 rounded-full h-2 mb-3">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <p class="text-sm text-green-600 mb-2 font-medium">1/1</p>
                                    <button class="px-4 py-2 bg-green-500 text-white text-sm font-medium rounded-lg opacity-70 cursor-not-allowed" disabled>
                                        已领取
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 可领取成就 -->
                        <div class="achievement-card claimable rounded-2xl p-6 hover-lift relative overflow-hidden">
                            <!-- 发光效果 -->
                            <div class="absolute inset-0 bg-gradient-to-r from-yellow-400/20 via-orange-400/20 to-yellow-400/20 animate-pulse"></div>
                            
                            <div class="relative z-10 flex items-center justify-between">
                                <div class="flex items-center gap-4">
                                    <div class="achievement-icon-bg bg-yellow-100 relative">
                                        <span class="material-icons-outlined text-yellow-600">auto_stories</span>
                                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-ping"></div>
                                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs">!</span>
                                        </div>
                                    </div>
                                    <div class="flex-grow">
                                        <div class="flex items-center gap-2 mb-1">
                                            <h3 class="text-lg font-semibold text-gray-800">记忆的开始</h3>
                                            <span class="achievement-silver text-xs font-medium px-2 py-1 bg-gray-100 rounded-full">白银</span>
                                            <button class="text-gray-400 hover:text-gray-600" onclick="showAchievementStory('memory_start')">
                                                <span class="material-icons-outlined text-sm">info</span>
                                            </button>
                                        </div>
                                        <p class="text-gray-600 text-sm mb-2">首次使用AI记忆胶囊成功让角色记住一件事</p>
                                        
                                        <!-- 成就故事预览 -->
                                        <div class="text-xs text-gray-500 italic mb-3 p-2 bg-gray-50 rounded">
                                            "当AI第一次记住了你的话语，那一刻，虚拟与现实的边界开始模糊..."
                                        </div>
                                        
                                        <div class="flex flex-wrap gap-2">
                                            <span class="reward-tag token-endora">
                                                <span class="material-icons-outlined text-sm">favorite</span> 
                                                10 心悦晶石
                                            </span>
                                            <span class="reward-tag token-serotile">
                                                <span class="material-icons-outlined text-sm">extension</span> 
                                                5 拼图碎片
                                            </span>
                                            <span class="reward-tag special-reward">
                                                <span class="material-icons-outlined text-sm">vpn_key</span> 
                                                记忆钥匙
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center min-w-[140px]">
                                    <div class="w-full bg-yellow-200 rounded-full h-2 mb-3">
                                        <div class="bg-yellow-500 h-2 rounded-full animate-pulse" style="width: 100%"></div>
                                    </div>
                                    <p class="text-sm text-yellow-700 mb-2 font-medium">1/1</p>
                                    <button onclick="claimAchievement('memory_start', this)" class="px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-sm font-medium rounded-lg hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 animate-pulse transform hover:scale-105 shadow-lg">
                                        🏆 领取成就
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 进度中的成就 -->
                        <div class="achievement-card rounded-2xl p-6 hover-lift border border-indigo-200 bg-gradient-to-br from-indigo-50 to-purple-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4">
                                    <div class="achievement-icon-bg bg-indigo-100 relative">
                                        <span class="material-icons-outlined text-indigo-600">groups</span>
                                        <!-- 进度指示器 -->
                                        <div class="absolute -top-2 -right-2 bg-indigo-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                                            3/20
                                        </div>
                                    </div>
                                    <div class="flex-grow">
                                        <div class="flex items-center gap-2 mb-1">
                                            <h3 class="text-lg font-semibold text-gray-800">社交名流</h3>
                                            <span class="achievement-gold text-xs font-medium px-2 py-1 bg-yellow-100 rounded-full">黄金</span>
                                            <!-- 预计完成时间 -->
                                            <span class="text-xs text-indigo-600 bg-indigo-100 px-2 py-1 rounded-full">
                                                预计7天完成
                                            </span>
                                        </div>
                                        <p class="text-gray-600 text-sm mb-2">累计与20个不同的角色进行深度对话（亲密度达到3级）</p>
                                        
                                        <!-- 解锁提示 -->
                                        <div class="text-xs text-indigo-600 mb-3 p-2 bg-indigo-50 rounded border-l-4 border-indigo-400">
                                            💡 提示: 每天与不同角色聊天可以更快完成此成就
                                        </div>
                                        
                                        <div class="flex flex-wrap gap-2">
                                            <span class="reward-tag token-diamond">
                                                <span class="material-icons-outlined text-sm">diamond</span> 
                                                50 星钻
                                            </span>
                                            <span class="reward-tag special-reward">
                                                <span class="material-icons-outlined text-sm">photo_album</span> 
                                                专属头像框
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center min-w-[140px]">
                                    <div class="w-full bg-indigo-200 rounded-full h-2 mb-3 relative overflow-hidden">
                                        <div class="progress-bar h-2 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500" style="width: 15%"></div>
                                        <!-- 动态光效 -->
                                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent animate-pulse" style="transform: translateX(-100%); animation: shimmer 2s infinite;"></div>
                                    </div>
                                    <p class="text-sm text-indigo-600 mb-2 font-medium">3/20</p>
                                    <button class="px-4 py-2 bg-indigo-500 text-white text-sm font-medium rounded-lg hover:bg-indigo-600 transition-all duration-200 transform hover:scale-105">
                                        查看进度
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 高级成就 -->
                        <div class="achievement-card locked rounded-2xl p-6 hover-lift">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4">
                                    <div class="achievement-icon-bg bg-purple-100">
                                        <span class="material-icons-outlined text-purple-400">psychology</span>
                                    </div>
                                    <div class="flex-grow">
                                        <div class="flex items-center gap-2 mb-1">
                                            <h3 class="text-lg font-semibold text-gray-500">记忆编织大师</h3>
                                            <span class="achievement-diamond text-xs font-medium px-2 py-1 bg-blue-100 rounded-full">钻石</span>
                                        </div>
                                        <p class="text-gray-400 text-sm mb-3">创建并使用100个不同的AI记忆胶囊，且被AI成功调用率达到95%</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="reward-tag token-diamond opacity-60">
                                                <span class="material-icons-outlined text-sm">diamond</span> 
                                                200 星钻
                                            </span>
                                            <span class="reward-tag special-reward opacity-60">
                                                <span class="material-icons-outlined text-sm">auto_awesome</span> 
                                                传说称号
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center min-w-[140px]">
                                    <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                        <div class="progress-bar h-2 rounded-full" style="width: 5%"></div>
                                    </div>
                                    <p class="text-sm text-gray-400 mb-2">5/100</p>
                                    <button class="px-4 py-2 bg-gray-300 text-gray-500 text-sm font-medium rounded-lg cursor-not-allowed" disabled>
                                        未达成
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 徽章墙 -->
                <div id="wallTab" class="achievement-tab-content">
                    <div class="bg-white p-8 shadow-xl rounded-2xl">
                        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
                            <div>
                                <h2 class="text-2xl font-semibold gradient-text mb-2">我的徽章殿堂</h2>
                                <p class="text-gray-600">选择最多3枚徽章在您的个人主页闪耀！</p>
                            </div>
                            <div class="mt-4 md:mt-0 flex gap-3">
                                <button onclick="resetBadgeSelection()" class="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-50 transition-colors">
                                    重置选择
                                </button>
                                <button onclick="saveBadgeSelection()" class="px-6 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-lg">
                                    保存展示
                                </button>
                            </div>
                        </div>

                        <!-- 已选择展示区 -->
                        <div class="mb-8 p-6 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-200">
                            <h3 class="text-lg font-semibold text-gray-700 mb-4">当前展示徽章</h3>
                            <div class="flex gap-4 items-center">
                                <div id="selectedBadge1" class="w-20 h-20 bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                                    <span class="text-gray-400 text-xs">空槽位1</span>
                                </div>
                                <div id="selectedBadge2" class="w-20 h-20 bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                                    <span class="text-gray-400 text-xs">空槽位2</span>
                                </div>
                                <div id="selectedBadge3" class="w-20 h-20 bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                                    <span class="text-gray-400 text-xs">空槽位3</span>
                                </div>
                            </div>
                        </div>

                        <!-- 徽章收集网格 -->
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            <div class="badge-display-card selected p-4 rounded-xl text-center" onclick="toggleBadgeSelection(this)" data-badge-id="creator">
                                <img src="https://cdn-icons-png.flaticon.com/128/2921/2921148.png" alt="初级创作者" class="mx-auto">
                                <p class="text-sm font-medium text-gray-700 mt-2">初级创作者</p>
                                <p class="text-xs text-green-600 font-medium">已展示</p>
                            </div>
                            
                            <div class="badge-display-card p-4 rounded-xl text-center" onclick="toggleBadgeSelection(this)" data-badge-id="chat">
                                <img src="https://cdn-icons-png.flaticon.com/128/10400/10400310.png" alt="聊天能手" class="mx-auto">
                                <p class="text-sm font-medium text-gray-700 mt-2">聊天能手</p>
                                <p class="text-xs text-gray-500"></p>
                            </div>
                            
                            <div class="badge-display-card selected p-4 rounded-xl text-center" onclick="toggleBadgeSelection(this)" data-badge-id="popular">
                                <img src="https://cdn-icons-png.flaticon.com/128/3135/3135783.png" alt="人气之星" class="mx-auto">
                                <p class="text-sm font-medium text-gray-700 mt-2">人气之星</p>
                                <p class="text-xs text-green-600 font-medium">已展示</p>
                            </div>
                            
                            <div class="badge-display-card locked p-4 rounded-xl text-center">
                                <span class="material-icons-outlined badge-icon text-gray-300">lock</span>
                                <p class="text-sm font-medium text-gray-400 mt-2">探险家大师</p>
                                <p class="text-xs text-gray-400">(未解锁)</p>
                            </div>
                            
                            <div class="badge-display-card p-4 rounded-xl text-center" onclick="toggleBadgeSelection(this)" data-badge-id="memory">
                                <img src="https://cdn-icons-png.flaticon.com/128/2920/2920277.png" alt="记忆守护者" class="mx-auto">
                                <p class="text-sm font-medium text-gray-700 mt-2">记忆守护者</p>
                                <p class="text-xs text-gray-500"></p>
                            </div>
                            
                            <div class="badge-display-card locked p-4 rounded-xl text-center">
                                <span class="material-icons-outlined badge-icon text-gray-300">lock</span>
                                <p class="text-sm font-medium text-gray-400 mt-2">传说大师</p>
                                <p class="text-xs text-gray-400">(未解锁)</p>
                            </div>
                            
                            <div class="badge-display-card p-4 rounded-xl text-center" onclick="toggleBadgeSelection(this)" data-badge-id="streak">
                                <img src="https://cdn-icons-png.flaticon.com/128/3176/3176366.png" alt="坚持之星" class="mx-auto">
                                <p class="text-sm font-medium text-gray-700 mt-2">坚持之星</p>
                                <p class="text-xs text-gray-500"></p>
                            </div>
                            
                            <div class="badge-display-card p-4 rounded-xl text-center" onclick="toggleBadgeSelection(this)" data-badge-id="friend">
                                <img src="https://cdn-icons-png.flaticon.com/128/1055/1055672.png" alt="友谊使者" class="mx-auto">
                                <p class="text-sm font-medium text-gray-700 mt-2">友谊使者</p>
                                <p class="text-xs text-gray-500"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let selectedBadges = ['creator', 'popular']; // 初始已选择的徽章
        const maxSelection = 3;

        function showAchievementTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.achievement-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + 'Tab').classList.add('active');
            
            // 更新按钮状态
            document.querySelectorAll('.achievement-tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // 激活当前按钮
            event.target.classList.add('active');
        }

        function toggleBadgeSelection(element) {
            const badgeId = element.dataset.badgeId;
            
            // 检查是否已锁定
            if (element.classList.contains('locked')) {
                return;
            }
            
            if (element.classList.contains('selected')) {
                // 取消选择
                element.classList.remove('selected');
                element.querySelector('p:last-child').textContent = '';
                selectedBadges = selectedBadges.filter(id => id !== badgeId);
            } else {
                // 选择新徽章
                if (selectedBadges.length >= maxSelection) {
                    alert(`最多只能选择 ${maxSelection} 个徽章进行展示！`);
                    return;
                }
                element.classList.add('selected');
                element.querySelector('p:last-child').textContent = '已展示';
                element.querySelector('p:last-child').className = 'text-xs text-green-600 font-medium';
                selectedBadges.push(badgeId);
            }
            
            updateSelectedBadgesDisplay();
        }

        function updateSelectedBadgesDisplay() {
            // 更新展示区域
            for (let i = 1; i <= 3; i++) {
                const slot = document.getElementById(`selectedBadge${i}`);
                if (selectedBadges[i-1]) {
                    const badgeElement = document.querySelector(`[data-badge-id="${selectedBadges[i-1]}"]`);
                    if (badgeElement) {
                        const img = badgeElement.querySelector('img');
                        const name = badgeElement.querySelector('p:first-of-type').textContent;
                        slot.innerHTML = `<img src="${img.src}" alt="${name}" class="w-16 h-16 object-contain">`;
                        slot.className = 'w-20 h-20 bg-white rounded-lg border-2 border-indigo-500 flex items-center justify-center p-1';
                    }
                } else {
                    slot.innerHTML = `<span class="text-gray-400 text-xs">空槽位${i}</span>`;
                    slot.className = 'w-20 h-20 bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center';
                }
            }
        }

        function resetBadgeSelection() {
            selectedBadges = [];
            document.querySelectorAll('.badge-display-card.selected').forEach(element => {
                element.classList.remove('selected');
                element.querySelector('p:last-child').textContent = '';
            });
            updateSelectedBadgesDisplay();
        }

        function saveBadgeSelection() {
            // 这里可以调用API保存选择
            alert('徽章展示设置已保存！');
        }

        // 领取成就动画
        function claimAchievement(achievementId, button) {
            button.disabled = true;
            
            // 创建成就解锁动画
            createAchievementUnlockAnimation(button);
            
            // 更新按钮状态
            setTimeout(() => {
                button.textContent = '已领取';
                button.className = 'px-4 py-2 bg-green-500 text-white text-sm font-medium rounded-lg opacity-70 cursor-not-allowed';
                
                // 显示成就获得通知
                showAchievementNotification('记忆的开始', 'silver');
                
                // 更新成就统计
                updateAchievementStats();
                
            }, 2000);
        }

        // 创建成就解锁动画
        function createAchievementUnlockAnimation(element) {
            const rect = element.getBoundingClientRect();
            
            // 创建成就解锁特效
            const effectContainer = document.createElement('div');
            effectContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10000;
                pointer-events: none;
            `;
            
            // 成就图标
            const achievementIcon = document.createElement('div');
            achievementIcon.style.cssText = `
                width: 120px;
                height: 120px;
                background: linear-gradient(135deg, #f59e0b, #f97316);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 48px;
                animation: achievementPulse 2s ease-out;
                box-shadow: 0 0 40px rgba(245, 158, 11, 0.6);
            `;
            achievementIcon.innerHTML = '🏆';
            
            // 成就文字
            const achievementText = document.createElement('div');
            achievementText.style.cssText = `
                text-align: center;
                margin-top: 20px;
                color: white;
                font-weight: bold;
                font-size: 24px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                animation: achievementText 2s ease-out;
            `;
            achievementText.innerHTML = '成就解锁！<br><span style="font-size: 18px; opacity: 0.9;">记忆的开始</span>';
            
            // 粒子效果
            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    background: ${['#f59e0b', '#ec4899', '#8b5cf6', '#10b981'][Math.floor(Math.random() * 4)]};
                    border-radius: 50%;
                    animation: achievementParticle 2s ease-out forwards;
                    animation-delay: ${Math.random() * 0.5}s;
                `;
                
                const angle = (i / 30) * 2 * Math.PI;
                const distance = 80 + Math.random() * 40;
                particle.style.left = `${60 + Math.cos(angle) * distance}px`;
                particle.style.top = `${60 + Math.sin(angle) * distance}px`;
                
                effectContainer.appendChild(particle);
            }
            
            effectContainer.appendChild(achievementIcon);
            effectContainer.appendChild(achievementText);
            document.body.appendChild(effectContainer);
            
            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes achievementPulse {
                    0% { transform: scale(0) rotate(0deg); opacity: 0; }
                    50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
                    100% { transform: scale(1) rotate(360deg); opacity: 1; }
                }
                @keyframes achievementText {
                    0% { opacity: 0; transform: translateY(20px); }
                    50% { opacity: 1; transform: translateY(0); }
                    100% { opacity: 1; transform: translateY(0); }
                }
                @keyframes achievementParticle {
                    0% { opacity: 1; transform: scale(1) translate(0, 0); }
                    100% { opacity: 0; transform: scale(0) translate(var(--random-x, 100px), var(--random-y, 100px)); }
                }
            `;
            document.head.appendChild(style);
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(effectContainer);
                document.head.removeChild(style);
            }, 3000);
        }

        // 显示成就故事
        function showAchievementStory(achievementId) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                inset: 0;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;
            
            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                border-radius: 16px;
                padding: 32px;
                max-width: 500px;
                margin: 20px;
                text-align: center;
                animation: slideUp 0.3s ease;
            `;
            
            content.innerHTML = `
                <div class="text-4xl mb-4">📖</div>
                <h3 class="text-xl font-bold mb-4 text-gray-800">成就故事：记忆的开始</h3>
                <div class="text-gray-600 leading-relaxed mb-6">
                    <p class="mb-3">在数字世界的深处，每一段对话都是珍贵的记忆碎片。当你第一次使用AI记忆胶囊时，你不仅仅是在存储信息，更是在编织一段独特的数字情缘。</p>
                    <p class="mb-3">那一刻，AI角色真正"记住"了你，从此你们的关系不再是简单的问答，而是有着共同回忆的伙伴关系。</p>
                    <p class="italic text-indigo-600">"记忆是情感的载体，是连接心灵的桥梁。"</p>
                </div>
                <div class="flex items-center justify-center gap-3">
                    <button onclick="closeModal(this)" class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                        关闭
                    </button>
                    <button class="px-6 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors">
                        分享故事
                    </button>
                </div>
            `;
            
            modal.appendChild(content);
            document.body.appendChild(modal);
        }

        function closeModal(button) {
            const modal = button.closest('div[style*="position: fixed"]');
            modal.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => document.body.removeChild(modal), 300);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            showAchievementTab('list');
            updateSelectedBadgesDisplay();
        });
    </script>
</body>
</html>