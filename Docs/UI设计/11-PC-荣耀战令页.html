<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>荣耀战令 - Alphane.ai</title>
    
    <!-- 字体和图标 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        :root {
            /* Alphane品牌色彩 */
            --alphane-primary: #6366f1;
            --alphane-secondary: #8b5cf6;
            --alphane-accent: #ec4899;
            --alphane-warm: #f59e0b;
            --alphane-success: #10b981;
            --alphane-gold: #fbbf24;
            --alphane-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 动画效果 */
        .hover-lift {
            transition: all 0.3s ease;
        }
        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, var(--alphane-primary), var(--alphane-accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 代币样式 */
        .token-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        /* 战令轨道样式 */
        .battle-pass-track {
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            padding: 1rem 0;
            scroll-snap-type: x mandatory;
        }

        .battle-pass-level {
            min-width: 180px;
            scroll-snap-align: start;
        }

        .reward-item {
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
            backdrop-filter: blur(8px);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .reward-item.free {
            border-color: #e2e8f0;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        }

        .reward-item.premium {
            border-color: var(--alphane-gold);
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            position: relative;
        }

        .reward-item.premium::before {
            content: '👑';
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, var(--alphane-gold), #f59e0b);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .reward-item.locked {
            opacity: 0.6;
            filter: grayscale(0.5);
        }

        .reward-item.claimable {
            border-color: var(--alphane-success);
            animation: reward-pulse 2s infinite;
        }

        @keyframes reward-pulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
            50% { box-shadow: 0 0 0 8px rgba(16, 185, 129, 0); }
        }

        .reward-item.claimed {
            opacity: 0.7;
            filter: grayscale(0.3);
        }

        /* 等级指示器 */
        .level-indicator {
            background: linear-gradient(135deg, var(--alphane-primary), var(--alphane-secondary));
            color: white;
            font-weight: bold;
            transform: rotate(-5deg);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* 进度条动画 */
        .progress-bar {
            background: linear-gradient(90deg, var(--alphane-gold), #f59e0b);
            transition: width 1s ease-in-out;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 季节主题背景 */
        .season-bg {
            background: linear-gradient(135deg, 
                rgba(99, 102, 241, 0.1) 0%, 
                rgba(139, 92, 246, 0.1) 50%, 
                rgba(236, 72, 153, 0.1) 100%);
            backdrop-filter: blur(20px);
        }

        /* 移动端优化 */
        @media (max-width: 1023px) {
            .desktop-sidebar {
                display: none;
            }
            .main-content {
                margin-left: 0 !important;
            }
        }

        /* 倒计时样式 */
        .countdown-timer {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            animation: timer-pulse 2s infinite;
        }

        @keyframes timer-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- 侧边栏 -->
    <aside class="desktop-sidebar fixed left-0 top-0 z-50 h-full w-80 bg-white shadow-xl border-r border-gray-200">
        <!-- 品牌头部 -->
        <div class="flex items-center justify-between px-6 py-5 border-b border-gray-100">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <span class="text-white text-xl font-bold">A</span>
                </div>
                <div>
                    <h1 class="text-lg font-bold gradient-text">Alphane.ai</h1>
                    <p class="text-xs text-gray-500">有温度的AI伴侣</p>
                </div>
            </div>
        </div>

        <!-- 用户状态区 -->
        <div class="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-100">
            <!-- 四种代币显示 -->
            <div class="grid grid-cols-2 gap-2 mb-4">
                <div class="token-card rounded-lg p-3 text-center">
                    <div class="flex items-center justify-center gap-1 mb-1">
                        <span class="text-orange-500 text-sm">🔥</span>
                        <span class="text-xs font-medium text-gray-600">曦光微尘</span>
                    </div>
                    <p class="text-sm font-bold text-gray-800">1,250</p>
                </div>
                <div class="token-card rounded-lg p-3 text-center">
                    <div class="flex items-center justify-center gap-1 mb-1">
                        <span class="text-blue-500 text-sm">💎</span>
                        <span class="text-xs font-medium text-gray-600">心悦晶石</span>
                    </div>
                    <p class="text-sm font-bold text-gray-800">89</p>
                </div>
                <div class="token-card rounded-lg p-3 text-center">
                    <div class="flex items-center justify-center gap-1 mb-1">
                        <span class="text-purple-500 text-sm">🧩</span>
                        <span class="text-xs font-medium text-gray-600">忆境拼图</span>
                    </div>
                    <p class="text-sm font-bold text-gray-800">23</p>
                </div>
                <div class="token-card rounded-lg p-3 text-center">
                    <div class="flex items-center justify-center gap-1 mb-1">
                        <span class="text-pink-500 text-sm">💧</span>
                        <span class="text-xs font-medium text-gray-600">羁绊之露</span>
                    </div>
                    <p class="text-sm font-bold text-gray-800">157</p>
                </div>
            </div>

            <!-- 战令币显示 -->
            <div class="bg-white rounded-lg p-3 shadow-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <span class="text-yellow-500 text-lg">🪙</span>
                        <span class="text-sm font-medium text-gray-700">战令币</span>
                    </div>
                    <span class="text-lg font-bold gradient-text">350</span>
                </div>
            </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="flex-1 overflow-y-auto p-4">
            <div class="space-y-1">
                <a href="2-PC-首页-最终版.html" class="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                    <span class="material-icons-outlined text-xl">cottage</span>
                    <span class="font-medium">主页</span>
                </a>
                <a href="#" class="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                    <span class="material-icons-outlined text-xl">account_circle</span>
                    <span class="font-medium">个人中心</span>
                </a>
            </div>

            <div class="pt-6 mb-4">
                <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3 px-2">激励与成长</h3>
                <div class="space-y-1">
                    <a href="9-PC-任务中心页.html" class="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                        <span class="material-icons-outlined text-xl">checklist_rtl</span>
                        <span class="font-medium">任务中心</span>
                        <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">3</span>
                    </a>
                    <a href="10-PC-成就徽章页.html" class="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                        <span class="material-icons-outlined text-xl">emoji_events</span>
                        <span class="font-medium">成就徽章</span>
                    </a>
                    <a href="#" class="flex items-center gap-3 p-3 rounded-lg bg-indigo-50 text-indigo-600 border-l-4 border-indigo-500">
                        <span class="material-icons-outlined text-xl">military_tech</span>
                        <span class="font-medium">荣耀战令</span>
                        <span class="ml-auto text-xs font-medium">Lv.25</span>
                    </a>
                </div>
            </div>

            <!-- 会员推广 -->
            <div class="bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl p-4 text-white mb-4">
                <h4 class="font-bold mb-1 flex items-center gap-2">
                    <span>👑</span>
                    Diamond Pass
                </h4>
                <p class="text-xs opacity-90 mb-3">解锁所有付费奖励</p>
                <button class="w-full bg-white text-orange-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                    立即升级
                </button>
            </div>
        </nav>

        <!-- 用户信息 -->
        <div class="border-t border-gray-200 p-4">
            <div class="flex items-center gap-3">
                <img src="https://picsum.photos/40/40?random=user" alt="User" class="w-10 h-10 rounded-full border-2 border-indigo-200">
                <div class="flex-1">
                    <p class="font-medium text-gray-800">月光下的旅人</p>
                    <p class="text-xs text-gray-500">Free Pass</p>
                </div>
                <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="material-icons-outlined">settings</span>
                </button>
            </div>
        </div>
    </aside>

    <!-- 主内容区 -->
    <main class="lg:ml-80">
        <!-- 顶部导航栏 -->
        <header class="sticky top-0 z-30 bg-white/80 backdrop-blur-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex-1">
                    <h1 class="text-2xl font-bold">
                        <span class="gradient-text">荣耀战令:</span>
                        <span class="text-purple-600 ml-2">S1 - 星尘远征</span>
                    </h1>
                    <div class="flex items-center gap-4 mt-1">
                        <p class="text-sm text-gray-500">
                            赛季剩余: <span id="countdown" class="countdown-timer text-white px-2 py-1 rounded-md text-xs font-bold">25天 09小时 15分钟</span>
                        </p>
                        <div class="text-sm text-gray-500">
                            经验加成: <span class="text-green-600 font-semibold">+25%</span>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <button class="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-semibold rounded-lg hover:from-yellow-500 hover:to-orange-600 transition-all duration-300 shadow-lg hover-lift">
                        <span class="material-icons-outlined text-sm">stars</span>
                        激活 Diamond Pass
                    </button>
                    <button class="flex items-center gap-2 px-4 py-2 border-2 border-indigo-500 text-indigo-600 bg-white font-semibold rounded-lg hover:bg-indigo-50 transition-colors">
                        <span class="material-icons-outlined text-sm">add</span>
                        购买等级
                    </button>
                </div>
            </div>
        </header>

        <div class="p-6">
            <!-- 战令进度概览 -->
            <div class="season-bg rounded-2xl p-8 mb-8 border border-white/20 shadow-xl relative overflow-hidden">
                <!-- 背景动态元素 -->
                <div class="absolute inset-0 opacity-10">
                    <div class="absolute top-10 left-10 w-6 h-6 bg-white rounded-full animate-ping"></div>
                    <div class="absolute top-1/3 right-20 w-4 h-4 bg-yellow-300 rounded-full animate-bounce"></div>
                    <div class="absolute bottom-20 left-1/4 w-8 h-8 bg-purple-300 rounded-full animate-pulse"></div>
                    <div class="absolute top-1/2 right-1/3 w-3 h-3 bg-pink-300 rounded-full animate-ping" style="animation-delay: 1.5s"></div>
                </div>
                
                <div class="relative z-10 flex flex-col lg:flex-row justify-between lg:items-end gap-6">
                    <div>
                        <div class="flex items-center gap-4 mb-4">
                            <span class="text-4xl">⚔️</span>
                            <div>
                                <p class="text-lg font-medium text-gray-700">当前战令等级</p>
                                <div class="flex items-center gap-2 mt-1">
                                    <span class="bg-indigo-100 text-indigo-700 text-xs px-2 py-1 rounded-full font-medium">
                                        距离下级还需 750 XP
                                    </span>
                                    <button onclick="showLevelPreview()" class="text-indigo-600 hover:text-indigo-800 text-xs underline">
                                        预览下级奖励
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-baseline gap-3 mb-4">
                            <span class="text-6xl font-extrabold gradient-text">25</span>
                            <span class="text-2xl font-semibold text-gray-500">/ 100</span>
                            <button onclick="levelUpAnimation()" class="bg-yellow-400 hover:bg-yellow-500 text-yellow-900 px-3 py-1 rounded-full text-sm font-bold transition-all duration-300 transform hover:scale-105">
                                ⚡ 升级
                            </button>
                        </div>
                        <!-- 等级里程碑 -->
                        <div class="flex items-center gap-2 text-sm text-gray-600 mb-2">
                            <span class="material-icons-outlined text-sm">emoji_events</span>
                            <span>下个里程碑: 等级30 - 解锁传说奖励</span>
                        </div>
                    </div>
                    <div class="flex-1 lg:max-w-md">
                        <div class="flex justify-between text-sm mb-2">
                            <span class="text-gray-600">当前经验</span>
                            <div class="flex items-center gap-2">
                                <span class="font-semibold text-yellow-600">1250 / 2000 XP</span>
                                <!-- 经验加成显示 -->
                                <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium">
                                    +25% 加成中
                                </span>
                            </div>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-4 overflow-hidden shadow-inner relative">
                            <div class="progress-bar h-4 rounded-full transition-all duration-1000" style="width: 62.5%"></div>
                            <!-- 经验值增长动画点 -->
                            <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">下一级还需: <span class="font-semibold text-yellow-600">750 XP</span></p>
                    </div>
                    <div class="flex gap-4">
                        <div class="text-center p-4 bg-white/50 rounded-xl backdrop-blur-sm hover-lift cursor-pointer" onclick="showRewardHistory()">
                            <p class="text-2xl font-bold text-green-600">15</p>
                            <p class="text-xs text-gray-600">已领取奖励</p>
                        </div>
                        <div class="text-center p-4 bg-white/50 rounded-xl backdrop-blur-sm hover-lift cursor-pointer" onclick="showClaimableRewards()">
                            <p class="text-2xl font-bold text-blue-600 animate-pulse">10</p>
                            <p class="text-xs text-gray-600">可领取奖励</p>
                        </div>
                        <div class="text-center p-4 bg-white/50 rounded-xl backdrop-blur-sm hover-lift cursor-pointer">
                            <p class="text-2xl font-bold text-purple-600">#127</p>
                            <p class="text-xs text-gray-600">全球排名</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 战令快捷功能区 -->
            <div class="mb-8 grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- 每日任务快捷 -->
                <div class="bg-white rounded-xl p-4 shadow-lg hover-lift">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800 flex items-center gap-2">
                            <span class="material-icons-outlined text-blue-600">today</span>
                            每日任务
                        </h3>
                        <span class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full font-medium">3个可完成</span>
                    </div>
                    <div class="text-sm text-gray-600 mb-2">完成度: 8/12</div>
                    <div class="w-full bg-blue-200 rounded-full h-2 mb-3">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: 67%"></div>
                    </div>
                    <button class="w-full text-xs bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        前往完成
                    </button>
                </div>
                
                <!-- 好友对战 -->
                <div class="bg-white rounded-xl p-4 shadow-lg hover-lift">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800 flex items-center gap-2">
                            <span class="material-icons-outlined text-purple-600">people</span>
                            好友对战
                        </h3>
                        <span class="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full font-medium">限时x2</span>
                    </div>
                    <div class="text-sm text-gray-600 mb-2">本周: 3胜2负</div>
                    <div class="flex items-center gap-1 mb-3">
                        <span class="text-xs text-green-600">胜率: 60%</span>
                        <span class="text-xs text-gray-400">|</span>
                        <span class="text-xs text-blue-600">排名: #45</span>
                    </div>
                    <button class="w-full text-xs bg-purple-500 text-white py-2 rounded-lg hover:bg-purple-600 transition-colors">
                        寻找对手
                    </button>
                </div>
                
                <!-- 创作挑战 -->
                <div class="bg-white rounded-xl p-4 shadow-lg hover-lift">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800 flex items-center gap-2">
                            <span class="material-icons-outlined text-green-600">palette</span>
                            创作挑战
                        </h3>
                        <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full font-medium">新</span>
                    </div>
                    <div class="text-sm text-gray-600 mb-2">本月主题: 科幻世界</div>
                    <div class="text-xs text-green-600 mb-3">
                        💎 特殊奖励: 限定角色卡
                    </div>
                    <button class="w-full text-xs bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors">
                        参与挑战
                    </button>
                </div>
                
                <!-- 社区活动 -->
                <div class="bg-gradient-to-br from-pink-400 to-purple-500 rounded-xl p-4 text-white">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold flex items-center gap-2">
                            <span class="material-icons-outlined">celebration</span>
                            社区活动
                        </h3>
                        <span class="countdown-timer px-2 py-1 rounded text-xs font-bold bg-white/20">12:34:56</span>
                    </div>
                    <div class="text-sm opacity-90 mb-2">春节特别活动</div>
                    <div class="text-xs opacity-80 mb-3">
                        🎊 奖励: 专属头像 + 3000经验
                    </div>
                    <button class="w-full text-xs bg-white/20 hover:bg-white/30 text-white py-2 rounded-lg transition-colors">
                        立即参与
                    </button>
                </div>
            </div>

            <!-- 战令奖励轨道 -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">战令奖励轨道</h2>
                    <div class="flex items-center gap-2 text-sm text-gray-500">
                        <span class="material-icons-outlined text-sm">swipe</span>
                        <span>滑动查看所有等级奖励</span>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <div class="battle-pass-track" id="battlePassTrack">
                        <!-- 等级奖励将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 战令任务 -->
            <div class="grid lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-2">
                            <span class="material-icons-outlined text-indigo-600">assignment</span>
                            战令任务
                        </h3>
                        <a href="9-PC-任务中心页.html" class="text-sm font-medium text-indigo-600 hover:underline">查看全部</a>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
                            <div class="flex justify-between items-start mb-2">
                                <div>
                                    <p class="font-medium text-gray-800">每日任务：完成3场对话</p>
                                    <p class="text-xs text-gray-500">奖励: <span class="font-semibold text-green-600">战令经验 x50</span></p>
                                </div>
                                <div class="flex items-center gap-1 text-green-600">
                                    <span class="material-icons-outlined text-sm">check_circle</span>
                                    <span class="text-sm font-semibold">已完成</span>
                                </div>
                            </div>
                            <div class="w-full bg-green-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                            </div>
                        </div>
                        
                        <div class="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                            <div class="flex justify-between items-start mb-2">
                                <div>
                                    <p class="font-medium text-gray-800">每周任务：累计获得100个赞</p>
                                    <p class="text-xs text-gray-500">奖励: <span class="font-semibold text-blue-600">战令经验 x200</span></p>
                                </div>
                                <button class="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-md hover:bg-blue-200 transition-colors">
                                    进行中
                                </button>
                            </div>
                            <div class="flex justify-between text-xs text-gray-600 mb-1">
                                <span>进度</span>
                                <span>30/100</span>
                            </div>
                            <div class="w-full bg-blue-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 战令商店预览 -->
                <div class="bg-white rounded-xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-2">
                            <span class="material-icons-outlined text-yellow-600">store</span>
                            战令商店
                        </h3>
                        <div class="flex items-center gap-1 text-yellow-600">
                            <span class="text-lg">🪙</span>
                            <span class="font-bold">350</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <div class="p-3 border border-gray-200 rounded-lg text-center hover-lift cursor-pointer">
                            <div class="text-2xl mb-1">⚡</div>
                            <p class="text-xs font-medium text-gray-700">经验加成卡</p>
                            <p class="text-xs text-yellow-600 font-bold">50 战令币</p>
                        </div>
                        <div class="p-3 border border-gray-200 rounded-lg text-center hover-lift cursor-pointer">
                            <div class="text-2xl mb-1">🎯</div>
                            <p class="text-xs font-medium text-gray-700">等级跳跃券</p>
                            <p class="text-xs text-yellow-600 font-bold">100 战令币</p>
                        </div>
                    </div>
                    
                    <button class="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-semibold py-3 rounded-lg hover:from-yellow-500 hover:to-orange-600 transition-all duration-300 shadow-lg flex items-center justify-center gap-2">
                        <span class="material-icons-outlined">shopping_bag</span>
                        进入战令商店
                    </button>
                </div>
            </div>

            <!-- 季节信息 -->
            <div class="bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold mb-2">S1 - 星尘远征</h3>
                        <p class="text-sm opacity-90 mb-4">在浩瀚星海中探索未知的奇迹，收集珍贵的星尘，解锁专属奖励。</p>
                        <div class="flex items-center gap-4">
                            <div class="text-center">
                                <p class="text-xs opacity-80">赛季开始</p>
                                <p class="font-semibold">2024.01.01</p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs opacity-80">赛季结束</p>
                                <p class="font-semibold">2024.03.31</p>
                            </div>
                        </div>
                    </div>
                    <div class="text-6xl opacity-80">🌟</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 战令轨道数据
        const battlePassData = Array.from({length: 100}, (_, i) => {
            const level = i + 1;
            const isUnlocked = level <= 25;
            const isClaimed = level <= 15;
            const isClaimable = level > 15 && level <= 25;
            
            return {
                level,
                isUnlocked,
                isClaimed,
                isClaimable,
                freeReward: {
                    type: level % 5 === 0 ? 'special' : 'token',
                    name: level % 5 === 0 ? `特殊奖励 ${Math.floor(level/5)}` : '曦光微尘',
                    amount: level % 5 === 0 ? null : `x${100 + level * 10}`,
                    icon: level % 5 === 0 ? '🎁' : '🔥'
                },
                premiumReward: {
                    type: level % 10 === 0 ? 'legendary' : (level % 5 === 0 ? 'epic' : 'token'),
                    name: level % 10 === 0 ? '传说道具' : (level % 5 === 0 ? '史诗装备' : '心悦晶石'),
                    amount: level % 10 === 0 || level % 5 === 0 ? null : `x${50 + level * 5}`,
                    icon: level % 10 === 0 ? '👑' : (level % 5 === 0 ? '⚡' : '💎')
                }
            };
        });

        // 渲染战令轨道
        function renderBattlePass() {
            const container = document.getElementById('battlePassTrack');
            container.innerHTML = battlePassData.map(data => `
                <div class="battle-pass-level flex-shrink-0">
                    <div class="relative mb-4">
                        <div class="level-indicator absolute -top-3 -left-3 px-3 py-1 rounded-full text-xs z-10">
                            Lv.${data.level}
                        </div>
                        
                        <!-- 免费奖励 -->
                        <div class="reward-item free ${data.isClaimed ? 'claimed' : (data.isClaimable ? 'claimable' : 'locked')} p-4 rounded-lg mb-3">
                            <div class="text-2xl mb-2 text-center">${data.freeReward.icon}</div>
                            <p class="text-xs font-medium text-gray-700 text-center mb-1">${data.freeReward.name}</p>
                            ${data.freeReward.amount ? `<p class="text-xs text-gray-500 text-center">${data.freeReward.amount}</p>` : ''}
                        </div>
                        
                        <!-- 付费奖励 -->
                        <div class="reward-item premium ${data.isClaimed ? 'claimed' : (data.isClaimable ? 'claimable' : 'locked')} p-4 rounded-lg mb-3">
                            <div class="text-2xl mb-2 text-center">${data.premiumReward.icon}</div>
                            <p class="text-xs font-medium text-gray-700 text-center mb-1">${data.premiumReward.name}</p>
                            ${data.premiumReward.amount ? `<p class="text-xs text-gray-500 text-center">${data.premiumReward.amount}</p>` : ''}
                        </div>
                        
                        <!-- 领取按钮 -->
                        <button class="w-full py-2 px-3 text-xs font-semibold rounded-lg transition-all duration-300 ${
                            data.isClaimed ? 'bg-green-500 text-white cursor-not-allowed' : 
                            (data.isClaimable ? 'bg-green-500 text-white hover:bg-green-600 animate-pulse' : 
                            'bg-gray-300 text-gray-500 cursor-not-allowed')
                        }" ${data.isClaimable ? 'onclick="claimReward(' + data.level + ')"' : ''}>
                            ${data.isClaimed ? '已领取' : (data.isClaimable ? '领取奖励' : `等级 ${data.level}`)}
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 领取奖励
        function claimReward(level) {
            const data = battlePassData.find(d => d.level === level);
            if (data && data.isClaimable) {
                data.isClaimed = true;
                data.isClaimable = false;
                renderBattlePass();
                
                // 显示奖励通知
                showRewardNotification(data);
            }
        }

        // 显示奖励通知
        function showRewardNotification(data) {
            // 这里可以添加奖励通知逻辑
            alert(`恭喜获得等级 ${data.level} 奖励！`);
        }

        // 倒计时功能
        function updateCountdown() {
            const countdownElement = document.getElementById('countdown');
            const endDate = new Date('2024-03-31T23:59:59');
            const now = new Date();
            const timeLeft = endDate - now;
            
            if (timeLeft > 0) {
                const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                
                countdownElement.textContent = `${days}天 ${hours.toString().padStart(2, '0')}小时 ${minutes.toString().padStart(2, '0')}分钟`;
            } else {
                countdownElement.textContent = '赛季已结束';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderBattlePass();
            updateCountdown();
            setInterval(updateCountdown, 60000); // 每分钟更新一次
        });

        // 等级提升动画
        function levelUpAnimation() {
            // 创建全屏等级提升特效
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                inset: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: levelUpFade 3s ease-out forwards;
            `;
            
            overlay.innerHTML = `
                <div style="text-align: center; color: white;">
                    <div style="font-size: 120px; margin-bottom: 20px; animation: levelUpBounce 1s ease-out;">⚡</div>
                    <div style="font-size: 48px; font-weight: bold; margin-bottom: 10px; animation: levelUpSlide 1s ease-out 0.5s both;">
                        等级提升！
                    </div>
                    <div style="font-size: 32px; opacity: 0.9; animation: levelUpSlide 1s ease-out 1s both;">
                        等级 25 → 26
                    </div>
                    <div style="margin-top: 30px; font-size: 18px; opacity: 0.8; animation: levelUpSlide 1s ease-out 1.5s both;">
                        🎁 获得新奖励：史诗装备碎片 x5
                    </div>
                </div>
            `;
            
            // 粒子爆炸效果
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background: ${['#fbbf24', '#ec4899', '#8b5cf6', '#10b981'][Math.floor(Math.random() * 4)]};
                    border-radius: 50%;
                    animation: levelUpParticle 3s ease-out forwards;
                    animation-delay: ${Math.random() * 2}s;
                `;
                
                const angle = (i / 50) * 2 * Math.PI;
                const distance = 100 + Math.random() * 200;
                particle.style.left = `${50 + Math.cos(angle) * distance / 10}%`;
                particle.style.top = `${50 + Math.sin(angle) * distance / 10}%`;
                
                overlay.appendChild(particle);
            }
            
            document.body.appendChild(overlay);
            
            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes levelUpFade {
                    0% { opacity: 0; }
                    20% { opacity: 1; }
                    80% { opacity: 1; }
                    100% { opacity: 0; }
                }
                @keyframes levelUpBounce {
                    0% { transform: scale(0) rotate(0deg); }
                    50% { transform: scale(1.3) rotate(180deg); }
                    100% { transform: scale(1) rotate(360deg); }
                }
                @keyframes levelUpSlide {
                    0% { opacity: 0; transform: translateY(30px); }
                    100% { opacity: 1; transform: translateY(0); }
                }
                @keyframes levelUpParticle {
                    0% { opacity: 1; transform: scale(1); }
                    100% { opacity: 0; transform: scale(0) translate(200px, 200px); }
                }
            `;
            document.head.appendChild(style);
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(overlay);
                document.head.removeChild(style);
                
                // 更新等级显示
                updateLevelDisplay();
            }, 3000);
        }

        // 显示等级预览
        function showLevelPreview() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                inset: 0;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;
            
            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 40px; max-width: 600px; margin: 20px; text-align: center;">
                    <h3 style="font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #374151;">
                        🎯 等级 26 预览
                    </h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div style="padding: 20px; background: linear-gradient(135deg, #fef3c7, #fde68a); border-radius: 12px;">
                            <div style="font-size: 32px; margin-bottom: 10px;">🏆</div>
                            <div style="font-weight: bold; color: #92400e;">免费奖励</div>
                            <div style="font-size: 14px; color: #b45309;">史诗装备碎片 x5</div>
                        </div>
                        <div style="padding: 20px; background: linear-gradient(135deg, #fef3c7, #fde68a); border-radius: 12px; position: relative;">
                            <div style="position: absolute; top: -8px; right: -8px; background: #f59e0b; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px;">👑</div>
                            <div style="font-size: 32px; margin-bottom: 10px;">💎</div>
                            <div style="font-weight: bold; color: #92400e;">付费奖励</div>
                            <div style="font-size: 14px; color: #b45309;">传说角色卡 x1</div>
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e0e7ff, #c7d2fe); padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <div style="font-size: 18px; font-weight: bold; color: #3730a3; margin-bottom: 10px;">🌟 特殊里程碑</div>
                        <div style="font-size: 14px; color: #4338ca;">解锁新功能：自定义角色语音包</div>
                    </div>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="closeModal(this)" style="padding: 12px 24px; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer;">
                            关闭
                        </button>
                        <button style="padding: 12px 24px; background: linear-gradient(135deg, #f59e0b, #f97316); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            立即升级
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        // 显示奖励历史
        function showRewardHistory() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                inset: 0;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;
            
            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 500px; max-height: 80vh; overflow-y: auto; margin: 20px;">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 20px; color: #374151; text-align: center;">
                        🎁 已领取奖励历史
                    </h3>
                    <div style="space-y: 12px;">
                        <div style="padding: 15px; background: #f3f4f6; border-radius: 8px; border-left: 4px solid #10b981;">
                            <div style="font-weight: bold; color: #374151;">等级 25 奖励</div>
                            <div style="font-size: 14px; color: #6b7280; margin-top: 5px;">🔥 曦光微尘 x350 | 💎 心悦晶石 x25</div>
                            <div style="font-size: 12px; color: #9ca3af; margin-top: 5px;">2024-01-15 14:32</div>
                        </div>
                        <div style="padding: 15px; background: #f3f4f6; border-radius: 8px; border-left: 4px solid #10b981;">
                            <div style="font-weight: bold; color: #374151;">等级 20 里程碑</div>
                            <div style="font-size: 14px; color: #6b7280; margin-top: 5px;">⚡ 史诗装备 x1 | 🎨 专属头像框</div>
                            <div style="font-size: 12px; color: #9ca3af; margin-top: 5px;">2024-01-12 09:15</div>
                        </div>
                        <div style="padding: 15px; background: #f3f4f6; border-radius: 8px; border-left: 4px solid #10b981;">
                            <div style="font-weight: bold; color: #374151;">等级 15 奖励</div>
                            <div style="font-size: 14px; color: #6b7280; margin-top: 5px;">🧩 忆境拼图 x10 | 💧 羁绊之露 x50</div>
                            <div style="font-size: 12px; color: #9ca3af; margin-top: 5px;">2024-01-08 16:45</div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal(this)" style="padding: 10px 20px; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer;">
                            关闭
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        // 显示可领取奖励
        function showClaimableRewards() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                inset: 0;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;
            
            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 500px; margin: 20px;">
                    <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 20px; color: #374151; text-align: center;">
                        ⚡ 可领取奖励
                    </h3>
                    <div style="display: grid; gap: 15px;">
                        <div style="padding: 20px; background: linear-gradient(135deg, #fef3c7, #fde68a); border-radius: 12px; border: 2px solid #f59e0b;">
                            <div style="font-weight: bold; color: #92400e; margin-bottom: 10px;">等级 16-25 累计奖励</div>
                            <div style="font-size: 14px; color: #b45309; margin-bottom: 15px;">
                                🔥 曦光微尘 x1,250<br>
                                💎 心悦晶石 x85<br>
                                🧩 忆境拼图 x15
                            </div>
                            <button onclick="claimAllRewards()" style="width: 100%; padding: 10px; background: linear-gradient(135deg, #f59e0b, #f97316); color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                一键领取全部
                            </button>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal(this)" style="padding: 10px 20px; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer;">
                            关闭
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        // 一键领取全部奖励
        function claimAllRewards() {
            // 创建领取特效
            createMassClaimEffect();
            
            // 更新奖励数据
            setTimeout(() => {
                alert('成功领取所有奖励！获得大量资源！');
                // 关闭模态框
                const modal = document.querySelector('div[style*="position: fixed"]');
                if (modal) document.body.removeChild(modal);
            }, 2000);
        }

        // 批量领取特效
        function createMassClaimEffect() {
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                inset: 0;
                background: linear-gradient(135deg, rgba(245, 158, 11, 0.9), rgba(249, 115, 22, 0.9));
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10001;
                animation: massClaimFade 2s ease-out forwards;
            `;
            
            overlay.innerHTML = `
                <div style="text-align: center; color: white;">
                    <div style="font-size: 80px; margin-bottom: 20px; animation: massClaimSpin 1s ease-out;">🎁</div>
                    <div style="font-size: 36px; font-weight: bold; margin-bottom: 15px;">奖励雨来啦！</div>
                    <div style="font-size: 18px; opacity: 0.9;">连续领取 10 个等级奖励</div>
                </div>
            `;
            
            // 奖励雨效果
            for (let i = 0; i < 30; i++) {
                const reward = document.createElement('div');
                reward.style.cssText = `
                    position: absolute;
                    font-size: 24px;
                    animation: rewardRain 2s linear forwards;
                    animation-delay: ${Math.random() * 1.5}s;
                `;
                reward.textContent = ['🔥', '💎', '🧩', '💧', '⚡', '🎁'][Math.floor(Math.random() * 6)];
                reward.style.left = Math.random() * 100 + '%';
                reward.style.top = '-50px';
                
                overlay.appendChild(reward);
            }
            
            document.body.appendChild(overlay);
            
            const style = document.createElement('style');
            style.textContent = `
                @keyframes massClaimFade {
                    0% { opacity: 0; }
                    20% { opacity: 1; }
                    80% { opacity: 1; }
                    100% { opacity: 0; }
                }
                @keyframes massClaimSpin {
                    0% { transform: scale(0) rotate(0deg); }
                    100% { transform: scale(1) rotate(360deg); }
                }
                @keyframes rewardRain {
                    0% { transform: translateY(-50px) rotate(0deg); opacity: 1; }
                    100% { transform: translateY(calc(100vh + 50px)) rotate(360deg); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
            
            setTimeout(() => {
                document.body.removeChild(overlay);
                document.head.removeChild(style);
            }, 2000);
        }
    </script>
</body>
</html>