<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆胶囊管理 - Alphane.ai</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        body {
            font-family: 'Inter', 'Noto Sans', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f0f4f8 100%);
            min-height: 100vh;
        }
        .memory-sidebar a.active { @apply bg-gradient-to-r from-sky-100 to-indigo-100 text-sky-700 border-sky-500 shadow-md; }
        .memory-sidebar a { @apply border-transparent hover:bg-sky-50 hover:text-sky-600 transition-all; }

        .memory-capsule-card {
            @apply bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] flex flex-col;
            border-left: 4px solid transparent;
            backdrop-filter: blur(10px);
        }
        .memory-capsule-card.type-user-added { @apply border-l-sky-500; }
        .memory-capsule-card.type-ai-learned { @apply border-l-purple-500; }
        .memory-capsule-card.important { @apply ring-2 ring-amber-400 shadow-amber-200; }

        .memory-image-placeholder {
            @apply w-full h-32 bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center text-slate-400;
        }
        .memory-tag {
            @apply inline-block bg-gradient-to-r from-slate-100 to-slate-200 text-slate-600 text-xs font-medium px-2 py-0.5 rounded-full mr-1 mb-1 hover:shadow-sm transition-all;
        }
        .emotion-tag {
             @apply text-xs px-2 py-1 rounded-full flex items-center gap-1 font-semibold shadow-sm;
        }
        
        .token-display {
            @apply flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-semibold;
        }
        .token-alphane { @apply bg-orange-100 text-orange-700; }
        .token-endora { @apply bg-blue-100 text-blue-700; }
        .token-serotile { @apply bg-purple-100 text-purple-700; }
        .token-oxytol { @apply bg-pink-100 text-pink-700; }
        
        .floating-action-btn {
            @apply fixed bottom-8 right-8 bg-gradient-to-r from-sky-500 to-indigo-600 text-white rounded-full p-4 shadow-2xl hover:shadow-sky-300/50 transition-all duration-300 hover:scale-110 z-50;
        }
        
        .memory-stats-card {
            @apply bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/20;
        }
        
        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
            50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
        }
        
        .memory-capsule-card.important {
            animation: glow 3s ease-in-out infinite;
        }
    </style>
</head>
<body class="text-slate-800">
    <div class="flex h-screen overflow-hidden">
        <aside class="memory-sidebar fixed left-0 top-0 z-40 flex h-full w-72 flex-col border-r border-slate-200/50 bg-white/95 backdrop-blur-sm shadow-xl">
            <div class="flex items-center gap-2.5 px-6 py-5 border-b border-slate-200/50">
                <svg class="h-9 w-9 text-sky-500" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path></svg>
                <span class="text-2xl font-bold bg-gradient-to-r from-sky-600 to-indigo-600 bg-clip-text text-transparent">Alphane.ai</span>
            </div>
            
            <!-- 四种代币显示 -->
            <div class="p-4 space-y-2 border-b border-slate-200/50">
                <div class="token-display token-alphane">
                    <span class="material-icons-outlined text-base">local_fire_department</span>
                    <span>1,250</span>
                    <span class="text-xs opacity-70">曦光微尘</span>
                </div>
                <div class="token-display token-endora">
                    <span class="material-icons-outlined text-base">diamond</span>
                    <span>880</span>
                    <span class="text-xs opacity-70">心悦晶石</span>
                </div>
                <div class="token-display token-serotile">
                    <span class="material-icons-outlined text-base">extension</span>
                    <span>23</span>
                    <span class="text-xs opacity-70">忆境拼图</span>
                </div>
                <div class="token-display token-oxytol">
                    <span class="material-icons-outlined text-base">favorite</span>
                    <span>156</span>
                    <span class="text-xs opacity-70">羁绊之露</span>
                </div>
            </div>
            
            <nav class="flex-1 space-y-1 overflow-y-auto px-4 py-4">
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border-l-4" href="2-PC-首页.html">
                    <span class="material-icons-outlined text-xl">cottage</span> 主页
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border-l-4" href="8-PC-个人中心页.html">
                    <span class="material-icons-outlined text-xl">account_circle</span> 个人中心
                </a>
                 <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border-l-4 active" href="12-PC-记忆片段管理页.html">
                    <span class="material-icons-outlined text-xl">memory</span> 记忆胶囊
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border-l-4" href="13-PC-商店付费页.html">
                    <span class="material-icons-outlined text-xl">storefront</span> 商店
                </a>
            </nav>
        </aside>

        <main class="ml-72 flex-1 overflow-y-auto">
            <header class="sticky top-0 z-30 flex h-20 items-center justify-between border-b border-slate-200/50 bg-white/90 px-8 backdrop-blur-md">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">记忆胶囊管理</h1>
                    <p class="text-sm text-slate-500 mt-1">珍藏与AI伙伴的每一个重要瞬间和信息 ✨</p>
                </div>
                <div class="flex items-center gap-4">
                    <div class="bg-gradient-to-r from-sky-50 to-indigo-50 px-4 py-2 rounded-lg border border-sky-200">
                        <span class="text-sm font-medium text-sky-700">记忆容量: </span>
                        <span class="font-bold text-sky-800">156/200</span>
                    </div>
                    <button class="rounded-lg bg-gradient-to-r from-sky-500 to-indigo-600 hover:from-sky-600 hover:to-indigo-700 px-4 py-2.5 text-sm font-semibold text-white shadow-lg transition-all flex items-center gap-1.5 hover:scale-105">
                        <span class="material-icons-outlined text-base">add_circle</span> 创建新记忆片段
                    </button>
                </div>
            </header>

            <div class="p-8">
                <!-- 记忆统计信息 -->
                <div class="mb-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="memory-stats-card">
                        <div class="flex items-center gap-3">
                            <span class="material-icons-outlined text-3xl text-sky-500">psychology</span>
                            <div>
                                <p class="text-2xl font-bold text-slate-800">156</p>
                                <p class="text-sm text-slate-500">总记忆数</p>
                            </div>
                        </div>
                    </div>
                    <div class="memory-stats-card">
                        <div class="flex items-center gap-3">
                            <span class="material-icons-outlined text-3xl text-purple-500">auto_awesome</span>
                            <div>
                                <p class="text-2xl font-bold text-slate-800">89</p>
                                <p class="text-sm text-slate-500">AI已调用</p>
                            </div>
                        </div>
                    </div>
                    <div class="memory-stats-card">
                        <div class="flex items-center gap-3">
                            <span class="material-icons-outlined text-3xl text-amber-500">star</span>
                            <div>
                                <p class="text-2xl font-bold text-slate-800">23</p>
                                <p class="text-sm text-slate-500">重要记忆</p>
                            </div>
                        </div>
                    </div>
                    <div class="memory-stats-card">
                        <div class="flex items-center gap-3">
                            <span class="material-icons-outlined text-3xl text-green-500">calendar_month</span>
                            <div>
                                <p class="text-2xl font-bold text-slate-800">7</p>
                                <p class="text-sm text-slate-500">本周新增</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 筛选器 -->
                <div class="mb-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 items-end bg-white/90 backdrop-blur-sm p-6 shadow-lg rounded-xl border border-white/20">
                    <div class="input-group">
                        <label for="filter_character" class="block text-xs font-medium text-slate-600 mb-1">关联角色</label>
                        <select id="filter_character" class="form-select w-full rounded-lg border-slate-300 shadow-sm text-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90">
                            <option value="">所有角色</option>
                            <option value="aiko">🌸 Aiko (羁绊: 知己)</option>
                            <option value="ethan">⚔️ Ethan (羁绊: 亲密)</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="filter_storyline" class="block text-xs font-medium text-slate-600 mb-1">关联故事线</label>
                        <select id="filter_storyline" class="form-select w-full rounded-lg border-slate-300 shadow-sm text-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90">
                            <option value="">所有故事线</option>
                            <option value="story1">🏫 Aiko的校园奇遇</option>
                            <option value="story2">🗡️ Ethan的冒险之旅</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="filter_date" class="block text-xs font-medium text-slate-600 mb-1">创建日期</label>
                        <input type="date" id="filter_date" class="form-input w-full rounded-lg border-slate-300 shadow-sm text-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90">
                    </div>
                    <div class="input-group">
                        <label for="filter_tags" class="block text-xs font-medium text-slate-600 mb-1">自定义标签</label>
                        <input type="text" id="filter_tags" class="form-input w-full rounded-lg border-slate-300 shadow-sm text-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90" placeholder="如：生日, 重要">
                    </div>
                     <div class="input-group">
                        <label for="sort_by" class="block text-xs font-medium text-slate-600 mb-1">排序方式</label>
                        <select id="sort_by" class="form-select w-full rounded-lg border-slate-300 shadow-sm text-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90">
                            <option value="newest">最新创建</option>
                            <option value="oldest">最早创建</option>
                            <option value="ai_called">AI调用频率</option>
                            <option value="importance">重要程度</option>
                        </select>
                    </div>
                </div>

                <!-- 记忆卡片网格 -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3">
                    <div class="memory-capsule-card type-user-added important">
                        <div class="memory-image-placeholder bg-gradient-to-br from-pink-100 to-purple-100">
                            <span class="material-icons-outlined text-5xl text-pink-400">cake</span>
                        </div>
                        <div class="p-5 flex flex-col flex-grow">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="text-lg font-semibold text-slate-800 line-clamp-2">我的生日是10月26日，喜欢草莓蛋糕 🎂</h3>
                                <span class="emotion-tag bg-gradient-to-r from-yellow-100 to-amber-100 text-amber-700" title="重要记忆">
                                    <span class="material-icons-outlined text-sm">priority_high</span>重要
                                </span>
                            </div>
                            <div class="flex items-center gap-2 mb-2">
                                <p class="text-xs text-slate-500">关联角色:</p>
                                <a href="#" class="text-sky-600 hover:underline font-medium text-xs bg-sky-50 px-2 py-0.5 rounded-full">🌸 Aiko</a>
                                <span class="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full font-medium">羁绊: 知己</span>
                            </div>
                            <p class="text-sm text-slate-600 line-clamp-3 mb-3 flex-grow">
                                这是我主动告诉Aiko的，希望她能记住。每年生日都想和她一起庆祝！AI已经在3次对话中成功运用了这个记忆。
                            </p>
                            <div class="text-xs text-slate-400 mb-1 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm">person</span>
                                由你添加 · 2025-05-15
                            </div>
                            <div class="text-xs mb-3 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm text-purple-500">auto_awesome</span>
                                <span class="text-purple-600 font-medium">Aiko已在对话中运用3次</span>
                                <span class="bg-purple-100 text-purple-700 px-1 py-0.5 rounded text-xs font-bold">+15 羁绊值</span>
                            </div>
                            <div class="mt-auto flex flex-wrap gap-1 mb-3">
                                <span class="memory-tag">🎂 生日</span>
                                <span class="memory-tag">❤️ 喜好</span>
                                <span class="memory-tag">🌸 Aiko专属</span>
                            </div>
                            <div class="flex gap-2 border-t border-slate-100 pt-3">
                                <button class="flex-1 rounded-lg bg-gradient-to-r from-sky-500 to-indigo-600 px-3 py-2 text-xs font-semibold text-white hover:from-sky-600 hover:to-indigo-700 transition-all flex items-center justify-center gap-1">
                                    <span class="material-icons-outlined text-sm">visibility</span>查看/编辑
                                </button>
                                <button class="rounded-lg bg-slate-100 p-2 text-slate-600 hover:bg-slate-200 transition-colors" title="分享">
                                    <span class="material-icons-outlined text-base">share</span>
                                </button>
                                <button class="rounded-lg bg-red-50 p-2 text-red-600 hover:bg-red-100 transition-colors" title="删除">
                                    <span class="material-icons-outlined text-base">delete_outline</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="memory-capsule-card type-ai-learned">
                         <div class="memory-image-placeholder bg-gradient-to-br from-blue-100 to-cyan-100">
                            <span class="material-icons-outlined text-5xl text-blue-400">waves</span>
                        </div>
                        <div class="p-5 flex flex-col flex-grow">
                             <div class="flex justify-between items-start mb-2">
                                <h3 class="text-lg font-semibold text-slate-800 line-clamp-2">关于上次去海边的事</h3>
                                 <span class="emotion-tag bg-gradient-to-r from-green-100 to-emerald-100 text-green-700" title="开心回忆">
                                     <span class="material-icons-outlined text-sm">sentiment_very_satisfied</span>开心
                                 </span>
                            </div>
                            <div class="flex items-center gap-2 mb-2">
                                <p class="text-xs text-slate-500">关联角色:</p>
                                <a href="#" class="text-sky-600 hover:underline font-medium text-xs bg-sky-50 px-2 py-0.5 rounded-full">⚔️ Ethan</a>
                                <span class="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium">羁绊: 亲密</span>
                            </div>
                            <p class="text-sm text-slate-600 line-clamp-3 mb-3 flex-grow">
                                Ethan说："上次我们去海边，你捡到的那个贝壳真漂亮，还记得吗？你说那是大海的礼物。" 这段对话被自动记录为共同回忆。
                            </p>
                            <div class="text-xs text-slate-400 mb-3 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm">auto_awesome</span>
                                来自对话自动生成 · 2025-05-20
                            </div>
                             <div class="mt-auto flex flex-wrap gap-1 mb-3">
                                <span class="memory-tag">🏖️ 旅行</span>
                                <span class="memory-tag">🌊 海边</span>
                                <span class="memory-tag">⚔️ Ethan</span>
                            </div>
                            <div class="flex gap-2 border-t border-slate-100 pt-3">
                                <button class="flex-1 rounded-lg bg-gradient-to-r from-sky-500 to-indigo-600 px-3 py-2 text-xs font-semibold text-white hover:from-sky-600 hover:to-indigo-700 transition-all flex items-center justify-center gap-1">
                                    <span class="material-icons-outlined text-sm">visibility</span>查看详情
                                </button>
                                <button class="rounded-lg bg-slate-100 p-2 text-slate-600 hover:bg-slate-200 transition-colors" title="删除">
                                    <span class="material-icons-outlined text-base">delete_outline</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                     <div class="memory-capsule-card type-user-added">
                        <div class="p-5 flex flex-col items-center justify-center h-full border-2 border-dashed border-sky-300 hover:border-sky-400 transition-colors cursor-pointer rounded-xl bg-gradient-to-br from-sky-50/50 to-indigo-50/50">
                            <span class="material-icons-outlined text-5xl text-sky-400 mb-2">add_circle_outline</span>
                            <p class="text-md font-semibold text-sky-600">创建新记忆片段</p>
                            <p class="text-xs text-slate-500 mt-1 text-center">主动添加AI需要记住的信息</p>
                            <div class="mt-3 text-xs text-center">
                                <span class="bg-sky-100 text-sky-700 px-2 py-1 rounded-full font-medium">+10 心悦晶石奖励</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-10 flex justify-center">
                    <nav class="inline-flex rounded-lg shadow-lg bg-white border border-slate-200" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center rounded-l-lg border-r border-slate-200 px-3 py-2 text-sm font-medium text-slate-500 hover:bg-slate-50 hover:text-slate-700 transition-colors">
                            <span class="material-icons-outlined text-lg">chevron_left</span>
                        </a>
                        <a href="#" aria-current="page" class="relative z-10 inline-flex items-center bg-gradient-to-r from-sky-500 to-indigo-600 px-4 py-2 text-sm font-semibold text-white">1</a>
                        <a href="#" class="relative inline-flex items-center border-r border-slate-200 px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 transition-colors">2</a>
                        <a href="#" class="relative inline-flex items-center border-r border-slate-200 px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 transition-colors">3</a>
                        <a href="#" class="relative inline-flex items-center rounded-r-lg px-3 py-2 text-sm font-medium text-slate-500 hover:bg-slate-50 hover:text-slate-700 transition-colors">
                            <span class="material-icons-outlined text-lg">chevron_right</span>
                        </a>
                    </nav>
                </div>
            </div>
        </main>
        
        <!-- 浮动操作按钮 -->
        <button class="floating-action-btn">
            <span class="material-icons-outlined text-2xl">memory</span>
        </button>
    </div>
</body>
</html>