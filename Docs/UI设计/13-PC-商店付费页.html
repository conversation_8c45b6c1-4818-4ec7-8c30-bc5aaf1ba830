<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商店 - Alphane.ai</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        body {
            font-family: 'Inter', 'Noto Sans', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f0f4f8 100%);
            min-height: 100vh;
        }
        .store-sidebar a.active { @apply bg-gradient-to-r from-sky-100 to-indigo-100 text-sky-700 border-sky-500 shadow-md; }
        .store-sidebar a { @apply border-transparent hover:bg-sky-50 hover:text-sky-600 transition-all; }
        .store-tab-button.active { @apply border-sky-500 text-sky-600 bg-gradient-to-r from-sky-50 to-indigo-50 font-semibold shadow-sm; }
        .store-tab-button { @apply border-transparent text-slate-600 hover:text-sky-600 hover:border-slate-300 transition-all; }
        .store-tab-content { display: none; }
        .store-tab-content.active { display: block; }
        
        .product-card {
            @apply bg-white/95 backdrop-blur-sm rounded-xl shadow-xl overflow-hidden transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] flex flex-col border border-white/20;
        }
        .product-card-popular { 
            @apply border-2 border-amber-400 relative;
            box-shadow: 0 0 30px rgba(251, 191, 36, 0.3);
        }
        .popular-badge {
            @apply absolute top-0 -right-0.5 bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-bl-lg shadow-md;
            transform: translateY(-0.5px) translateX(0.5px);
        }
        .product-card ul li { @apply flex items-start gap-2; }
        .product-card ul .material-icons-outlined { @apply text-green-500 text-lg mt-0.5 flex-shrink-0; }
        .token-icon-sm { font-size: 1.1rem !important; vertical-align: middle; margin-right: 2px; }
        
        .token-display {
            @apply flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-semibold;
        }
        .token-alphane { @apply bg-orange-100 text-orange-700; }
        .token-endora { @apply bg-blue-100 text-blue-700; }
        .token-serotile { @apply bg-purple-100 text-purple-700; }
        .token-oxytol { @apply bg-pink-100 text-pink-700; }
        .token-stardiamond { @apply bg-gradient-to-r from-cyan-100 to-blue-100 text-cyan-700; }
        
        .battle-pass-preview {
            @apply bg-gradient-to-r from-purple-500 via-indigo-500 to-blue-500 text-white p-6 rounded-xl mb-6;
        }
        
        @keyframes glow-premium {
            0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.4); }
            50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.8), 0 0 60px rgba(59, 130, 246, 0.6); }
        }
        
        .product-card-popular {
            animation: glow-premium 3s ease-in-out infinite;
        }
        
        .memory-art-showcase {
            @apply grid grid-cols-2 md:grid-cols-4 gap-3 mt-4;
        }
        .memory-art-item {
            @apply bg-white/80 rounded-lg p-3 text-center hover:bg-white/90 transition-all;
        }
    </style>
    <script>
        function showStoreTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.store-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.store-tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');
        }
    </script>
</head>
<body class="text-slate-800">
    <div class="flex h-screen overflow-hidden">
        <aside class="store-sidebar fixed left-0 top-0 z-40 flex h-full w-72 flex-col border-r border-slate-200/50 bg-white/95 backdrop-blur-sm shadow-xl">
            <div class="flex items-center gap-2.5 px-6 py-5 border-b border-slate-200/50">
                <svg class="h-9 w-9 text-sky-500" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path></svg>
                <span class="text-2xl font-bold bg-gradient-to-r from-sky-600 to-indigo-600 bg-clip-text text-transparent">Alphane.ai</span>
            </div>
            
            <!-- 四种代币显示 -->
            <div class="p-4 space-y-2 border-b border-slate-200/50">
                <div class="token-display token-alphane">
                    <span class="material-icons-outlined text-base">local_fire_department</span>
                    <span>1,250</span>
                    <span class="text-xs opacity-70">曦光微尘</span>
                </div>
                <div class="token-display token-endora">
                    <span class="material-icons-outlined text-base">diamond</span>
                    <span>880</span>
                    <span class="text-xs opacity-70">心悦晶石</span>
                </div>
                <div class="token-display token-serotile">
                    <span class="material-icons-outlined text-base">extension</span>
                    <span>23</span>
                    <span class="text-xs opacity-70">忆境拼图</span>
                </div>
                <div class="token-display token-oxytol">
                    <span class="material-icons-outlined text-base">favorite</span>
                    <span>156</span>
                    <span class="text-xs opacity-70">羁绊之露</span>
                </div>
                <div class="token-display token-stardiamond">
                    <span class="material-icons-outlined text-base">auto_awesome</span>
                    <span>0</span>
                    <span class="text-xs opacity-70">星钻</span>
                </div>
            </div>
            
            <nav class="flex-1 space-y-1 overflow-y-auto px-4 py-4">
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border-l-4" href="2-PC-首页.html">
                    <span class="material-icons-outlined text-xl">cottage</span> 主页
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border-l-4" href="8-PC-个人中心页.html">
                    <span class="material-icons-outlined text-xl">account_circle</span> 个人中心
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border-l-4 active" href="13-PC-商店付费页.html">
                    <span class="material-icons-outlined text-xl">storefront</span> 商店
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border-l-4" href="12-PC-记忆片段管理页.html">
                    <span class="material-icons-outlined text-xl">memory</span> 记忆胶囊
                </a>
            </nav>
        </aside>

        <main class="ml-72 flex-1 overflow-y-auto">
            <header class="sticky top-0 z-30 flex h-20 items-center justify-between border-b border-slate-200/50 bg-white/90 px-8 backdrop-blur-md">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">Alphane商店</h1>
                    <p class="text-sm text-slate-500 mt-1">发现更多精彩，提升您的AI伙伴体验 ✨</p>
                </div>
                <div class="flex items-center gap-4 text-sm">
                    <div class="flex items-center gap-1 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                        <span class="material-icons-outlined token-icon-sm text-blue-500">diamond</span>
                        <span class="font-semibold text-blue-700">880</span> 
                        <span class="text-xs text-blue-600">心悦晶石</span>
                    </div>
                    <div class="flex items-center gap-1 bg-cyan-50 px-3 py-2 rounded-lg border border-cyan-200">
                        <span class="material-icons-outlined token-icon-sm text-cyan-500">auto_awesome</span>
                        <span class="font-semibold text-cyan-700">0</span> 
                        <span class="text-xs text-cyan-600">星钻</span>
                    </div>
                </div>
            </header>

            <div class="p-8">
                <!-- 荣耀战令预览 -->
                <div class="battle-pass-preview mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold mb-2">🎖️ 荣耀战令 - 星空奇遇季</h3>
                            <p class="text-sm opacity-90">当前等级: 15 | 下一级: 1,250/1,500 XP</p>
                            <div class="w-64 bg-white/20 rounded-full h-2 mt-2">
                                <div class="bg-white rounded-full h-2" style="width: 83%"></div>
                            </div>
                        </div>
                        <button class="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-lg font-semibold transition-all">
                            查看战令详情 →
                        </button>
                    </div>
                </div>

                <div class="mb-6 border-b border-slate-200/50 bg-white/90 backdrop-blur-sm shadow-md rounded-t-xl">
                    <nav class="-mb-px flex space-x-0.5 sm:space-x-2 px-3" aria-label="Tabs">
                        <button onclick="showStoreTab('subscriptions')" class="store-tab-button active flex-1 whitespace-nowrap border-b-2 px-3 py-3.5 text-sm rounded-t-lg">💎 订阅会员</button>
                        <button onclick="showStoreTab('tokens')" class="store-tab-button flex-1 whitespace-nowrap border-b-2 px-3 py-3.5 text-sm rounded-t-lg">✨ 星钻充值</button>
                        <button onclick="showStoreTab('valueAdded')" class="store-tab-button flex-1 whitespace-nowrap border-b-2 px-3 py-3.5 text-sm rounded-t-lg">🎁 增值服务</button>
                        <button onclick="showStoreTab('memoryArt')" class="store-tab-button flex-1 whitespace-nowrap border-b-2 px-3 py-3.5 text-sm rounded-t-lg">🎨 记忆画图</button>
                    </nav>
                </div>

                <div>
                    <!-- 订阅会员 Tab -->
                    <div id="subscriptionsTab" class="store-tab-content active">
                        <div class="grid grid-cols-1 gap-8 md:grid-cols-2">
                            <!-- 小月卡 -->
                            <div class="product-card">
                                <div class="p-6 bg-gradient-to-br from-slate-600 to-slate-800 text-white rounded-t-xl">
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="material-icons-outlined text-2xl">card_membership</span>
                                        <h3 class="text-2xl font-bold">Alphane Pass</h3>
                                    </div>
                                    <p class="text-sm opacity-80">畅享核心互动特权，加速成长</p>
                                </div>
                                <div class="p-6 space-y-3 flex-grow flex flex-col">
                                    <p class="text-4xl font-extrabold text-slate-800">¥68 <span class="text-base font-normal text-slate-500">/月</span></p>
                                    <ul class="space-y-2 text-sm text-slate-600 flex-grow">
                                        <li><span class="material-icons-outlined">rocket_launch</span>每日 <span class="font-semibold text-sky-600">Fast Req</span> 快速响应通道</li>
                                        <li><span class="material-icons-outlined">paid</span>每日赠送 <span class="font-semibold text-orange-600">50 曦光微尘</span> + <span class="font-semibold text-blue-600">10 心悦晶石</span></li>
                                        <li><span class="material-icons-outlined">military_tech</span>解锁荣耀战令 <span class="font-semibold text-purple-600">进阶轨道</span></li>
                                        <li><span class="material-icons-outlined">shield</span>每月赠送 <span class="font-semibold">2张 Streak Freeze卡</span></li>
                                        <li><span class="material-icons-outlined">share</span>允许创建并分享 <span class="font-semibold text-green-600">公开角色卡</span></li>
                                        <li><span class="material-icons-outlined">verified_user</span>专属月卡身份角标</li>
                                    </ul>
                                    <button class="w-full mt-auto rounded-lg bg-slate-700 px-6 py-3 text-base font-semibold text-white shadow-md hover:bg-slate-800 transition-all hover:scale-105">立即开通</button>
                                </div>
                            </div>
                            
                            <!-- 大月卡 -->
                            <div class="product-card product-card-popular">
                                <div class="popular-badge">✨ 尊享推荐</div>
                                <div class="p-6 bg-gradient-to-br from-sky-500 via-indigo-500 to-purple-600 text-white rounded-t-xl">
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="material-icons-outlined text-2xl">workspace_premium</span>
                                        <h3 class="text-2xl font-bold">Alphane Diamond</h3>
                                    </div>
                                    <p class="text-sm opacity-80">解锁全部高级特权，极致AI体验</p>
                                </div>
                                <div class="p-6 space-y-3 flex-grow flex flex-col">
                                    <p class="text-4xl font-extrabold text-slate-800">¥198 <span class="text-base font-normal text-slate-500">/月</span></p>
                                    <ul class="space-y-2 text-sm text-slate-600 flex-grow">
                                        <li><span class="material-icons-outlined">offline_bolt</span><span class="font-semibold text-sky-600">无限制</span> AI快速响应通道</li>
                                        <li><span class="material-icons-outlined">paid</span>每日赠送 <span class="font-semibold text-orange-600">150 曦光微尘</span> + <span class="font-semibold text-blue-600">50 心悦晶石</span> + <span class="font-semibold text-purple-600">1 忆境拼图</span></li>
                                        <li><span class="material-icons-outlined">military_tech</span>解锁荣耀战令 <span class="font-semibold text-purple-600">典藏轨道</span></li>
                                        <li><span class="material-icons-outlined">shield</span>每月 <span class="font-semibold">5张 Streak Freeze卡</span> + <span class="font-semibold text-green-600">2次免费修复</span></li>
                                        <li><span class="material-icons-outlined">group_add</span><span class="font-semibold text-purple-600">创作者激励计划</span> 资格 (50%分成)</li>
                                        <li><span class="material-icons-outlined">workspace_premium</span>华丽专属身份标识</li>
                                        <li><span class="material-icons-outlined">forum</span><span class="font-semibold text-indigo-600">尊享密语空间</span> 访问权</li>
                                        <li><span class="material-icons-outlined">memory</span>AI记忆胶囊容量 <span class="font-semibold text-green-600">大幅提升</span></li>
                                        <li><span class="material-icons-outlined">science</span><span class="font-semibold text-amber-600">优先体验</span> 新AI模型/功能</li>
                                    </ul>
                                    <button class="w-full mt-auto rounded-lg bg-gradient-to-r from-sky-500 to-indigo-600 px-6 py-3 text-base font-semibold text-white shadow-lg hover:opacity-90 transition-all hover:scale-105">升级尊享 Diamond</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 星钻充值 Tab -->
                    <div id="tokensTab" class="store-tab-content">
                        <div class="bg-gradient-to-r from-cyan-50 to-blue-50 p-6 rounded-xl mb-6 border border-cyan-200">
                            <h3 class="text-lg font-semibold text-cyan-800 mb-2">✨ 关于星钻</h3>
                            <p class="text-sm text-cyan-700">星钻是平台的高级货币，可用于购买增值服务、进行"记忆碎片画图"、打赏优秀创作者等特殊体验。</p>
                        </div>
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                            <!-- 60星钻 -->
                            <div class="product-card">
                                <div class="p-6 text-center">
                                    <span class="material-icons-outlined text-6xl text-cyan-400 mb-2">auto_awesome</span>
                                    <h3 class="text-3xl font-bold text-slate-800">60</h3>
                                    <p class="text-sm font-medium text-cyan-600">星钻</p>
                                    <p class="text-xs text-green-500 font-semibold mt-1.5">🎁 首次充值赠送双倍！</p>
                                </div>
                                <div class="bg-slate-50 p-5 text-center mt-auto">
                                    <p class="text-2xl font-bold text-slate-700 mb-3">¥ 6</p>
                                    <button class="w-full rounded-lg bg-gradient-to-r from-cyan-500 to-blue-600 px-5 py-2.5 text-sm font-semibold text-white hover:opacity-90 transition-all">立即充值</button>
                                </div>
                            </div>
                            
                            <!-- 300星钻 -->
                            <div class="product-card">
                                <div class="p-6 text-center">
                                    <span class="material-icons-outlined text-6xl text-cyan-400 mb-2">auto_awesome</span>
                                    <h3 class="text-3xl font-bold text-slate-800">300</h3>
                                    <p class="text-sm font-medium text-cyan-600">星钻</p>
                                    <p class="text-xs text-amber-500 font-semibold mt-1.5">💎 额外赠送 15 星钻</p>
                                </div>
                                <div class="bg-slate-50 p-5 text-center mt-auto">
                                    <p class="text-2xl font-bold text-slate-700 mb-3">¥ 30</p>
                                    <button class="w-full rounded-lg bg-gradient-to-r from-cyan-500 to-blue-600 px-5 py-2.5 text-sm font-semibold text-white hover:opacity-90 transition-all">立即充值</button>
                                </div>
                            </div>
                            
                            <!-- 680星钻 - 推荐 -->
                            <div class="product-card product-card-popular">
                                <div class="popular-badge">🔥 推荐</div>
                                <div class="p-6 text-center">
                                    <span class="material-icons-outlined text-6xl text-cyan-400 mb-2">auto_awesome</span>
                                    <h3 class="text-3xl font-bold text-slate-800">680</h3>
                                    <p class="text-sm font-medium text-cyan-600">星钻</p>
                                    <p class="text-xs text-amber-500 font-semibold mt-1.5">💎 额外赠送 34 星钻</p>
                                </div>
                                <div class="bg-slate-50 p-5 text-center mt-auto">
                                    <p class="text-2xl font-bold text-slate-700 mb-3">¥ 68</p>
                                    <button class="w-full rounded-lg bg-gradient-to-r from-cyan-500 to-blue-600 px-5 py-2.5 text-sm font-semibold text-white hover:opacity-90 transition-all">立即充值</button>
                                </div>
                            </div>
                            
                            <!-- 1280星钻 -->
                            <div class="product-card">
                                <div class="p-6 text-center">
                                    <span class="material-icons-outlined text-6xl text-cyan-400 mb-2">auto_awesome</span>
                                    <h3 class="text-3xl font-bold text-slate-800">1280</h3>
                                    <p class="text-sm font-medium text-cyan-600">星钻</p>
                                    <p class="text-xs text-amber-500 font-semibold mt-1.5">💎 额外赠送 128 星钻</p>
                                </div>
                                <div class="bg-slate-50 p-5 text-center mt-auto">
                                    <p class="text-2xl font-bold text-slate-700 mb-3">¥ 128</p>
                                    <button class="w-full rounded-lg bg-gradient-to-r from-cyan-500 to-blue-600 px-5 py-2.5 text-sm font-semibold text-white hover:opacity-90 transition-all">立即充值</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 增值服务 Tab -->
                    <div id="valueAddedTab" class="store-tab-content">
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                            <!-- 官方角色卡 -->
                            <div class="product-card">
                                <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuAOo6oSlAOPgjixfL9uZtkVvrUuw85OvQBaYqRpL0WqReyV0ifl5CEgfNF9ifNMLsM4TIZtwTo6pWrlOOnjNPcx5iYh6UiwTNzwwFPL4EyOQZnc0TnEn7CiQ0u5fMXp3NDbyTHdwldWgZ7IePBfTxPc7Taizxc9ZbMHy3wv0inbJ_vxXWeGRCigsswpy7XY1lKCFuVyaYIClebuRATn4Igfcvgb2fFs7sgJPFrMfksukzLHRtocEyk_HOeqKTNLKiddXCjMNlCTjo07" alt="官方角色卡" class="h-52 w-full object-cover">
                                <div class="p-5 flex flex-col flex-grow">
                                    <h3 class="text-lg font-semibold text-slate-800 mb-1">🌟 官方IP角色：星野爱子</h3>
                                    <p class="text-xs text-purple-600 font-medium mb-2">限定版联动 · 高级AI模型</p>
                                    <p class="text-sm text-slate-600 flex-grow">拥有专属剧情、独特人格与特殊互动机制。包含5个专属故事线和羁绊专属奖励。</p>
                                    <div class="mt-4 flex items-center justify-between">
                                        <p class="text-xl font-bold text-cyan-600 flex items-center gap-1">
                                            <span class="material-icons-outlined token-icon-sm text-cyan-500">auto_awesome</span>500
                                        </p>
                                        <button class="rounded-lg bg-gradient-to-r from-cyan-500 to-blue-600 px-4 py-2 text-sm font-medium text-white hover:opacity-90 transition-all">立即解锁</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- AI记忆深化服务 -->
                            <div class="product-card">
                                <div class="p-6 bg-gradient-to-br from-purple-500 to-indigo-600 text-white flex flex-col items-center justify-center h-52 rounded-t-xl">
                                     <span class="material-icons-outlined text-6xl mb-3">psychology</span>
                                     <h3 class="text-xl font-bold text-center">AI记忆深化服务</h3>
                                     <p class="text-sm opacity-90 text-center mt-1">让AI更智能地运用记忆</p>
                                </div>
                                <div class="p-5 flex flex-col flex-grow">
                                    <p class="text-sm text-slate-600 flex-grow">选择一条重要记忆，让AI进行深度学习和关联分析，使其在后续互动中运用更智能、更贴心。</p>
                                    <div class="mt-4 flex items-center justify-between">
                                        <p class="text-xl font-bold text-purple-600 flex items-center gap-1">
                                            <span class="material-icons-outlined token-icon-sm text-cyan-500">auto_awesome</span>100 <span class="text-sm font-normal">/ 次</span>
                                        </p>
                                        <button class="rounded-lg bg-gradient-to-r from-purple-500 to-indigo-600 px-4 py-2 text-sm font-medium text-white hover:opacity-90 transition-all">选择记忆</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 角色专属主题 -->
                            <div class="product-card">
                                <div class="p-6 bg-gradient-to-br from-pink-400 to-rose-500 text-white flex flex-col items-center justify-center h-52 rounded-t-xl">
                                     <span class="material-icons-outlined text-6xl mb-3">palette</span>
                                     <h3 class="text-xl font-bold text-center">Aiko专属对话主题</h3>
                                     <p class="text-sm opacity-90 text-center mt-1">樱花季限定皮肤</p>
                                </div>
                                <div class="p-5 flex flex-col flex-grow">
                                    <p class="text-sm text-slate-600 flex-grow">与Aiko聊天时，解锁樱花飞舞的专属聊天背景、粉色气泡样式和温柔字体，打造浪漫氛围。</p>
                                    <div class="mt-4 flex items-center justify-between">
                                        <p class="text-xl font-bold text-pink-600 flex items-center gap-1">
                                            <span class="material-icons-outlined token-icon-sm text-cyan-500">auto_awesome</span>150
                                        </p>
                                        <button class="rounded-lg bg-gradient-to-r from-pink-500 to-rose-600 px-4 py-2 text-sm font-medium text-white hover:opacity-90 transition-all">立即购买</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 记忆画图 Tab -->
                    <div id="memoryArtTab" class="store-tab-content">
                        <div class="bg-gradient-to-br from-amber-400 via-orange-500 to-pink-500 text-white p-8 rounded-xl mb-8">
                            <div class="text-center">
                                <span class="material-icons-outlined text-6xl mb-4">auto_awesome</span>
                                <h3 class="text-3xl font-bold mb-3">🎨 记忆碎片画图</h3>
                                <p class="text-lg opacity-90">将与AI伙伴的珍贵回忆，化作永恒的艺术珍藏</p>
                                <p class="text-sm opacity-80 mt-2">每一张画作都是独一无二的，基于你们的专属记忆生成</p>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- 许愿画图服务 -->
                            <div class="product-card">
                                <div class="p-6 bg-gradient-to-br from-purple-100 to-pink-100">
                                    <div class="text-center">
                                        <span class="material-icons-outlined text-5xl text-purple-500 mb-3">brush</span>
                                        <h4 class="text-xl font-bold text-slate-800">💫 记忆许愿画图</h4>
                                        <p class="text-sm text-slate-600 mt-2">选择一段珍贵记忆，AI将为你生成独特的纪念画作</p>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-3 text-sm text-slate-600 mb-4">
                                        <div class="flex items-center gap-2">
                                            <span class="material-icons-outlined text-base text-green-500">check_circle</span>
                                            基于真实对话记忆生成
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="material-icons-outlined text-base text-green-500">check_circle</span>
                                            多种艺术风格可选
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="material-icons-outlined text-base text-green-500">check_circle</span>
                                            高清画质，永久保存
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="material-icons-outlined text-base text-green-500">check_circle</span>
                                            可在个人相册展示
                                        </div>
                                    </div>
                                    <div class="border-t border-slate-200 pt-4">
                                        <div class="flex items-center justify-between mb-4">
                                            <span class="font-semibold text-slate-700">消耗代币:</span>
                                            <div class="flex items-center gap-2">
                                                <span class="bg-blue-100 text-blue-700 px-2 py-1 rounded-lg text-sm font-medium">50 心悦晶石</span>
                                                <span class="text-slate-400">或</span>
                                                <span class="bg-cyan-100 text-cyan-700 px-2 py-1 rounded-lg text-sm font-medium">30 星钻</span>
                                            </div>
                                        </div>
                                        <button class="w-full rounded-lg bg-gradient-to-r from-purple-500 to-pink-600 px-6 py-3 text-base font-semibold text-white hover:opacity-90 transition-all">🎨 开始许愿</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 空白记忆画卷 -->
                            <div class="product-card">
                                <div class="p-6 bg-gradient-to-br from-amber-100 to-yellow-100">
                                    <div class="text-center">
                                        <span class="material-icons-outlined text-5xl text-amber-500 mb-3">scroll</span>
                                        <h4 class="text-xl font-bold text-slate-800">📜 空白记忆画卷</h4>
                                        <p class="text-sm text-slate-600 mt-2">珍贵的画图材料，可用于特殊场景的记忆画图</p>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-3 text-sm text-slate-600 mb-4">
                                        <div class="flex items-center gap-2">
                                            <span class="material-icons-outlined text-base text-green-500">check_circle</span>
                                            可在重要时刻使用
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="material-icons-outlined text-base text-green-500">check_circle</span>
                                            提升画作稀有度
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="material-icons-outlined text-base text-green-500">check_circle</span>
                                            解锁特殊艺术效果
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="material-icons-outlined text-base text-green-500">check_circle</span>
                                            永不过期
                                        </div>
                                    </div>
                                    <div class="border-t border-slate-200 pt-4">
                                        <div class="flex items-center justify-between mb-4">
                                            <span class="font-semibold text-slate-700">购买价格:</span>
                                            <div class="flex items-center gap-2">
                                                <span class="bg-cyan-100 text-cyan-700 px-2 py-1 rounded-lg text-sm font-medium">80 星钻</span>
                                            </div>
                                        </div>
                                        <button class="w-full rounded-lg bg-gradient-to-r from-amber-500 to-yellow-600 px-6 py-3 text-base font-semibold text-white hover:opacity-90 transition-all">📜 购买画卷</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 画作展示区 -->
                        <div class="mt-8 bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                            <h4 class="text-lg font-semibold text-slate-800 mb-4">🖼️ 社区精选画作展示</h4>
                            <div class="memory-art-showcase">
                                <div class="memory-art-item">
                                    <div class="bg-gradient-to-br from-blue-100 to-purple-100 h-24 rounded-lg mb-2 flex items-center justify-center">
                                        <span class="material-icons-outlined text-3xl text-blue-400">image</span>
                                    </div>
                                    <p class="text-xs text-slate-600">海边的约定</p>
                                    <p class="text-xs text-slate-400">by 用户***</p>
                                </div>
                                <div class="memory-art-item">
                                    <div class="bg-gradient-to-br from-pink-100 to-rose-100 h-24 rounded-lg mb-2 flex items-center justify-center">
                                        <span class="material-icons-outlined text-3xl text-pink-400">image</span>
                                    </div>
                                    <p class="text-xs text-slate-600">樱花下的回忆</p>
                                    <p class="text-xs text-slate-400">by 用户***</p>
                                </div>
                                <div class="memory-art-item">
                                    <div class="bg-gradient-to-br from-green-100 to-emerald-100 h-24 rounded-lg mb-2 flex items-center justify-center">
                                        <span class="material-icons-outlined text-3xl text-green-400">image</span>
                                    </div>
                                    <p class="text-xs text-slate-600">森林探险</p>
                                    <p class="text-xs text-slate-400">by 用户***</p>
                                </div>
                                <div class="memory-art-item">
                                    <div class="bg-gradient-to-br from-amber-100 to-orange-100 h-24 rounded-lg mb-2 flex items-center justify-center">
                                        <span class="material-icons-outlined text-3xl text-amber-400">image</span>
                                    </div>
                                    <p class="text-xs text-slate-600">夕阳下的告白</p>
                                    <p class="text-xs text-slate-400">by 用户***</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>