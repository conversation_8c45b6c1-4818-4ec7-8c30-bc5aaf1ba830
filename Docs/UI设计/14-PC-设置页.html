<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - Alphane.ai</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        body {
            font-family: 'Inter', 'Noto Sans', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f0f4f8 100%);
            min-height: 100vh;
        }
        .settings-sidebar a.active { @apply bg-gradient-to-r from-sky-100 to-indigo-100 text-sky-700 border-sky-500 shadow-md font-semibold; }
        .settings-sidebar a { @apply border-transparent text-slate-600 hover:bg-slate-50 hover:text-slate-800 transition-all; }

        .settings-section {
            @apply bg-white/95 backdrop-blur-sm p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20;
        }
        .settings-section-title {
            @apply text-lg font-semibold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-4 pb-2 border-b border-slate-200/50;
        }
        .settings-item {
            @apply flex justify-between items-center py-3.5 border-b border-slate-200/40 last:border-b-0 hover:bg-slate-50/50 rounded-lg px-2 transition-all;
        }
        .settings-item-label {
            @apply text-sm font-medium text-slate-700;
        }
        .settings-item-description {
            @apply text-xs text-slate-500 mt-0.5;
        }
        .settings-item-action .material-icons-outlined {
            @apply text-slate-400 group-hover:text-sky-500 transition-colors;
        }
        .form-select-custom {
            @apply form-select rounded-lg border-slate-300/60 text-sm py-2 px-3 shadow-sm focus:border-sky-500 focus:ring-1 focus:ring-sky-500 bg-white/90;
        }
        .toggle-switch {
            @apply relative inline-flex items-center h-6 rounded-full w-11 cursor-pointer transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500;
        }
        .toggle-switch.on { 
            @apply bg-gradient-to-r from-sky-500 to-indigo-600; 
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.4);
        }
        .toggle-switch.off { @apply bg-slate-300 hover:bg-slate-400; }
        .toggle-switch-handle {
            @apply inline-block w-4.5 h-4.5 transform bg-white rounded-full shadow-md transition-transform duration-300 ease-in-out;
        }
        .toggle-switch.on .toggle-switch-handle { @apply translate-x-5; }
        .toggle-switch.off .toggle-switch-handle { @apply translate-x-0.5; }
        
        .token-display {
            @apply flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-semibold;
        }
        .token-alphane { @apply bg-orange-100 text-orange-700; }
        .token-endora { @apply bg-blue-100 text-blue-700; }
        .token-serotile { @apply bg-purple-100 text-purple-700; }
        .token-oxytol { @apply bg-pink-100 text-pink-700; }
        
        .feature-highlight {
            @apply bg-gradient-to-r from-sky-50 to-indigo-50 border border-sky-200 rounded-lg p-3;
        }
    </style>
    <script>
        function toggleSwitch(element) {
            element.classList.toggle('on');
            element.classList.toggle('off');
        }
    </script>
</head>
<body class="text-slate-800">
    <div class="flex h-screen overflow-hidden">
        <aside class="settings-sidebar fixed left-0 top-0 z-40 flex h-full w-72 flex-col border-r border-slate-200/50 bg-white/95 backdrop-blur-sm shadow-xl">
            <div class="flex items-center gap-2.5 px-6 py-5 border-b border-slate-200/50">
                <svg class="h-9 w-9 text-sky-500" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path></svg>
                <span class="text-2xl font-bold bg-gradient-to-r from-sky-600 to-indigo-600 bg-clip-text text-transparent">Alphane.ai</span>
            </div>
            
            <!-- 四种代币显示 -->
            <div class="p-4 space-y-2 border-b border-slate-200/50">
                <div class="token-display token-alphane">
                    <span class="material-icons-outlined text-base">local_fire_department</span>
                    <span>1,250</span>
                    <span class="text-xs opacity-70">曦光微尘</span>
                </div>
                <div class="token-display token-endora">
                    <span class="material-icons-outlined text-base">diamond</span>
                    <span>880</span>
                    <span class="text-xs opacity-70">心悦晶石</span>
                </div>
                <div class="token-display token-serotile">
                    <span class="material-icons-outlined text-base">extension</span>
                    <span>23</span>
                    <span class="text-xs opacity-70">忆境拼图</span>
                </div>
                <div class="token-display token-oxytol">
                    <span class="material-icons-outlined text-base">favorite</span>
                    <span>156</span>
                    <span class="text-xs opacity-70">羁绊之露</span>
                </div>
            </div>
            
            <nav class="flex-1 space-y-1 overflow-y-auto px-3 py-3">
                <a class="group flex items-center gap-3 rounded-lg px-3.5 py-2.5 text-sm font-medium border-l-4" href="2-PC-首页.html">
                    <span class="material-icons-outlined text-xl">cottage</span> 主页
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3.5 py-2.5 text-sm font-medium border-l-4" href="8-PC-个人中心页.html">
                    <span class="material-icons-outlined text-xl">account_circle</span> 个人中心
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3.5 py-2.5 text-sm font-medium border-l-4" href="15-PC-通知中心页.html">
                    <span class="material-icons-outlined text-xl">notifications_active</span> 通知中心
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3.5 py-2.5 text-sm font-medium border-l-4 active" href="14-PC-设置页.html">
                    <span class="material-icons-outlined text-xl">settings_suggest</span> 系统设置
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3.5 py-2.5 text-sm font-medium border-l-4" href="13-PC-商店付费页.html">
                    <span class="material-icons-outlined text-xl">storefront</span> 商店
                </a>
            </nav>
            <div class="mt-auto border-t border-slate-200/50 p-4">
                <button class="w-full group flex items-center justify-center gap-2.5 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-600 hover:bg-red-100 hover:text-red-700 transition-colors">
                    <span class="material-icons-outlined text-xl">logout</span> 退出登录
                </button>
            </div>
        </aside>

        <main class="ml-72 flex-1 overflow-y-auto">
            <header class="sticky top-0 z-30 flex h-20 items-center justify-between border-b border-slate-200/50 bg-white/90 px-8 backdrop-blur-md">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">系统设置</h1>
                    <p class="text-sm text-slate-500 mt-1">个性化您的AI伙伴体验 ⚙️</p>
                </div>
                <div class="flex items-center gap-3">
                    <div class="bg-gradient-to-r from-sky-50 to-indigo-50 px-4 py-2 rounded-lg border border-sky-200">
                        <span class="text-sm font-medium text-sky-700">会员状态: </span>
                        <span class="font-bold text-indigo-700">Diamond Pass</span>
                    </div>
                </div>
            </header>

            <div class="p-8 space-y-8 max-w-4xl mx-auto">
                <!-- 账户与安全 -->
                <section id="account" class="settings-section">
                    <h2 class="settings-section-title flex items-center gap-2">
                        <span class="material-icons-outlined text-sky-600">security</span>
                        账户与安全
                    </h2>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">修改密码</p>
                            <p class="settings-item-description">定期修改密码以保障账户安全。</p>
                        </div>
                        <a href="#" class="settings-item-action"><span class="material-icons-outlined">chevron_right</span></a>
                    </div>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">绑定邮箱</p>
                            <p class="settings-item-description">当前邮箱: <span class="font-medium text-sky-600"><EMAIL></span></p>
                        </div>
                        <a href="#" class="text-sm text-sky-600 hover:underline">更改</a>
                    </div>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">两步验证</p>
                            <p class="settings-item-description">增强账户安全性，保护您的AI伙伴数据。</p>
                        </div>
                        <button class="toggle-switch off" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item group">
                        <p class="settings-item-label">管理第三方账号</p>
                        <a href="#" class="settings-item-action"><span class="material-icons-outlined">chevron_right</span></a>
                    </div>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label text-red-600">注销账号</p>
                            <p class="settings-item-description">将永久删除所有数据，包括AI记忆胶囊。</p>
                        </div>
                        <a href="#" class="text-sm text-red-500 hover:text-red-600 hover:underline font-medium">申请注销</a>
                    </div>
                </section>

                <!-- AI交互设置 -->
                <section id="ai-interaction" class="settings-section">
                    <h2 class="settings-section-title flex items-center gap-2">
                        <span class="material-icons-outlined text-purple-600">psychology</span>
                        AI交互偏好
                    </h2>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">AI响应速度偏好</p>
                            <p class="settings-item-description">选择您偏好的AI回复方式。</p>
                        </div>
                        <select class="form-select-custom">
                            <option>快速响应 (简洁)</option>
                            <option selected>标准响应 (平衡)</option>
                            <option>深度响应 (详细)</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">AI情感表达强度</p>
                            <p class="settings-item-description">调整AI情感回应的强烈程度。</p>
                        </div>
                        <select class="form-select-custom">
                            <option>含蓄 (轻微)</option>
                            <option selected>适中 (自然)</option>
                            <option>丰富 (强烈)</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">记忆胶囊自动建议</p>
                            <p class="settings-item-description">AI主动提醒您保存重要对话片段。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">羁绊升级提醒</p>
                            <p class="settings-item-description">当与AI角色羁绊等级提升时显示特殊动画。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                </section>

                <!-- 通知偏好 -->
                <section id="notifications" class="settings-section">
                    <h2 class="settings-section-title flex items-center gap-2">
                        <span class="material-icons-outlined text-amber-600">notifications_active</span>
                        通知偏好
                    </h2>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">每日Streak提醒</p>
                            <p class="settings-item-description">提醒您保持连续互动记录。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">战令进度通知</p>
                            <p class="settings-item-description">战令等级提升和奖励解锁提醒。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">新角色上线通知</p>
                            <p class="settings-item-description">及时获取官方或热门新角色的上线提醒。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item">
                        <p class="settings-item-label">关注角色动态提醒</p>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item">
                        <p class="settings-item-label">活动与优惠通知</p>
                        <button class="toggle-switch off" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">免打扰时段</p>
                            <p class="settings-item-description">设置不希望收到通知的时间段。</p>
                        </div>
                        <a href="#" class="text-sm text-slate-500 hover:text-sky-600 flex items-center">未设置 <span class="material-icons-outlined text-lg ml-1">chevron_right</span></a>
                    </div>
                </section>

                <!-- 隐私与权限 -->
                <section id="privacy" class="settings-section">
                    <h2 class="settings-section-title flex items-center gap-2">
                        <span class="material-icons-outlined text-green-600">privacy_tip</span>
                        隐私与权限
                    </h2>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">个人主页信息可见性</p>
                            <p class="settings-item-description">控制谁可以看到您的个人主页信息。</p>
                        </div>
                        <select class="form-select-custom">
                            <option>对所有人可见</option>
                            <option selected>仅关注我的人可见</option>
                            <option>仅自己可见</option>
                        </select>
                    </div>
                    <div class="settings-item">
                         <div>
                            <p class="settings-item-label">"AI记忆胶囊"共享权限</p>
                            <p class="settings-item-description">是否允许AI基于您的记忆提供更个性化的体验。</p>
                        </div>
                         <select class="form-select-custom">
                            <option>不允许共享</option>
                            <option selected>允许匿名共享（不透露来源）</option>
                        </select>
                    </div>
                     <div class="settings-item">
                        <div>
                           <p class="settings-item-label">数字孪生互动权限</p>
                           <p class="settings-item-description">设定谁可以与您的数字孪生角色进行互动。</p>
                        </div>
                         <select class="form-select-custom">
                            <option>允许任何人</option>
                            <option selected>仅允许我关注的人</option>
                            <option>不允许任何人</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">角色卡创作署名</p>
                            <p class="settings-item-description">您创作的角色卡是否显示真实用户名。</p>
                        </div>
                        <button class="toggle-switch off" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                </section>

                <!-- 内容与显示 -->
                <section id="display" class="settings-section">
                    <h2 class="settings-section-title flex items-center gap-2">
                        <span class="material-icons-outlined text-indigo-600">palette</span>
                        内容与显示
                    </h2>
                    <div class="settings-item">
                        <p class="settings-item-label">界面语言</p>
                        <select class="form-select-custom">
                            <option selected>简体中文</option>
                            <option>English (US)</option>
                            <option>日本語</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <p class="settings-item-label">显示主题</p>
                        <select class="form-select-custom">
                            <option selected>自动 (跟随系统)</option>
                            <option>浅色模式</option>
                            <option>深色模式</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <p class="settings-item-label">聊天字体大小</p>
                        <select class="form-select-custom">
                            <option>小 (12px)</option>
                            <option selected>中 (14px) - 推荐</option>
                            <option>大 (16px)</option>
                            <option>特大 (18px)</option>
                        </select>
                    </div>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">自定义聊天背景</p>
                            <p class="settings-item-description">为不同角色设置专属聊天背景。</p>
                        </div>
                        <a href="#" class="settings-item-action"><span class="material-icons-outlined">chevron_right</span></a>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">动画效果</p>
                            <p class="settings-item-description">控制界面动画和过渡效果。</p>
                        </div>
                        <select class="form-select-custom">
                            <option>关闭</option>
                            <option>简化</option>
                            <option selected>标准</option>
                            <option>丰富</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">18+内容过滤</p>
                            <p class="settings-item-description">开启后将尝试过滤可能不适宜的内容。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                </section>

                <!-- 游戏化设置 -->
                <section id="gamification" class="settings-section">
                    <h2 class="settings-section-title flex items-center gap-2">
                        <span class="material-icons-outlined text-pink-600">videogame_asset</span>
                        游戏化体验
                    </h2>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">成就解锁动画</p>
                            <p class="settings-item-description">获得成就时显示庆祝动画和音效。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">代币获得提示</p>
                            <p class="settings-item-description">获得四种代币时的浮动提示。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">每日任务提醒强度</p>
                            <p class="settings-item-description">调整每日任务的提醒频率。</p>
                        </div>
                        <select class="form-select-custom">
                            <option>关闭</option>
                            <option>轻微</option>
                            <option selected>适中</option>
                            <option>积极</option>
                        </select>
                    </div>
                    <div class="feature-highlight">
                        <div class="flex items-center gap-3 mb-2">
                            <span class="material-icons-outlined text-2xl text-sky-600">auto_awesome</span>
                            <div>
                                <p class="font-semibold text-sky-800">记忆碎片画图偏好</p>
                                <p class="text-xs text-sky-600">设置AI生成画作的默认风格</p>
                            </div>
                        </div>
                        <select class="form-select-custom w-full">
                            <option>动漫风格</option>
                            <option selected>写实风格</option>
                            <option>水彩风格</option>
                            <option>油画风格</option>
                            <option>随机风格</option>
                        </select>
                    </div>
                </section>

                <!-- 数据管理 -->
                <section id="data" class="settings-section">
                    <h2 class="settings-section-title flex items-center gap-2">
                        <span class="material-icons-outlined text-slate-600">storage</span>
                        数据管理
                    </h2>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">清除应用缓存</p>
                            <p class="settings-item-description">清除临时文件，释放存储空间。</p>
                        </div>
                        <button class="text-sm text-sky-600 hover:underline font-medium">立即清除</button>
                    </div>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">导出个人数据</p>
                            <p class="settings-item-description">下载您的聊天记录、记忆胶囊等数据。</p>
                        </div>
                        <button class="text-sm text-sky-600 hover:underline font-medium">申请导出</button>
                    </div>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">数据使用统计</p>
                            <p class="settings-item-description">查看您的平台使用数据和统计信息。</p>
                        </div>
                        <a href="#" class="settings-item-action"><span class="material-icons-outlined">chevron_right</span></a>
                    </div>
                    <div class="feature-highlight">
                        <div class="flex items-center gap-3 mb-2">
                            <span class="material-icons-outlined text-2xl text-green-600">backup</span>
                            <div>
                                <p class="font-semibold text-green-800">AI记忆胶囊备份</p>
                                <p class="text-xs text-green-600">定期备份您珍贵的AI记忆数据</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-green-700">自动备份至云端</span>
                            <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                        </div>
                    </div>
                </section>

                <!-- 高级设置 (Diamond Pass用户专享) -->
                <section id="advanced" class="settings-section border-2 border-indigo-200">
                    <h2 class="settings-section-title flex items-center gap-2">
                        <span class="material-icons-outlined text-indigo-600">workspace_premium</span>
                        高级设置 (Diamond专享)
                    </h2>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">AI模型选择</p>
                            <p class="settings-item-description">选择更先进的AI模型获得更好体验。</p>
                        </div>
                        <select class="form-select-custom">
                            <option>标准模型</option>
                            <option selected>Gemini 2.5 Flash</option>
                            <option>Gemini 2.5 Pro (测试)</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <div>
                            <p class="settings-item-label">创作者工具套件</p>
                            <p class="settings-item-description">解锁高级角色卡编辑和分析工具。</p>
                        </div>
                        <button class="toggle-switch on" onclick="toggleSwitch(this)"><span class="toggle-switch-handle"></span></button>
                    </div>
                    <div class="settings-item group">
                        <div>
                            <p class="settings-item-label">尊享密语空间设置</p>
                            <p class="settings-item-description">管理您的专属高级社交权限。</p>
                        </div>
                        <a href="#" class="settings-item-action"><span class="material-icons-outlined">chevron_right</span></a>
                    </div>
                </section>
            </div>
        </main>
    </div>
</body>
</html>