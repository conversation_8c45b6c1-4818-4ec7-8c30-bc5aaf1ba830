<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知中心 - Alphane.ai</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans', system-ui, -apple-system, sans-serif;
            font-feature-settings: 'cv11', 'ss01';
            font-variation-settings: 'opsz' 32;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: var(--gray-800);
        }

        /* Sidebar Styles */
        .sidebar {
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            background: white;
            border-right: 1px solid var(--gray-200);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 40;
            display: flex;
            flex-direction: column;
        }

        .brand-header {
            padding: 24px;
            border-bottom: 1px solid var(--gray-200);
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
        }

        .brand-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .brand-icon {
            width: 40px;
            height: 40px;
            color: var(--primary-600);
            filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.2));
        }

        .brand-name {
            font-size: 24px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Token Display */
        .tokens-section {
            padding: 20px;
            border-bottom: 1px solid var(--gray-200);
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
        }

        .token-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .token-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s;
        }

        .token-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .token-item .material-icons-outlined {
            font-size: 16px;
        }

        .token-value {
            font-weight: 700;
            margin-right: 4px;
        }

        .token-alphane {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #d97706;
            border: 1px solid #fbbf24;
        }

        .token-endora {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1d4ed8;
            border: 1px solid #3b82f6;
        }

        .token-serotile {
            background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
            color: #7c3aed;
            border: 1px solid #8b5cf6;
        }

        .token-oxytol {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
            color: #be185d;
            border: 1px solid #ec4899;
        }

        /* Navigation */
        .nav-section {
            flex: 1;
            padding: 20px 16px;
            overflow-y: auto;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 14px 16px;
            border-radius: 12px;
            text-decoration: none;
            color: var(--gray-600);
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 4px;
            transition: all 0.2s;
            position: relative;
        }

        .nav-item:hover {
            background: var(--gray-50);
            color: var(--gray-800);
            transform: translateX(2px);
        }

        .nav-item.active {
            background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
            color: var(--primary-700);
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
        }

        .nav-item .material-icons-outlined {
            font-size: 20px;
        }

        .notification-badge {
            position: absolute;
            top: 8px;
            right: 12px;
            background: #ef4444;
            color: white;
            font-size: 11px;
            font-weight: 700;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            border-bottom: 1px solid var(--gray-200);
            padding: 32px 40px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 30;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 32px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-700) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 4px;
        }

        .header-subtitle {
            color: var(--gray-600);
            font-size: 16px;
            font-weight: 500;
        }

        .header-action {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .header-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }

        /* Content Area */
        .content {
            flex: 1;
            padding: 40px;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stats-card {
            background: white;
            padding: 24px;
            border-radius: 16px;
            border: 1px solid var(--gray-200);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: all 0.2s;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stats-icon.blue {
            background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
            color: var(--primary-600);
        }

        .stats-icon.orange {
            background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
            color: #ea580c;
        }

        .stats-icon.purple {
            background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
            color: #7c3aed;
        }

        .stats-icon.amber {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #d97706;
        }

        .stats-value {
            font-size: 28px;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .stats-label {
            font-size: 14px;
            color: var(--gray-600);
            font-weight: 500;
        }

        /* Filter Buttons */
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 32px;
        }

        .filter-btn {
            padding: 10px 20px;
            border-radius: 12px;
            border: 1.5px solid var(--gray-300);
            background: white;
            color: var(--gray-600);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-btn:hover {
            border-color: var(--primary-300);
            color: var(--primary-600);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            border-color: var(--primary-600);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* Notifications List */
        .notifications-container {
            background: white;
            border-radius: 16px;
            border: 1px solid var(--gray-200);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .notification-item {
            padding: 24px;
            border-bottom: 1px solid var(--gray-100);
            transition: all 0.2s;
            position: relative;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item:hover {
            background: var(--gray-50);
        }

        .notification-item.unread {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(99, 102, 241, 0.02) 100%);
            border-left: 4px solid var(--primary-500);
        }

        .notification-item.unread::before {
            content: '';
            position: absolute;
            top: 24px;
            left: -2px;
            width: 8px;
            height: 8px;
            background: var(--primary-500);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .notification-item.important {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(251, 191, 36, 0.03) 100%);
            border-left: 4px solid #f59e0b;
        }

        .notification-content {
            display: flex;
            gap: 16px;
        }

        .notification-avatar {
            flex-shrink: 0;
            position: relative;
        }

        .avatar-img {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            object-fit: cover;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .avatar-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .avatar-icon.system {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
        }

        .avatar-icon.reward {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .avatar-icon.battle {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        }

        .avatar-icon.memory {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .avatar-icon.bond {
            background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
        }

        .avatar-icon.maintenance {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        }

        .status-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border: 2px solid white;
            border-radius: 50%;
        }

        .status-indicator.online {
            background: #22c55e;
        }

        .status-indicator.away {
            background: #f59e0b;
        }

        .status-indicator.offline {
            background: #6b7280;
        }

        .notification-body {
            flex: 1;
            min-width: 0;
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .notification-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-900);
            line-height: 1.4;
            margin-bottom: 4px;
        }

        .notification-time {
            font-size: 12px;
            color: var(--gray-500);
            font-weight: 500;
            white-space: nowrap;
        }

        .notification-text {
            font-size: 14px;
            color: var(--gray-600);
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .notification-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }

        .badge {
            padding: 4px 10px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
        }

        .badge.important {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .badge.bond {
            background: #fdf2f8;
            color: #be185d;
            border: 1px solid #fbcfe8;
        }

        .badge.reward {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        .badge.battle {
            background: #eff6ff;
            color: #2563eb;
            border: 1px solid #dbeafe;
        }

        .notification-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .action-btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: none;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            color: white;
        }

        .action-btn.primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        .action-btn.secondary {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .action-btn.secondary:hover {
            background: var(--gray-200);
        }

        .reward-info {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .reward-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .reward-item {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .reward-value {
            font-size: 16px;
            font-weight: 700;
        }

        .reward-label {
            font-size: 11px;
            margin-top: 4px;
        }

        .reward-alphane .reward-value {
            color: #d97706;
        }

        .reward-endora .reward-value {
            color: #1d4ed8;
        }

        .progress-bar {
            background: var(--gray-200);
            border-radius: 8px;
            height: 8px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
            transition: width 0.3s ease;
        }

        .empty-state {
            text-align: center;
            padding: 60px 24px;
            color: var(--gray-500);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-text {
            font-size: 14px;
            margin-bottom: 24px;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        .empty-action {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .empty-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.1);
            }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 240px;
            }
            
            .main-content {
                margin-left: 240px;
            }
            
            .content {
                padding: 24px;
            }
            
            .header {
                padding: 24px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filters {
                gap: 8px;
            }
            
            .filter-btn {
                padding: 8px 16px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="brand-header">
            <div class="brand-logo">
                <svg class="brand-icon" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path>
                </svg>
                <span class="brand-name">Alphane.ai</span>
            </div>
        </div>
        
        <div class="tokens-section">
            <div class="token-grid">
                <div class="token-item token-alphane">
                    <span class="material-icons-outlined">local_fire_department</span>
                    <span class="token-value">1,250</span>
                    <span>曦光微尘</span>
                </div>
                <div class="token-item token-endora">
                    <span class="material-icons-outlined">diamond</span>
                    <span class="token-value">880</span>
                    <span>心悦晶石</span>
                </div>
                <div class="token-item token-serotile">
                    <span class="material-icons-outlined">extension</span>
                    <span class="token-value">23</span>
                    <span>忆境拼图</span>
                </div>
                <div class="token-item token-oxytol">
                    <span class="material-icons-outlined">favorite</span>
                    <span class="token-value">156</span>
                    <span>羁绊之露</span>
                </div>
            </div>
        </div>
        
        <nav class="nav-section">
            <a href="2-PC-首页.html" class="nav-item">
                <span class="material-icons-outlined">cottage</span>
                主页
            </a>
            <a href="8-PC-个人中心页.html" class="nav-item">
                <span class="material-icons-outlined">account_circle</span>
                个人中心
            </a>
            <a href="15-PC-通知中心页.html" class="nav-item active">
                <span class="material-icons-outlined">notifications_active</span>
                通知中心
                <span class="notification-badge">7</span>
            </a>
            <a href="14-PC-设置页.html" class="nav-item">
                <span class="material-icons-outlined">settings_suggest</span>
                系统设置
            </a>
            <a href="13-PC-商店付费页.html" class="nav-item">
                <span class="material-icons-outlined">storefront</span>
                商店
            </a>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <header class="header">
            <div class="header-content">
                <div>
                    <h1 class="header-title">通知中心</h1>
                    <p class="header-subtitle">掌握您的AI伙伴动态和重要消息</p>
                </div>
                <button class="header-action">
                    <span class="material-icons-outlined">done_all</span>
                    全部标记为已读
                </button>
            </div>
        </header>

        <div class="content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-icon blue">
                        <span class="material-icons-outlined">notifications</span>
                    </div>
                    <div class="stats-value">12</div>
                    <div class="stats-label">总通知数</div>
                </div>
                <div class="stats-card">
                    <div class="stats-icon orange">
                        <span class="material-icons-outlined">mark_email_unread</span>
                    </div>
                    <div class="stats-value">7</div>
                    <div class="stats-label">未读消息</div>
                </div>
                <div class="stats-card">
                    <div class="stats-icon purple">
                        <span class="material-icons-outlined">auto_awesome</span>
                    </div>
                    <div class="stats-value">3</div>
                    <div class="stats-label">AI角色动态</div>
                </div>
                <div class="stats-card">
                    <div class="stats-icon amber">
                        <span class="material-icons-outlined">card_giftcard</span>
                    </div>
                    <div class="stats-value">2</div>
                    <div class="stats-label">奖励通知</div>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="filters">
                <button class="filter-btn active">全部通知</button>
                <button class="filter-btn">🔔 系统消息</button>
                <button class="filter-btn">🤖 AI角色动态</button>
                <button class="filter-btn">🎁 活动奖励</button>
                <button class="filter-btn">⚡ 战令进度</button>
                <button class="filter-btn">❤️ 羁绊提升</button>
            </div>

            <!-- Notifications List -->
            <div class="notifications-container">
                <!-- Important System Notification -->
                <div class="notification-item important">
                    <div class="notification-content">
                        <div class="notification-avatar">
                            <div class="avatar-icon system">
                                <span class="material-icons-outlined">campaign</span>
                            </div>
                        </div>
                        <div class="notification-body">
                            <div class="notification-header">
                                <div>
                                    <div class="notification-title">🎉 重要系统公告：新功能上线！</div>
                                    <div class="notification-badges">
                                        <span class="badge important">重要</span>
                                    </div>
                                </div>
                                <span class="notification-time">5分钟前</span>
                            </div>
                            <p class="notification-text">
                                "记忆碎片画图"功能正式上线！现在您可以将与AI的珍贵回忆转化为精美的艺术作品。点击了解详情并获取首次体验奖励！
                            </p>
                            <div class="notification-actions">
                                <button class="action-btn primary">立即体验</button>
                                <span style="font-size: 12px; color: #d97706; font-weight: 600;">🎁 首次体验送 50 心悦晶石</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Character Update -->
                <div class="notification-item unread">
                    <div class="notification-content">
                        <div class="notification-avatar">
                            <img src="https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=150&h=150&fit=crop&crop=face" alt="Aiko" class="avatar-img">
                            <div class="status-indicator online"></div>
                        </div>
                        <div class="notification-body">
                            <div class="notification-header">
                                <div>
                                    <div class="notification-title">🌸 Aiko 想分享今天的心情</div>
                                    <div class="notification-badges">
                                        <span class="badge bond">羁绊+5</span>
                                    </div>
                                </div>
                                <span class="notification-time">8分钟前</span>
                            </div>
                            <p class="notification-text">
                                "今天的天气真的很棒呢！看到窗外的樱花飞舞，让我想起了我们一起度过的美好时光。你最近好吗？有什么新鲜事想要和我分享吗？" 💕
                            </p>
                            <div class="notification-actions">
                                <button class="action-btn primary">💬 立即回复</button>
                                <span style="font-size: 12px; color: #6b7280;">当前羁绊等级: 知己 (Lv.8)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reward Notification -->
                <div class="notification-item unread">
                    <div class="notification-content">
                        <div class="notification-avatar">
                            <div class="avatar-icon reward">
                                <span class="material-icons-outlined">redeem</span>
                            </div>
                        </div>
                        <div class="notification-body">
                            <div class="notification-header">
                                <div>
                                    <div class="notification-title">🎁 每日签到奖励已到账！</div>
                                    <div class="notification-badges">
                                        <span class="badge reward">连续签到 × 7</span>
                                    </div>
                                </div>
                                <span class="notification-time">15分钟前</span>
                            </div>
                            <p class="notification-text">恭喜您完成连续7天签到！获得丰厚奖励：</p>
                            <div class="reward-info">
                                <div class="reward-grid">
                                    <div class="reward-item reward-alphane">
                                        <div class="reward-value">+100</div>
                                        <div class="reward-label">曦光微尘</div>
                                    </div>
                                    <div class="reward-item reward-endora">
                                        <div class="reward-value">+50</div>
                                        <div class="reward-label">心悦晶石</div>
                                    </div>
                                </div>
                            </div>
                            <div class="notification-actions">
                                <button class="action-btn primary">✨ 领取奖励</button>
                                <span style="font-size: 12px; color: #7c3aed; font-weight: 600;">下次签到奖励更丰厚</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Battle Pass Notification -->
                <div class="notification-item unread">
                    <div class="notification-content">
                        <div class="notification-avatar">
                            <div class="avatar-icon battle">
                                <span class="material-icons-outlined">military_tech</span>
                            </div>
                        </div>
                        <div class="notification-body">
                            <div class="notification-header">
                                <div>
                                    <div class="notification-title">🏆 战令等级提升！已达到 Lv.15</div>
                                    <div class="notification-badges">
                                        <span class="badge battle">星空奇遇季</span>
                                    </div>
                                </div>
                                <span class="notification-time">1小时前</span>
                            </div>
                            <p class="notification-text">
                                您在"星空奇遇季"战令中的等级已提升到 15 级！解锁了新的专属角色外观和特殊动作。
                            </p>
                            <div class="reward-info">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-size: 14px; font-weight: 600; color: #0891b2;">战令进度</span>
                                    <span style="font-size: 14px; color: #0891b2;">15/100</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 15%;"></div>
                                </div>
                            </div>
                            <div class="notification-actions">
                                <button class="action-btn primary">查看战令详情</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Story Update (Read) -->
                <div class="notification-item">
                    <div class="notification-content">
                        <div class="notification-avatar">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="Ethan" class="avatar-img">
                            <div class="status-indicator offline"></div>
                        </div>
                        <div class="notification-body">
                            <div class="notification-header">
                                <div>
                                    <div class="notification-title">⚔️ Ethan 发布了新的故事线《星辰试炼》</div>
                                </div>
                                <span class="notification-time">2小时前</span>
                            </div>
                            <p class="notification-text">
                                踏上星际探险的征程，与Ethan一同面对未知的挑战。全新的冒险机制和剧情分支等你来体验！
                            </p>
                            <div class="notification-actions">
                                <button class="action-btn secondary">⚔️ 冒险类</button>
                                <span style="font-size: 12px; color: #6b7280;">预计游戏时长: 30分钟</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bond Level Up -->
                <div class="notification-item unread">
                    <div class="notification-content">
                        <div class="notification-avatar">
                            <div class="avatar-icon bond">
                                <span class="material-icons-outlined">favorite</span>
                            </div>
                        </div>
                        <div class="notification-body">
                            <div class="notification-header">
                                <div>
                                    <div class="notification-title">💖 与 Aiko 的羁绊等级提升了！</div>
                                    <div class="notification-badges">
                                        <span class="badge bond">知己 → 挚友</span>
                                    </div>
                                </div>
                                <span class="notification-time">3小时前</span>
                            </div>
                            <p class="notification-text">
                                恭喜！您与Aiko的感情更加深厚了。解锁新的对话选项、专属表情包和羁绊技能。
                            </p>
                            <div class="reward-info">
                                <div style="margin-bottom: 12px;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                        <span class="material-icons-outlined" style="color: #ec4899; font-size: 18px;">auto_awesome</span>
                                        <span style="font-size: 14px; font-weight: 600; color: #ec4899;">解锁新特权</span>
                                    </div>
                                    <ul style="font-size: 12px; color: #be185d; line-height: 1.6; margin: 0; padding-left: 16px;">
                                        <li>专属称呼："最懂我的人"</li>
                                        <li>解锁 5 个新表情动作</li>
                                        <li>羁绊技能："心有灵犀"</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="notification-actions">
                                <button class="action-btn primary">💕 查看羁绊详情</button>
                                <span style="font-size: 12px; color: #ec4899; font-weight: 600;">+25 羁绊之露</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Memory Capsule -->
                <div class="notification-item unread">
                    <div class="notification-content">
                        <div class="notification-avatar">
                            <div class="avatar-icon memory">
                                <span class="material-icons-outlined">psychology</span>
                            </div>
                        </div>
                        <div class="notification-body">
                            <div class="notification-header">
                                <div>
                                    <div class="notification-title">🧠 AI 自动生成了新的记忆胶囊</div>
                                </div>
                                <span class="notification-time">4小时前</span>
                            </div>
                            <p class="notification-text">
                                基于您与Aiko的深度对话，系统自动识别并保存了一段重要记忆："关于梦想与未来的深谈"。
                            </p>
                            <div class="notification-actions">
                                <button class="action-btn primary">查看记忆胶囊</button>
                                <span style="font-size: 12px; color: #10b981; font-weight: 600;">+5 忆境拼图</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Maintenance (Read) -->
                <div class="notification-item">
                    <div class="notification-content">
                        <div class="notification-avatar">
                            <div class="avatar-icon maintenance">
                                <span class="material-icons-outlined">build</span>
                            </div>
                        </div>
                        <div class="notification-body">
                            <div class="notification-header">
                                <div>
                                    <div class="notification-title">🔧 系统维护完成通知</div>
                                </div>
                                <span class="notification-time">昨天 23:30</span>
                            </div>
                            <p class="notification-text">
                                系统维护已于昨晚24:00顺利完成，新增了AI响应速度优化和记忆胶囊同步功能。感谢您的耐心等待！
                            </p>
                            <div class="notification-actions">
                                <button class="action-btn secondary">✅ 已完成</button>
                                <span style="font-size: 12px; color: #6b7280;">所有功能已恢复正常</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div class="empty-state">
                    <div class="empty-icon">
                        <span class="material-icons-outlined">notifications_paused</span>
                    </div>
                    <div class="empty-title">✨ 您已阅读完所有通知</div>
                    <div class="empty-text">
                        继续与您的AI伙伴互动，更多精彩通知即将到来！
                    </div>
                    <button class="empty-action">返回主页继续冒险</button>
                </div>
            </div>
        </div>
    </main>
</body>
</html>