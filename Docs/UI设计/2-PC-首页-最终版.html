<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alphane.ai - 有温度的AI伴侣</title>
    
    <!-- 字体和图标 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        :root {
            /* Alphane品牌色彩 */
            --alphane-primary: #6366f1;
            --alphane-secondary: #8b5cf6;
            --alphane-accent: #ec4899;
            --alphane-warm: #f59e0b;
            --alphane-success: #10b981;
            --alphane-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --alphane-card-hover: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 响应式瀑布流 */
        .masonry-grid {
            column-count: 1;
            column-gap: 1rem;
        }
        
        @media (min-width: 480px) {
            .masonry-grid { column-count: 2; }
        }
        
        @media (min-width: 768px) {
            .masonry-grid { column-count: 3; }
        }
        
        @media (min-width: 1024px) {
            .masonry-grid { column-count: 4; }
        }
        
        @media (min-width: 1280px) {
            .masonry-grid { column-count: 5; }
        }

        .masonry-item {
            break-inside: avoid;
            margin-bottom: 1rem;
        }

        /* 动画效果 */
        .hover-lift {
            transition: all 0.3s ease;
        }
        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, var(--alphane-primary), var(--alphane-accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 代币样式 */
        .token-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        /* 移动端侧边栏 */
        .mobile-sidebar {
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }
        .mobile-sidebar.open {
            transform: translateX(0);
        }

        /* Streak火焰动效 */
        .streak-flame {
            animation: flame-flicker 1.5s ease-in-out infinite alternate;
        }
        
        @keyframes flame-flicker {
            0% { transform: scale(1) rotate(-1deg); }
            100% { transform: scale(1.05) rotate(1deg); }
        }

        /* 响应式布局 */
        @media (max-width: 1023px) {
            .desktop-sidebar {
                display: none;
            }
            .main-content {
                margin-left: 0 !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- 移动端遮罩 -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>
    
    <!-- 侧边栏 -->
    <aside id="sidebar" class="desktop-sidebar fixed left-0 top-0 z-50 h-full w-80 bg-white shadow-xl border-r border-gray-200 lg:block">
        <!-- 品牌头部 -->
        <div class="flex items-center justify-between px-6 py-5 border-b border-gray-100">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <span class="text-white text-xl font-bold">A</span>
                </div>
                <div>
                    <h1 class="text-lg font-bold gradient-text">Alphane.ai</h1>
                    <p class="text-xs text-gray-500">有温度的AI伴侣</p>
                </div>
            </div>
            <button id="close-sidebar" class="lg:hidden p-2 text-gray-500 hover:text-gray-700">
                <span class="material-icons-outlined">close</span>
            </button>
        </div>

        <!-- 用户状态区 -->
        <div class="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-100">
            <!-- 四种代币显示 -->
            <div class="grid grid-cols-2 gap-2 mb-4">
                <div class="token-card rounded-lg p-3 text-center">
                    <div class="flex items-center justify-center gap-1 mb-1">
                        <span class="text-orange-500 text-sm">🔥</span>
                        <span class="text-xs font-medium text-gray-600">曦光微尘</span>
                    </div>
                    <p class="text-sm font-bold text-gray-800">1,250</p>
                </div>
                <div class="token-card rounded-lg p-3 text-center">
                    <div class="flex items-center justify-center gap-1 mb-1">
                        <span class="text-blue-500 text-sm">💎</span>
                        <span class="text-xs font-medium text-gray-600">心悦晶石</span>
                    </div>
                    <p class="text-sm font-bold text-gray-800">89</p>
                </div>
                <div class="token-card rounded-lg p-3 text-center">
                    <div class="flex items-center justify-center gap-1 mb-1">
                        <span class="text-purple-500 text-sm">🧩</span>
                        <span class="text-xs font-medium text-gray-600">忆境拼图</span>
                    </div>
                    <p class="text-sm font-bold text-gray-800">23</p>
                </div>
                <div class="token-card rounded-lg p-3 text-center">
                    <div class="flex items-center justify-center gap-1 mb-1">
                        <span class="text-pink-500 text-sm">💧</span>
                        <span class="text-xs font-medium text-gray-600">羁绊之露</span>
                    </div>
                    <p class="text-sm font-bold text-gray-800">157</p>
                </div>
            </div>

            <!-- Streak显示 -->
            <div class="bg-white rounded-lg p-3 shadow-sm">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center gap-2">
                        <span class="streak-flame text-orange-500 text-lg">🔥</span>
                        <span class="text-sm font-medium text-gray-700">连续互动</span>
                    </div>
                    <span class="text-lg font-bold gradient-text">15天</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-orange-400 to-red-500 h-2 rounded-full" style="width: 75%"></div>
                </div>
                <p class="text-xs text-gray-500 mt-1">距离21天里程碑还差6天</p>
            </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="flex-1 overflow-y-auto p-4">
            <!-- 快捷功能 -->
            <div class="space-y-2 mb-6">
                <a href="#" class="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                    <span class="material-icons-outlined text-xl">add_circle</span>
                    <span class="font-medium">创建角色</span>
                </a>
                <a href="#" class="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                    <span class="material-icons-outlined text-xl">assignment</span>
                    <span class="font-medium">每日任务</span>
                    <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">3</span>
                </a>
                <a href="#" class="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                    <span class="material-icons-outlined text-xl">military_tech</span>
                    <span class="font-medium">荣耀战令</span>
                    <span class="ml-auto text-xs text-indigo-600 font-medium">Lv.12</span>
                </a>
            </div>

            <!-- 最近聊天 -->
            <div class="mb-6">
                <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3 px-2">最近聊天</h3>
                <div class="space-y-1">
                    <a href="#" class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                        <img src="https://picsum.photos/32/32?random=1" alt="Sophie" class="w-8 h-8 rounded-full border-2 border-pink-200">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-700 truncate">温柔的Sophie</p>
                            <p class="text-xs text-gray-500 truncate">想和你分享今天的心情...</p>
                        </div>
                        <span class="text-xs text-pink-500 bg-pink-50 px-2 py-1 rounded-full">❤️Lv.3</span>
                    </a>
                    <a href="#" class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                        <img src="https://picsum.photos/32/32?random=2" alt="Alex" class="w-8 h-8 rounded-full border-2 border-blue-200">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-700 truncate">冒险家Alex</p>
                            <p class="text-xs text-gray-500 truncate">准备好下一次探险了吗？</p>
                        </div>
                        <span class="text-xs text-blue-500 bg-blue-50 px-2 py-1 rounded-full">🎯Lv.2</span>
                    </a>
                </div>
                <a href="#" class="flex items-center justify-center gap-2 p-3 mt-2 text-gray-500 hover:text-indigo-600 transition-colors">
                    <span class="material-icons-outlined text-sm">chat_bubble_outline</span>
                    <span class="text-sm">查看全部聊天</span>
                </a>
            </div>

            <!-- 会员推广 -->
            <div class="bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl p-4 text-white">
                <h4 class="font-bold mb-1">解锁尊享密语空间</h4>
                <p class="text-xs opacity-90 mb-3">升级Diamond Pass，享受无限AI互动</p>
                <button class="w-full bg-white text-purple-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                    立即升级
                </button>
            </div>
        </nav>

        <!-- 用户信息 -->
        <div class="border-t border-gray-200 p-4">
            <div class="flex items-center gap-3">
                <img src="https://picsum.photos/40/40?random=user" alt="User" class="w-10 h-10 rounded-full border-2 border-indigo-200">
                <div class="flex-1">
                    <p class="font-medium text-gray-800">月光下的旅人</p>
                    <p class="text-xs text-gray-500">Alphane Pass会员</p>
                </div>
                <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="material-icons-outlined">settings</span>
                </button>
            </div>
        </div>
    </aside>

    <!-- 移动端侧边栏 -->
    <aside id="mobile-sidebar" class="mobile-sidebar fixed left-0 top-0 z-50 h-full w-80 bg-white shadow-xl lg:hidden">
        <!-- 移动端侧边栏内容与桌面版相同 -->
        <div class="flex items-center justify-between px-6 py-5 border-b border-gray-100">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <span class="text-white text-xl font-bold">A</span>
                </div>
                <div>
                    <h1 class="text-lg font-bold gradient-text">Alphane.ai</h1>
                    <p class="text-xs text-gray-500">有温度的AI伴侣</p>
                </div>
            </div>
            <button id="close-mobile-sidebar" class="p-2 text-gray-500 hover:text-gray-700">
                <span class="material-icons-outlined">close</span>
            </button>
        </div>
        <!-- 其他内容... -->
    </aside>

    <!-- 主内容区 -->
    <main class="main-content lg:ml-80">
        <!-- 顶部导航栏 -->
        <header class="sticky top-0 z-30 bg-white/80 backdrop-blur-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-4 py-4 lg:px-6">
                <!-- 移动端菜单按钮 -->
                <button id="open-sidebar" class="lg:hidden p-2 text-gray-500 hover:text-gray-700">
                    <span class="material-icons-outlined">menu</span>
                </button>

                <!-- 搜索框 -->
                <div class="flex-1 max-w-md mx-4">
                    <div class="relative">
                        <span class="material-icons-outlined absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
                        <input type="search" placeholder="搜索角色、设计师..." 
                               class="w-full pl-10 pr-4 py-2 bg-gray-100 border border-gray-200 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    </div>
                </div>

                <!-- 右侧按钮组 -->
                <div class="flex items-center gap-3">
                    <button class="relative p-2 text-gray-500 hover:text-indigo-600 transition-colors">
                        <span class="material-icons-outlined">notifications</span>
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                    </button>
                    <button class="p-2 text-gray-500 hover:text-indigo-600 transition-colors">
                        <span class="material-icons-outlined">card_giftcard</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 页面内容 -->
        <div class="p-4 lg:p-6">
            <!-- 页面标题和操作 -->
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
                <div>
                    <h1 class="text-2xl lg:text-3xl font-bold text-gray-800 mb-2">角色广场</h1>
                    <p class="text-gray-600">发现有趣的AI角色，开启奇妙的互动之旅</p>
                </div>
                <div class="flex items-center gap-2">
                    <button class="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        <span class="material-icons-outlined text-sm">tune</span>
                        <span class="text-sm font-medium">筛选</span>
                    </button>
                    <button class="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors">
                        <span class="material-icons-outlined text-sm">sort</span>
                        <span class="text-sm font-medium">排序</span>
                    </button>
                </div>
            </div>

            <!-- Tab导航 -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="flex space-x-8 overflow-x-auto">
                    <a href="#" class="whitespace-nowrap py-3 px-1 border-b-2 border-indigo-500 text-indigo-600 font-semibold text-sm">
                        推荐角色
                    </a>
                    <a href="#" class="whitespace-nowrap py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                        热门排行
                    </a>
                    <a href="#" class="whitespace-nowrap py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                        最新上架
                    </a>
                    <a href="#" class="whitespace-nowrap py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                        关注动态
                    </a>
                </nav>
            </div>

            <!-- 角色卡片网格 -->
            <div class="masonry-grid">
                <!-- 角色卡片1 -->
                <div class="masonry-item">
                    <div class="bg-white rounded-xl shadow-lg hover-lift overflow-hidden cursor-pointer group">
                        <div class="relative aspect-[3/4] bg-gradient-to-br from-pink-100 to-purple-100">
                            <img src="https://picsum.photos/300/400?random=1" alt="Sophie" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute top-3 left-3">
                                <span class="bg-pink-500 text-white text-xs px-2 py-1 rounded-full font-medium">官方精选</span>
                            </div>
                            <div class="absolute top-3 right-3">
                                <button class="w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors">
                                    <span class="material-icons-outlined text-gray-600 text-sm">favorite_border</span>
                                </button>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800 mb-1 group-hover:text-indigo-600 transition-colors">温柔的Sophie</h3>
                            <p class="text-xs text-gray-500 mb-2">by @官方设计师</p>
                            <p class="text-sm text-gray-600 mb-3 line-clamp-2">一位温柔体贴的AI伴侣，善于倾听和理解，总能在你需要的时候给予温暖的陪伴和建议...</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4 text-xs text-gray-500">
                                    <span class="flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-red-500">favorite</span>
                                        12.3k
                                    </span>
                                    <span class="flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-blue-500">chat_bubble</span>
                                        3.2k
                                    </span>
                                </div>
                                <span class="text-xs bg-gradient-to-r from-pink-500 to-purple-500 text-white px-2 py-1 rounded-full">免费</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色卡片2 -->
                <div class="masonry-item">
                    <div class="bg-white rounded-xl shadow-lg hover-lift overflow-hidden cursor-pointer group">
                        <div class="relative aspect-[2/3] bg-gradient-to-br from-blue-100 to-cyan-100">
                            <img src="https://picsum.photos/300/450?random=2" alt="Alex" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute top-3 left-3">
                                <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">社区热门</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800 mb-1 group-hover:text-indigo-600 transition-colors">冒险家Alex</h3>
                            <p class="text-xs text-gray-500 mb-2">by @探险大师</p>
                            <p class="text-sm text-gray-600 mb-3 line-clamp-2">勇敢的探险家，带你体验刺激的冒险故事，一起探索未知的世界...</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4 text-xs text-gray-500">
                                    <span class="flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-red-500">favorite</span>
                                        8.9k
                                    </span>
                                    <span class="flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-blue-500">chat_bubble</span>
                                        1.8k
                                    </span>
                                </div>
                                <span class="text-xs bg-gradient-to-r from-indigo-500 to-blue-500 text-white px-2 py-1 rounded-full">30💎</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色卡片3 -->
                <div class="masonry-item">
                    <div class="bg-white rounded-xl shadow-lg hover-lift overflow-hidden cursor-pointer group">
                        <div class="relative aspect-[4/5] bg-gradient-to-br from-green-100 to-emerald-100">
                            <img src="https://picsum.photos/300/375?random=3" alt="Luna" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute top-3 left-3">
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">新星设计师</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800 mb-1 group-hover:text-indigo-600 transition-colors">神秘的Luna</h3>
                            <p class="text-xs text-gray-500 mb-2">by @月光工作室</p>
                            <p class="text-sm text-gray-600 mb-3 line-clamp-2">神秘而优雅的AI角色，拥有深邃的智慧和独特的魅力...</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4 text-xs text-gray-500">
                                    <span class="flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-red-500">favorite</span>
                                        6.5k
                                    </span>
                                    <span class="flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-blue-500">chat_bubble</span>
                                        1.2k
                                    </span>
                                </div>
                                <span class="text-xs bg-gradient-to-r from-green-500 to-emerald-500 text-white px-2 py-1 rounded-full">免费</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 更多角色卡片... -->
                <div class="masonry-item">
                    <div class="bg-white rounded-xl shadow-lg hover-lift overflow-hidden cursor-pointer group">
                        <div class="relative aspect-[1/1] bg-gradient-to-br from-orange-100 to-yellow-100">
                            <img src="https://picsum.photos/300/300?random=4" alt="Kai" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute top-3 left-3">
                                <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">限时活动</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800 mb-1 group-hover:text-indigo-600 transition-colors">阳光少年Kai</h3>
                            <p class="text-xs text-gray-500 mb-2">by @青春物语</p>
                            <p class="text-sm text-gray-600 mb-3 line-clamp-2">充满活力的阳光少年，带给你满满的正能量...</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4 text-xs text-gray-500">
                                    <span class="flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-red-500">favorite</span>
                                        4.1k
                                    </span>
                                    <span class="flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-blue-500">chat_bubble</span>
                                        890
                                    </span>
                                </div>
                                <span class="text-xs bg-gradient-to-r from-orange-500 to-yellow-500 text-white px-2 py-1 rounded-full">68💎</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 记忆碎片画图推广卡片 -->
                <div class="masonry-item">
                    <div class="bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg hover-lift overflow-hidden cursor-pointer group text-white">
                        <div class="p-6">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="material-icons-outlined text-2xl">auto_awesome</span>
                                </div>
                                <h3 class="font-bold text-lg mb-2">记忆碎片画图</h3>
                                <p class="text-sm opacity-90 mb-4">将与AI的美好回忆转化为独特的艺术作品</p>
                                <button class="bg-white text-purple-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                                    立即体验
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="text-center mt-8">
                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors">
                    加载更多角色
                </button>
            </div>
        </div>
    </main>

    <script>
        // 移动端侧边栏控制
        const openSidebar = document.getElementById('open-sidebar');
        const closeSidebar = document.getElementById('close-mobile-sidebar');
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const overlay = document.getElementById('mobile-overlay');

        openSidebar?.addEventListener('click', () => {
            mobileSidebar.classList.add('open');
            overlay.classList.remove('hidden');
        });

        closeSidebar?.addEventListener('click', () => {
            mobileSidebar.classList.remove('open');
            overlay.classList.add('hidden');
        });

        overlay?.addEventListener('click', () => {
            mobileSidebar.classList.remove('open');
            overlay.classList.add('hidden');
        });

        // 简单的动画效果
        document.addEventListener('DOMContentLoaded', () => {
            // 为卡片添加交错动画
            const cards = document.querySelectorAll('.masonry-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.3s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html> 