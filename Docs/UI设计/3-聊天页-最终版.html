<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>与Sophie的奇妙冒险 - Alphane.ai</title>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --accent: #ec4899;
            --warm: #f59e0b;
            --success: #10b981;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            overflow: hidden;
        }

        /* 温暖背景效果 */
        .chat-bg-animated {
            background: linear-gradient(-45deg, 
                rgba(255, 247, 255, 0.95),
                rgba(249, 250, 251, 0.98), 
                rgba(254, 242, 242, 0.95),
                rgba(255, 255, 255, 0.98)
            );
            background-size: 400% 400%;
            animation: gradientShift 25s ease infinite;
            position: relative;
        }

        .chat-bg-animated::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.08) 0%, transparent 50%);
            animation: bgShift 30s ease infinite;
        }

        @keyframes bgShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 浮动粒子效果 */
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
            z-index: 1;
        }
        
        .particle {
            position: absolute;
            border-radius: 50%;
            animation: float 10s infinite linear;
            filter: blur(1px);
        }
        
        @keyframes float {
            0% { 
                transform: translateY(100vh) rotate(0deg) scale(0); 
                opacity: 0; 
            }
            10% { 
                opacity: 0.8; 
                transform: translateY(90vh) rotate(36deg) scale(1);
            }
            90% { 
                opacity: 0.8; 
                transform: translateY(10vh) rotate(324deg) scale(1);
            }
            100% { 
                transform: translateY(-10vh) rotate(360deg) scale(0); 
                opacity: 0; 
            }
        }

        /* 毛玻璃效果增强 */
        .glass {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(20px) saturate(1.8);
            border: 1px solid rgba(255, 255, 255, 0.5);
            box-shadow: 
                0 8px 32px rgba(31, 38, 135, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(25px) saturate(1.8);
            border: 1px solid rgba(255, 255, 255, 0.6);
            box-shadow: 
                0 16px 64px rgba(31, 38, 135, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.4),
                0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* AI头像表情动画增强 */
        .ai-avatar {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
        }
        
        .ai-avatar::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ffecd2, #fcb69f, #ffecd2);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .ai-avatar.happy::before {
            opacity: 0.6;
            animation: avatarGlow 2s ease-in-out infinite alternate;
        }
        
        .ai-avatar.thinking::before {
            opacity: 0.4;
            background: linear-gradient(45deg, #a8edea, #fed6e3, #a8edea);
        }
        
        @keyframes avatarGlow {
            from { transform: scale(1); opacity: 0.6; }
            to { transform: scale(1.1); opacity: 0.3; }
        }
        
        .ai-avatar.happy {
            animation: bounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
        
        .ai-avatar.thinking {
            animation: pulse 2.5s ease-in-out infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-12px); }
            80% { transform: translateY(-6px); }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.08); }
        }

        /* 聊天气泡美化 */
        .chat-bubble {
            animation: bubbleIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: visible;
            box-shadow: 
                0 10px 40px rgba(31, 38, 135, 0.1),
                0 2px 8px rgba(31, 38, 135, 0.05);
        }
        
        @keyframes bubbleIn {
            from { 
                opacity: 0; 
                transform: translateY(30px) scale(0.8); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }

        /* AI情感气泡美化 */
        .emotion-happy {
            background: linear-gradient(135deg, 
                rgba(255, 248, 225, 0.95), 
                rgba(255, 237, 160, 0.9),
                rgba(254, 215, 102, 0.85)
            );
            border-left: 4px solid #f59e0b;
            box-shadow: 
                0 12px 40px rgba(245, 158, 11, 0.25),
                0 4px 16px rgba(245, 158, 11, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            position: relative;
        }
        
        .emotion-happy::after {
            content: '✨';
            position: absolute;
            top: -8px;
            right: 12px;
            font-size: 16px;
            animation: sparkle 2.5s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { 
                transform: scale(1) rotate(0deg); 
                opacity: 0.8; 
            }
            50% { 
                transform: scale(1.3) rotate(180deg); 
                opacity: 1; 
            }
        }
        
        .emotion-excited {
            background: linear-gradient(135deg, 
                rgba(254, 226, 226, 0.95), 
                rgba(252, 165, 165, 0.9),
                rgba(248, 113, 113, 0.85)
            );
            border-left: 4px solid #ef4444;
            box-shadow: 
                0 12px 40px rgba(239, 68, 68, 0.25),
                0 4px 16px rgba(239, 68, 68, 0.1);
            animation: excitement 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
        
        @keyframes excitement {
            0%, 100% { transform: scale(1) rotate(0deg); }
            25%, 75% { transform: scale(1.03) rotate(1deg); }
            50% { transform: scale(1.05) rotate(-1deg); }
        }
        
        .emotion-thinking {
            background: linear-gradient(135deg, 
                rgba(238, 242, 255, 0.95), 
                rgba(199, 210, 254, 0.9),
                rgba(165, 180, 252, 0.85)
            );
            border-left: 4px solid #6366f1;
            box-shadow: 
                0 12px 40px rgba(99, 102, 241, 0.25),
                0 4px 16px rgba(99, 102, 241, 0.1);
        }

        /* 打字动画美化 */
        .typing-animation {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px;
        }
        
        .typing-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            animation: typing 1.6s infinite;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing {
            0%, 60%, 100% { 
                transform: translateY(0) scale(1); 
                opacity: 0.4; 
                box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
            }
            30% { 
                transform: translateY(-12px) scale(1.2); 
                opacity: 1;
                box-shadow: 0 8px 16px rgba(99, 102, 241, 0.5);
            }
        }

        /* 记忆胶囊美化 */
        .memory-capsule {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.9), 
                rgba(248, 250, 252, 0.85),
                rgba(241, 245, 249, 0.8)
            );
            border: 1px solid rgba(139, 92, 246, 0.25);
            box-shadow: 
                0 8px 32px rgba(139, 92, 246, 0.15),
                0 2px 8px rgba(139, 92, 246, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .memory-capsule:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 
                0 16px 48px rgba(139, 92, 246, 0.25),
                0 8px 16px rgba(139, 92, 246, 0.1);
        }

        .memory-capsule::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(139, 92, 246, 0.15), 
                transparent
            );
            transition: left 0.8s ease;
        }

        .memory-capsule:hover::before {
            left: 100%;
        }

        /* 经验条美化 */
        .exp-bar {
            animation: expGain 2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: linear-gradient(90deg, 
                #ec4899, 
                #f97316, 
                #eab308
            );
            box-shadow: 
                0 4px 16px rgba(236, 72, 153, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
        }
        
        .exp-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(255, 255, 255, 0.3) 50%, 
                transparent 100%
            );
            animation: expShine 2s ease-in-out infinite;
        }
        
        @keyframes expShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes expGain {
            from { width: 0; }
            to { width: var(--exp-width); }
        }

        /* 代币奖励动画 */
        .token-reward {
            animation: tokenFloat 1.2s ease-out;
            pointer-events: none;
        }
        
        @keyframes tokenFloat {
            0% { opacity: 0; transform: translateY(0) scale(0.5); }
            30% { opacity: 1; transform: translateY(-20px) scale(1); }
            100% { opacity: 0; transform: translateY(-60px) scale(0.8); }
        }

        /* 特殊消息美化 */
        .special-message {
            background: linear-gradient(135deg, 
                #667eea 0%, 
                #764ba2 25%, 
                #f093fb 50%, 
                #f5576c 75%, 
                #4facfe 100%
            );
            background-size: 200% 200%;
            animation: gradientAnimation 4s ease infinite;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 16px 48px rgba(102, 126, 234, 0.4),
                0 8px 24px rgba(102, 126, 234, 0.2);
        }
        
        @keyframes gradientAnimation {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .special-message::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, 
                transparent, 
                rgba(255,255,255,0.4), 
                transparent
            );
            animation: shimmer 3s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .game-sidebar {
                position: fixed;
                top: 0;
                right: -100%;
                transition: right 0.3s ease;
                z-index: 50;
                height: 100vh;
            }
            .game-sidebar.open {
                right: 0;
            }
        }

        /* 卡片悬浮效果美化 */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-origin: center;
        }

        .card-hover:hover {
            transform: translateY(-6px) scale(1.03);
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 8px 24px rgba(0, 0, 0, 0.08);
        }

        /* 新的CSS类 */
        .input-field {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 
                inset 0 2px 8px rgba(0, 0, 0, 0.05),
                0 4px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .input-field:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(99, 102, 241, 0.5);
            box-shadow: 
                inset 0 2px 8px rgba(0, 0, 0, 0.05),
                0 8px 24px rgba(99, 102, 241, 0.15),
                0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 
                0 8px 24px rgba(102, 126, 234, 0.3),
                0 4px 12px rgba(102, 126, 234, 0.2);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 
                0 12px 32px rgba(102, 126, 234, 0.4),
                0 6px 16px rgba(102, 126, 234, 0.25);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.3), 
                transparent
            );
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* 快捷按钮美化 */
        .quick-btn {
            backdrop-filter: blur(10px);
            box-shadow: 
                0 4px 16px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .quick-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 
                0 8px 24px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }
    </style>
</head>
<body class="h-screen flex chat-bg-animated">
    <!-- 动态背景粒子 -->
    <div class="floating-particles fixed inset-0 pointer-events-none" id="particles"></div>

    <!-- 主聊天区域 -->
    <div class="flex-1 flex flex-col relative z-10">
        <!-- 顶部状态栏 -->
        <header class="glass-strong p-4 shadow-lg">
            <div class="flex items-center justify-between">
                <!-- 角色信息 -->
                <div class="flex items-center gap-4">
                    <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-full transition-colors">
                        <span class="material-icons-outlined text-slate-700">arrow_back</span>
                    </button>
                    
                    <div class="relative">
                        <div class="ai-avatar happy">
                            <img src="https://picsum.photos/50/50?random=1" alt="Sophie" 
                                 class="w-12 h-12 rounded-full border-3 border-pink-300 shadow-lg">
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                        </div>
                    </div>
                    
                    <div>
                        <h1 class="text-lg font-bold text-slate-800 flex items-center gap-2">
                            温柔的Sophie
                            <span class="text-xs bg-gradient-to-r from-pink-500 to-rose-500 text-white px-2 py-1 rounded-full">❤️知己</span>
                        </h1>
                        <div id="aiStatus" class="text-sm text-slate-600">在花园里漫步</div>
                    </div>
                </div>

                <!-- 羁绊进度和代币 -->
                <div class="hidden md:flex items-center gap-4">
                    <!-- 代币显示 -->
                    <div class="flex items-center gap-2">
                        <div class="glass flex items-center gap-1 px-3 py-1 rounded-full">
                            <span class="text-orange-500 text-sm">🔥</span>
                            <span class="text-xs font-bold text-slate-700">1,247</span>
                        </div>
                        <div class="glass flex items-center gap-1 px-3 py-1 rounded-full">
                            <span class="text-blue-500 text-sm">💎</span>
                            <span class="text-xs font-bold text-slate-700">89</span>
                        </div>
                    </div>
                    
                    <!-- 羁绊进度 -->
                    <div class="text-right">
                        <p class="text-xs text-slate-600">羁绊经验</p>
                        <div class="w-32 h-2 bg-white/30 rounded-full overflow-hidden">
                            <div class="h-full exp-bar rounded-full" 
                                 style="--exp-width: 75%; width: 75%"></div>
                        </div>
                        <p class="text-xs text-slate-500 mt-1">2,850 / 3,000</p>
                    </div>
                    
                    <!-- 场景切换 -->
                    <div class="flex gap-1">
                        <button onclick="changeScene('garden')" class="p-2 hover:bg-white/20 rounded text-sm transition-colors" title="花园">🌸</button>
                        <button onclick="changeScene('cafe')" class="p-2 hover:bg-white/20 rounded text-sm transition-colors" title="咖啡厅">☕</button>
                        <button onclick="changeScene('library')" class="p-2 hover:bg-white/20 rounded text-sm transition-colors" title="图书馆">📚</button>
                    </div>
                    
                    <button id="toggle-sidebar" class="md:hidden p-2 hover:bg-white/20 rounded-full">
                        <span class="material-icons-outlined">info</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 聊天消息区域 -->
        <main class="flex-1 overflow-y-auto p-4 space-y-6">
            <!-- AI消息 - 开心 -->
            <div class="flex items-start gap-3 max-w-4xl">
                <div class="ai-avatar happy">
                    <img src="https://picsum.photos/40/40?random=1" alt="Sophie" 
                         class="w-10 h-10 rounded-full border-2 border-pink-200 flex-shrink-0">
                </div>
                <div class="flex-1">
                    <div class="chat-bubble emotion-happy p-4 rounded-2xl rounded-bl-sm max-w-lg">
                        <p class="text-gray-800">哇！今天能和你聊天真是太开心了！🌟 你想聊什么呢？</p>
                        
                        <div class="flex items-center gap-2 mt-2 text-xs">
                            <span class="text-amber-600 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm">sentiment_very_satisfied</span>
                                开心 +3
                            </span>
                            <span class="text-pink-500">+5💧</span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 ml-4">Sophie · 刚刚</p>
                </div>
                
                <!-- 互动按钮 -->
                <div class="flex flex-col gap-1">
                    <button class="p-1 text-gray-400 hover:text-green-500 transition-colors" title="赞">
                        <span class="material-icons-outlined text-sm">thumb_up</span>
                    </button>
                    <button class="p-1 text-gray-400 hover:text-purple-500 transition-colors" title="存入记忆胶囊">
                        <span class="material-icons-outlined text-sm">psychology</span>
                    </button>
                </div>
            </div>

            <!-- 用户消息 -->
            <div class="flex items-start gap-3 max-w-4xl ml-auto flex-row-reverse">
                <img src="https://picsum.photos/40/40?random=2" alt="User" 
                     class="w-10 h-10 rounded-full border-2 border-indigo-200 flex-shrink-0">
                <div class="flex-1">
                    <div class="chat-bubble bg-gradient-to-r from-indigo-500 to-purple-500 text-white p-4 rounded-2xl rounded-br-sm max-w-lg ml-auto">
                        <p>我想听你讲一个有趣的故事！</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 mr-4 text-right">你 · 1分钟前</p>
                </div>
            </div>

            <!-- AI正在输入 -->
            <div class="flex items-start gap-3 max-w-4xl">
                <div class="ai-avatar thinking">
                    <img src="https://picsum.photos/40/40?random=1" alt="Sophie" 
                         class="w-10 h-10 rounded-full border-2 border-pink-200 flex-shrink-0">
                </div>
                <div class="flex-1">
                    <div class="chat-bubble glass p-4 rounded-2xl rounded-bl-sm max-w-lg">
                        <div class="typing-animation">
                            <span class="text-gray-600 text-sm mr-2">Sophie正在思考</span>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI消息 - 基于记忆的回复 -->
            <div class="flex items-start gap-3 max-w-4xl">
                <div class="ai-avatar">
                    <img src="https://picsum.photos/40/40?random=1" alt="Sophie" 
                         class="w-10 h-10 rounded-full border-2 border-pink-200 flex-shrink-0">
                </div>
                <div class="flex-1">
                    <div class="chat-bubble emotion-thinking p-4 rounded-2xl rounded-bl-sm max-w-lg">
                        <p class="text-gray-800">
                            让我想想... 🤔 记得你之前说过喜欢奇幻故事对吧？
                            <span class="inline-flex items-center ml-1 px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                                <span class="material-icons-outlined text-xs mr-1">lightbulb</span>
                                记忆调用
                            </span>
                        </p>
                        <p class="text-gray-800 mt-2">
                            那我来讲一个魔法师和他的小猫咪的故事吧！从前有一只会说话的小猫，它总是能找到失落的魔法物品...
                        </p>
                        
                        <!-- 互动选项 -->
                        <div class="grid grid-cols-2 gap-2 mt-3">
                            <button class="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-lg text-xs hover:bg-indigo-100 transition-all transform hover:scale-105 card-hover">
                                📚 继续这个故事
                            </button>
                            <button class="px-3 py-2 bg-pink-50 text-pink-700 rounded-lg text-xs hover:bg-pink-100 transition-all transform hover:scale-105 card-hover">
                                🎭 换个故事类型
                            </button>
                        </div>
                        
                        <div class="flex items-center gap-2 mt-2 text-xs">
                            <span class="text-purple-600 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm">auto_awesome</span>
                                精彩对话 +10经验
                            </span>
                            <span class="text-blue-500">+3💎</span>
                        </div>
                    </div>
                    
                    <!-- AI动作控制 -->
                    <div class="flex gap-2 mt-2 ml-4">
                        <button class="text-xs text-indigo-600 hover:text-indigo-800 flex items-center gap-1 px-2 py-1 hover:bg-indigo-50 rounded transition-colors">
                            <span class="material-icons-outlined text-xs">refresh</span>
                            重新生成
                        </button>
                        <button class="text-xs text-indigo-600 hover:text-indigo-800 flex items-center gap-1 px-2 py-1 hover:bg-indigo-50 rounded transition-colors">
                            <span class="material-icons-outlined text-xs">play_arrow</span>
                            继续
                        </button>
                    </div>
                </div>
            </div>

            <!-- 系统提示 - 成就解锁 -->
            <div class="flex justify-center">
                <div class="special-message text-white px-6 py-3 rounded-full shadow-lg">
                    <span class="flex items-center gap-2 text-sm font-medium relative z-10">
                        <span class="material-icons-outlined">emoji_events</span>
                        解锁成就：故事爱好者！获得 50🔥曦光微尘
                    </span>
                </div>
            </div>
        </main>

        <!-- 输入区域 -->
        <footer class="glass-strong border-t border-white/20 p-4">
            <!-- 快捷表情/动作栏 -->
            <div class="flex gap-2 mb-3 overflow-x-auto pb-2">
                <button class="px-3 py-1 bg-pink-100 text-pink-700 rounded-full text-sm hover:bg-pink-200 transition-colors flex-shrink-0 quick-btn">
                    😊 开心
                </button>
                <button class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm hover:bg-blue-200 transition-colors flex-shrink-0 quick-btn">
                    🤔 思考
                </button>
                <button class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors flex-shrink-0 quick-btn">
                    💝 送礼物
                </button>
                <button class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm hover:bg-green-200 transition-colors flex-shrink-0 quick-btn">
                    🌟 鼓励
                </button>
            </div>

            <!-- 主输入区 -->
            <div class="flex items-center gap-3">
                <div class="relative">
                    <button class="p-3 text-indigo-500 hover:bg-indigo-50 rounded-full transition-colors" onclick="toggleQuickMenu()">
                        <span class="material-icons-outlined">add_circle</span>
                    </button>
                    
                    <div id="quickMenu" class="absolute bottom-full left-0 mb-2 w-64 glass-strong rounded-xl shadow-lg hidden">
                        <div class="p-3 grid grid-cols-2 gap-2">
                            <button class="flex items-center gap-2 p-2 hover:bg-white/20 rounded-lg text-sm">
                                <span class="material-icons-outlined text-purple-500">psychology</span>
                                记忆胶囊
                            </button>
                            <button class="flex items-center gap-2 p-2 hover:bg-white/20 rounded-lg text-sm">
                                <span class="material-icons-outlined text-pink-500">favorite</span>
                                表达心意
                            </button>
                        </div>
                    </div>
                </div>

                <div class="flex-1 relative">
                    <input type="text" placeholder="和Sophie分享你的想法..." 
                           class="w-full p-3 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent input-field">
                </div>

                <button class="p-3 text-white rounded-full shadow-lg hover:shadow-xl transition-all transform hover:scale-105 btn-primary">
                    <span class="material-icons-outlined">send</span>
                </button>
            </div>
        </footer>
    </div>

    <!-- 游戏状态侧边栏 -->
    <aside class="game-sidebar w-80 glass-strong border-l border-white/20 flex flex-col">
        <!-- 羁绊信息 -->
        <div class="p-4 bg-gradient-to-br from-pink-50 to-purple-50 border-b border-white/20">
            <div class="text-center">
                <div class="relative inline-block mb-3">
                    <div class="w-20 h-20 rounded-full bg-gradient-to-br from-pink-300 to-purple-400 flex items-center justify-center text-white text-2xl font-bold border-4 border-white shadow-lg">
                        ❤️
                    </div>
                    <div class="absolute -bottom-1 -right-1 bg-yellow-400 text-white text-xs px-2 py-1 rounded-full font-bold">
                        Lv.4
                    </div>
                </div>
                <h3 class="font-bold text-gray-800">知己关系</h3>
                <p class="text-sm text-gray-600">与Sophie的羁绊</p>
                
                <div class="mt-3">
                    <div class="flex justify-between text-xs text-gray-500 mb-1">
                        <span>经验值</span>
                        <span>2,850 / 3,000</span>
                    </div>
                    <div class="w-full h-3 bg-white/50 rounded-full overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-pink-400 to-purple-500 rounded-full" style="width: 95%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">距离"灵魂伴侣"还需要 150 经验</p>
                </div>
            </div>
        </div>

        <!-- 当前代币状态 -->
        <div class="p-4 border-b border-white/20">
            <h4 class="font-semibold text-gray-800 mb-3">今日收获</h4>
            <div class="space-y-2">
                <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg card-hover">
                    <span class="text-sm flex items-center gap-2">
                        <span class="text-orange-500">🔥</span>
                        曦光微尘
                    </span>
                    <span class="font-bold text-orange-600">+25</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg card-hover">
                    <span class="text-sm flex items-center gap-2">
                        <span class="text-blue-500">💎</span>
                        心悦晶石
                    </span>
                    <span class="font-bold text-blue-600">+8</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-pink-50 rounded-lg card-hover">
                    <span class="text-sm flex items-center gap-2">
                        <span class="text-pink-500">💧</span>
                        羁绊之露
                    </span>
                    <span class="font-bold text-pink-600">+12</span>
                </div>
            </div>
        </div>

        <!-- 记忆胶囊 -->
        <div class="p-4 border-b border-white/20">
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-semibold text-gray-800">记忆胶囊</h4>
                <button class="text-xs text-indigo-600 hover:text-indigo-800">查看全部</button>
            </div>
            
            <div class="space-y-2">
                <div class="memory-capsule p-3 rounded-lg relative overflow-hidden">
                    <div class="relative z-10">
                        <div class="flex items-start gap-3">
                            <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1">
                                <p class="text-xs text-purple-700 font-medium">喜欢奇幻故事</p>
                                <span class="text-xs text-purple-500">刚刚被调用</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="memory-capsule p-3 rounded-lg relative overflow-hidden">
                    <div class="relative z-10">
                        <div class="flex items-start gap-3">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1">
                                <p class="text-xs text-blue-700 font-medium">最喜欢的颜色是蓝色</p>
                                <span class="text-xs text-blue-500">3天前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 今日任务 -->
        <div class="p-4 border-b border-white/20">
            <h4 class="font-semibold text-gray-800 mb-3">今日任务</h4>
            <div class="space-y-2">
                <div class="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
                    <span class="text-green-500 material-icons-outlined text-sm">check_circle</span>
                    <span class="text-sm text-green-700">与AI深度对话 (完成)</span>
                </div>
                <div class="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    <span class="text-gray-400 material-icons-outlined text-sm">radio_button_unchecked</span>
                    <span class="text-sm text-gray-600">分享一个故事 (0/1)</span>
                </div>
                <div class="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    <span class="text-gray-400 material-icons-outlined text-sm">radio_button_unchecked</span>
                    <span class="text-sm text-gray-600">获得50羁绊经验 (38/50)</span>
                </div>
            </div>
        </div>

        <!-- 特殊功能 -->
        <div class="flex-1 p-4">
            <h4 class="font-semibold text-gray-800 mb-3">特殊互动</h4>
            <div class="space-y-2">
                <button class="w-full p-3 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg hover:from-purple-200 hover:to-pink-200 transition-all card-hover">
                    <div class="flex items-center gap-2">
                        <span class="material-icons-outlined text-purple-500">auto_awesome</span>
                        <span class="text-sm font-medium text-gray-700">记忆碎片画图</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">将对话转化为艺术作品</p>
                </button>
                
                <button class="w-full p-3 bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg hover:from-yellow-200 hover:to-orange-200 transition-all card-hover">
                    <div class="flex items-center gap-2">
                        <span class="material-icons-outlined text-orange-500">celebration</span>
                        <span class="text-sm font-medium text-gray-700">赠送虚拟礼物</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">表达你的心意</p>
                </button>
            </div>
        </div>

        <!-- 移动端关闭按钮 -->
        <button class="md:hidden absolute top-4 right-4 p-2 text-gray-500 hover:text-gray-700" onclick="toggleSidebar()">
            <span class="material-icons-outlined">close</span>
        </button>
    </aside>

    <!-- 浮动代币奖励动画 -->
    <div id="tokenRewards" class="fixed inset-0 pointer-events-none z-50"></div>

    <script>
        // 粒子效果
        function createParticles() {
            const particles = document.getElementById('particles');
            particles.innerHTML = '';
            
            const particleCount = 20;
            const colors = ['#fbbf24', '#f472b6', '#a78bfa', '#34d399', '#60a5fa'];
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (6 + Math.random() * 4) + 's';
                particle.style.width = particle.style.height = (3 + Math.random() * 5) + 'px';
                particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                particle.style.opacity = '0.6';
                particles.appendChild(particle);
            }
        }

        // 场景切换
        function changeScene(scene) {
            const statusText = {
                garden: '在花园里漫步',
                cafe: '在咖啡厅里放松', 
                library: '在图书馆里学习'
            };
            document.getElementById('aiStatus').textContent = statusText[scene] || '在线';
        }

        // 切换侧边栏
        function toggleSidebar() {
            const sidebar = document.querySelector('.game-sidebar');
            sidebar.classList.toggle('open');
        }

        // 切换快捷菜单
        function toggleQuickMenu() {
            const menu = document.getElementById('quickMenu');
            menu.classList.toggle('hidden');
        }

        // 模拟代币奖励动画
        function showTokenReward(type, amount, element) {
            const reward = document.createElement('div');
            reward.className = 'token-reward absolute text-lg font-bold';
            const rect = element.getBoundingClientRect();
            reward.style.left = rect.left + 'px';
            reward.style.top = rect.top + 'px';
            
            const icons = {
                'dust': '🔥',
                'crystal': '💎',
                'essence': '💧'
            };
            
            reward.innerHTML = `${icons[type]} +${amount}`;
            reward.style.color = type === 'dust' ? '#f59e0b' : type === 'crystal' ? '#3b82f6' : '#ec4899';
            
            document.getElementById('tokenRewards').appendChild(reward);
            
            setTimeout(() => {
                reward.remove();
            }, 1200);
        }

        // 模拟AI状态变化
        function updateAIAvatar(emotion) {
            const avatar = document.querySelector('.ai-avatar');
            avatar.className = `ai-avatar ${emotion}`;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            
            // 模拟AI状态变化
            setTimeout(() => updateAIAvatar('thinking'), 3000);
            setTimeout(() => updateAIAvatar('happy'), 6000);
            
            // 模拟获得代币
            setTimeout(() => {
                const chatBubble = document.querySelector('.chat-bubble');
                showTokenReward('crystal', 3, chatBubble);
            }, 4000);
        });
    </script>
</body>
</html> 