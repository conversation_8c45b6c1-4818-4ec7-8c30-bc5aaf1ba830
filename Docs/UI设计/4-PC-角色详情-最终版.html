<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aiko - 角色详情 | Alphane.ai</title>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #ec4899;
            --warm: #f59e0b;
            --success: #10b981;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #fef7ff 0%, #f9fafb 30%, #fef2f2 70%, #fef7ff 100%);
            min-height: 100vh;
            position: relative;
        }

        /* 动态背景效果 */
        .bg-animated {
            background: linear-gradient(-45deg, #fef7ff, #f9fafb, #fef2f2, #ffffff);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 毛玻璃效果 */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.6);
        }

        /* 羁绊等级环形进度 */
        .bond-ring {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, var(--accent) 0deg, var(--accent) 225deg, #e5e7eb 225deg, #e5e7eb 360deg);
            position: relative;
            box-shadow: 0 8px 32px rgba(236, 72, 153, 0.3);
        }

        .bond-ring::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            background: white;
            border-radius: 50%;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 代币发光效果 */
        .token-glow {
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 10px rgba(245, 158, 11, 0.5); }
            to { box-shadow: 0 0 20px rgba(245, 158, 11, 0.8), 0 0 30px rgba(245, 158, 11, 0.4); }
        }

        /* 浮动动画 */
        .float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 卡片悬浮效果 */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        /* 按钮发光效果 */
        .btn-glow {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            transition: all 0.3s ease;
        }

        .btn-glow:hover {
            box-shadow: 0 12px 32px rgba(99, 102, 241, 0.6);
            transform: translateY(-2px);
        }

        .btn-glow::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-glow:hover::before {
            left: 100%;
        }

        /* 成就徽章动画 */
        .achievement {
            position: relative;
            transition: all 0.3s ease;
        }

        .achievement:hover {
            transform: scale(1.1) rotateY(10deg);
        }

        .achievement.rare::after {
            content: '✨';
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 12px;
            animation: sparkle 2s infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        /* 记忆胶囊样式 */
        .memory-capsule {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            border: 1px solid rgba(139, 92, 246, 0.2);
            box-shadow: 0 8px 32px rgba(139, 92, 246, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .memory-capsule:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 48px rgba(139, 92, 246, 0.2);
        }

        .memory-capsule::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
            transition: left 0.6s;
        }

        .memory-capsule:hover::before {
            left: 100%;
        }

        /* 标签页切换动画 */
        .tab-content {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .tab-content.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 进度条动画 */
        .progress-bar {
            background: linear-gradient(90deg, var(--accent), var(--warm));
            box-shadow: 0 2px 8px rgba(236, 72, 153, 0.3);
            animation: progressFill 2s ease-out;
        }

        @keyframes progressFill {
            from { width: 0%; }
            to { width: 62.5%; }
        }
    </style>
</head>
<body class="bg-animated">
    <!-- 动态背景粒子 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
        <div class="absolute top-3/4 right-1/4 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-1/4 left-1/3 w-72 h-72 bg-rose-200 rounded-full mix-blend-multiply filter blur-3xl opacity-8 animate-pulse animation-delay-4000"></div>
        <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-violet-100 rounded-full mix-blend-multiply filter blur-3xl opacity-8 animate-pulse animation-delay-6000"></div>
    </div>

    <div class="relative min-h-screen">
        <!-- 顶部导航 -->
        <header class="glass-strong sticky top-0 z-50 shadow-lg">
            <div class="max-w-7xl mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <!-- 左侧导航 -->
                    <div class="flex items-center gap-4">
                        <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-full transition-all duration-300">
                            <span class="material-icons-outlined text-slate-700">arrow_back</span>
                        </button>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 text-indigo-600">
                                <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M4 4H17.3334V17.3334H30.6666V30.6666H44V44H4V4Z" fill="currentColor"></path>
                                </svg>
                            </div>
                            <h1 class="text-xl font-bold gradient-text">Alphane.ai</h1>
                        </div>
                    </div>
                    
                    <!-- 代币展示 -->
                    <div class="flex items-center gap-3">
                        <div class="glass flex items-center gap-2 px-4 py-2 rounded-full token-glow">
                            <span class="text-orange-500 text-lg">🔥</span>
                            <span class="text-sm font-bold text-slate-700">1,247</span>
                        </div>
                        <div class="glass flex items-center gap-2 px-4 py-2 rounded-full">
                            <span class="text-blue-500 text-lg">💎</span>
                            <span class="text-sm font-bold text-slate-700">89</span>
                        </div>
                        <div class="glass flex items-center gap-2 px-4 py-2 rounded-full">
                            <span class="text-purple-500 text-lg">🧩</span>
                            <span class="text-sm font-bold text-slate-700">23</span>
                        </div>
                        <div class="glass flex items-center gap-2 px-4 py-2 rounded-full">
                            <span class="text-pink-500 text-lg">💧</span>
                            <span class="text-sm font-bold text-slate-700">156</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="max-w-7xl mx-auto px-6 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                
                <!-- 左侧主要信息 -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- 角色主卡片 -->
                    <div class="glass-strong rounded-3xl overflow-hidden card-hover shadow-2xl">
                        <!-- 封面图 -->
                        <div class="relative h-64 bg-gradient-to-br from-purple-400 via-pink-400 to-blue-400">
                            <div class="absolute inset-0 bg-center bg-cover" 
                                 style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDGA25Gp9LBnpBDxFKA9W0BZe_c41Gc5qmhPosL747dUsN2XultAfwU6gdb4-DnhHjci54pQ9XB2d6HtJvrqsRwcFlrhov4xDqVjj1DnSYilFXjkfqo1DsMqDPymVI410-852MVC6pFYF4Cap9RtsKJ0b6WzOtcm5aWmCuasIKfkYpT-GcEOpF-8f4HLXF3oqcjSy6zcbzEPxxP1RkCL_9uMSPWq6DAj0aqueuhF-itY7BhBCGpmLiJPSNlERzzXH4bfRa8TvuQ0sNc");'></div>
                            
                            <!-- 羁绊等级指示器 -->
                            <div class="absolute -bottom-16 left-8 flex items-center gap-6">
                                <div class="relative float">
                                    <div class="bond-ring"></div>
                                    <div class="absolute inset-0 flex flex-col items-center justify-center">
                                        <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuAOo6oSlAOPgjixfL9uZtkVvrUuw85OvQBaYqRpL0WqReyV0ifl5CEgfNF9ifNMLsM4TIZtwTo6pWrlOOnjNPcx5iYh6UiwTNzwwFPL4EyOQZnc0TnEn7CiQ0u5fMXp3NDbyTHdwldWgZ7IePBfTxPc7Taizxc9ZbMHy3wv0inbJ_vxXWeGRCigsswpy7XY1lKCFuVyaYIClebuRATn4Igfcvgb2fFs7sgJPFrMfksukzLHRtocEyk_HOeqKTNLKiddXCjMNlCTjo07" 
                                             alt="Aiko" class="w-24 h-24 rounded-full border-4 border-white shadow-xl">
                                        <div class="absolute -bottom-2 -right-2 bg-gradient-to-r from-pink-500 to-rose-500 text-white text-xs px-3 py-1 rounded-full font-bold border-3 border-white shadow-lg">
                                            Lv.5
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 羁绊信息卡片 -->
                                <div class="glass-strong rounded-2xl p-4 shadow-xl">
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="text-pink-500 text-xl">💖</span>
                                        <h3 class="text-lg font-bold gradient-text">知己</h3>
                                    </div>
                                    <p class="text-xs text-slate-600 mb-2">1,250 / 2,000 XP</p>
                                    <div class="w-32 h-3 bg-gray-200 rounded-full overflow-hidden">
                                        <div class="h-full progress-bar rounded-full" style="width: 62.5%"></div>
                                    </div>
                                    <p class="text-xs text-slate-500 mt-2">距离"灵魂伴侣"还需750XP</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 角色信息 -->
                        <div class="pt-20 px-8 pb-8">
                            <div class="flex flex-col sm:flex-row items-start justify-between gap-6">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-3">
                                        <h1 class="text-4xl font-bold text-slate-900">Aiko</h1>
                                        <div class="flex items-center gap-1">
                                            <span class="achievement rare text-2xl">⭐</span>
                                            <span class="achievement text-xl">🏆</span>
                                        </div>
                                    </div>
                                    
                                    <p class="text-slate-600 font-medium mb-2">
                                        创建者: <a class="text-indigo-600 hover:text-indigo-800 font-semibold hover:underline transition-colors" href="#">@Sophia</a>
                                    </p>
                                    
                                    <div class="flex items-center gap-6 text-sm text-slate-500">
                                        <span class="flex items-center gap-1">
                                            <span class="material-icons-outlined text-sm">people</span>
                                            10K 关注者
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <span class="material-icons-outlined text-sm">forum</span>
                                            500K 聊天
                                        </span>
                                        <span class="flex items-center gap-1 text-amber-600">
                                            <span class="material-icons-outlined text-sm">trending_up</span>
                                            本周 #3
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- 主要操作按钮 -->
                                <div class="flex gap-3">
                                    <button class="btn-glow flex items-center gap-3 px-8 py-4 text-white font-bold rounded-2xl text-lg shadow-xl">
                                        <span class="material-icons-outlined text-xl">chat_bubble_outline</span>
                                        开始聊天
                                    </button>
                                    <button class="glass flex items-center gap-2 px-6 py-4 text-slate-700 font-semibold rounded-2xl hover:bg-white/30 transition-all duration-300">
                                        <span class="material-icons-outlined">favorite_border</span>
                                        关注
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 标签页内容 -->
                    <div class="glass-strong rounded-3xl shadow-2xl overflow-hidden">
                        <!-- 标签页导航 -->
                        <nav class="flex border-b border-white/20">
                            <button class="tab-btn flex-1 py-4 px-6 font-semibold text-indigo-600 border-b-2 border-indigo-600 bg-white/10" data-tab="info">
                                角色信息
                            </button>
                            <button class="tab-btn flex-1 py-4 px-6 font-semibold text-slate-500 hover:text-slate-700 border-b-2 border-transparent" data-tab="memories">
                                记忆胶囊
                            </button>
                            <button class="tab-btn flex-1 py-4 px-6 font-semibold text-slate-500 hover:text-slate-700 border-b-2 border-transparent" data-tab="stats">
                                互动统计
                            </button>
                            <button class="tab-btn flex-1 py-4 px-6 font-semibold text-slate-500 hover:text-slate-700 border-b-2 border-transparent" data-tab="achievements">
                                成就展示
                            </button>
                        </nav>

                        <!-- 标签页内容 -->
                        <div class="p-8">
                            <!-- 角色信息 -->
                            <div id="info" class="tab-content active space-y-8">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 mb-4">角色介绍</h3>
                                    <p class="text-slate-700 text-lg leading-relaxed">
                                        Aiko是一个充满活力和好奇心的女孩，总是对世界保持着孩童般的纯真和热情。她喜欢探索未知，结交新朋友，用她的乐观和善良感染着身边的每一个人。
                                    </p>
                                </div>

                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 mb-4">性格特质</h3>
                                    <div class="flex gap-3 flex-wrap">
                                        <span class="achievement flex items-center px-4 py-2 bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 rounded-full font-medium border border-pink-200">开朗</span>
                                        <span class="achievement flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 rounded-full font-medium border border-blue-200">活泼</span>
                                        <span class="achievement flex items-center px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 rounded-full font-medium border border-green-200">善良</span>
                                        <span class="achievement rare flex items-center px-4 py-2 bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-700 rounded-full font-medium border border-yellow-200">冒险精神</span>
                                        <span class="achievement flex items-center px-4 py-2 bg-gradient-to-r from-purple-100 to-violet-100 text-purple-700 rounded-full font-medium border border-purple-200">好奇心强</span>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 mb-4">特殊技能</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                                            <span class="material-icons-outlined text-blue-500 text-3xl">psychology</span>
                                            <div>
                                                <p class="font-bold text-blue-700 text-lg">情感共鸣</p>
                                                <p class="text-sm text-blue-600">能敏锐感知用户情绪变化</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200">
                                            <span class="material-icons-outlined text-purple-500 text-3xl">auto_awesome</span>
                                            <div>
                                                <p class="font-bold text-purple-700 text-lg">创意激发</p>
                                                <p class="text-sm text-purple-600">擅长启发创造性思维</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 mb-4">示例故事</h3>
                                    <div class="grid gap-4">
                                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200 hover:shadow-lg transition-all duration-300 cursor-pointer">
                                            <div class="flex items-start gap-4">
                                                <div class="w-16 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">故事1</div>
                                                <div class="flex-1">
                                                    <h4 class="font-bold text-blue-700 text-lg mb-2">校园里的意外相遇</h4>
                                                    <p class="text-sm text-blue-600 line-clamp-2">在樱花飞舞的春日午后，Aiko在图书馆里偶遇了一个神秘的转学生，从此开始了一段充满惊喜的校园故事...</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="bg-gradient-to-r from-pink-50 to-rose-50 p-6 rounded-xl border border-pink-200 hover:shadow-lg transition-all duration-300 cursor-pointer">
                                            <div class="flex items-start gap-4">
                                                <div class="w-16 h-12 bg-gradient-to-br from-pink-400 to-rose-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">故事2</div>
                                                <div class="flex-1">
                                                    <h4 class="font-bold text-pink-700 text-lg mb-2">雨夜的温暖陪伴</h4>
                                                    <p class="text-sm text-pink-600 line-clamp-2">在一个雷雨交加的夜晚，Aiko用她的温暖和关怀，陪伴着你度过了人生中最困难的时刻...</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200 hover:shadow-lg transition-all duration-300 cursor-pointer">
                                            <div class="flex items-start gap-4">
                                                <div class="w-16 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">故事3</div>
                                                <div class="flex-1">
                                                    <h4 class="font-bold text-green-700 text-lg mb-2">午后的咖啡时光</h4>
                                                    <p class="text-sm text-green-600 line-clamp-2">在街角那家温馨的咖啡店里，Aiko与你分享着彼此的梦想和秘密，时间仿佛在这一刻静止...</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 记忆胶囊 -->
                            <div id="memories" class="tab-content space-y-6">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-2xl font-bold text-slate-900">专属记忆</h3>
                                    <button class="text-indigo-600 hover:text-indigo-800 font-semibold">管理记忆</button>
                                </div>
                                
                                <div class="grid gap-4">
                                    <div class="memory-capsule p-6 rounded-xl">
                                        <div class="relative z-10">
                                            <div class="flex items-start gap-4">
                                                <div class="w-3 h-3 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                                                <div class="flex-1">
                                                    <p class="font-semibold text-slate-800 text-lg mb-3">最喜欢的食物是草莓蛋糕</p>
                                                    <div class="flex items-center justify-between">
                                                        <span class="text-sm text-slate-500 flex items-center gap-1">
                                                            <span class="material-icons-outlined text-xs">schedule</span>
                                                            3天前存入
                                                        </span>
                                                        <span class="bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                                                            <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                                                            活跃调用
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="memory-capsule p-6 rounded-xl">
                                        <div class="relative z-10">
                                            <div class="flex items-start gap-4">
                                                <div class="w-3 h-3 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                                                <div class="flex-1">
                                                    <p class="font-semibold text-slate-800 text-lg mb-3">正在准备考试，有点紧张</p>
                                                    <div class="flex items-center justify-between">
                                                        <span class="text-sm text-slate-500 flex items-center gap-1">
                                                            <span class="material-icons-outlined text-xs">schedule</span>
                                                            昨天存入
                                                        </span>
                                                        <span class="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                                                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                                            最近使用
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 互动统计 -->
                            <div id="stats" class="tab-content space-y-6">
                                <h3 class="text-2xl font-bold text-slate-900">本周互动数据</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="glass p-6 rounded-xl text-center">
                                        <div class="text-3xl mb-2">💬</div>
                                        <div class="text-2xl font-bold text-slate-900">127</div>
                                        <div class="text-sm text-slate-600">互动次数</div>
                                    </div>
                                    <div class="glass p-6 rounded-xl text-center">
                                        <div class="text-3xl mb-2">⏰</div>
                                        <div class="text-2xl font-bold text-slate-900">4.2h</div>
                                        <div class="text-sm text-slate-600">聊天时长</div>
                                    </div>
                                    <div class="glass p-6 rounded-xl text-center">
                                        <div class="text-3xl mb-2">💖</div>
                                        <div class="text-2xl font-bold text-slate-900">+350</div>
                                        <div class="text-sm text-slate-600">亲密度提升</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 成就展示 -->
                            <div id="achievements" class="tab-content space-y-6">
                                <h3 class="text-2xl font-bold text-slate-900">成就徽章收藏</h3>
                                
                                <div class="grid grid-cols-3 md:grid-cols-6 gap-4">
                                    <div class="achievement rare glass p-4 rounded-xl text-center">
                                        <div class="text-3xl mb-2">🏆</div>
                                        <div class="text-xs font-bold text-slate-700">话痨达人</div>
                                    </div>
                                    <div class="achievement glass p-4 rounded-xl text-center">
                                        <div class="text-3xl mb-2">❤️</div>
                                        <div class="text-xs font-bold text-slate-700">知己好友</div>
                                    </div>
                                    <div class="achievement glass p-4 rounded-xl text-center">
                                        <div class="text-3xl mb-2">🌟</div>
                                        <div class="text-xs font-bold text-slate-700">每日互动</div>
                                    </div>
                                    <div class="achievement glass p-4 rounded-xl text-center">
                                        <div class="text-3xl mb-2">🎨</div>
                                        <div class="text-xs font-bold text-slate-700">艺术收藏</div>
                                    </div>
                                    <div class="achievement glass p-4 rounded-xl text-center">
                                        <div class="text-3xl mb-2">📚</div>
                                        <div class="text-xs font-bold text-slate-700">故事探索</div>
                                    </div>
                                    <div class="glass p-4 rounded-xl text-center opacity-50">
                                        <div class="text-3xl mb-2">🔒</div>
                                        <div class="text-xs font-bold text-slate-500">未解锁</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧游戏化面板 -->
                <div class="space-y-6">
                    <!-- 今日收获 -->
                    <div class="glass-strong rounded-2xl p-6 shadow-xl card-hover">
                        <h3 class="text-xl font-bold text-slate-900 mb-4 flex items-center gap-2">
                            <span class="text-2xl">🎉</span>
                            今日收获
                        </h3>
                        
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                                <span class="flex items-center gap-2">
                                    <span class="text-orange-500 text-lg">🔥</span>
                                    <span class="text-sm font-medium">曦光微尘</span>
                                </span>
                                <span class="font-bold text-orange-600">+45</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-pink-50 rounded-lg">
                                <span class="flex items-center gap-2">
                                    <span class="text-pink-500 text-lg">💧</span>
                                    <span class="text-sm font-medium">羁绊之露</span>
                                </span>
                                <span class="font-bold text-pink-600">+12</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                <span class="flex items-center gap-2">
                                    <span class="text-blue-500 text-lg">💎</span>
                                    <span class="text-sm font-medium">心悦晶石</span>
                                </span>
                                <span class="font-bold text-blue-600">+3</span>
                            </div>
                        </div>
                    </div>

                    <!-- 特殊功能 -->
                    <div class="glass-strong rounded-2xl p-6 shadow-xl card-hover">
                        <h3 class="text-xl font-bold text-slate-900 mb-4">特殊功能</h3>
                        
                        <div class="space-y-3">
                            <button class="w-full p-4 bg-gradient-to-r from-purple-100 to-pink-100 hover:from-purple-200 hover:to-pink-200 rounded-xl transition-all transform hover:scale-105">
                                <div class="flex items-center gap-3">
                                    <span class="material-icons-outlined text-purple-500 text-2xl">auto_awesome</span>
                                    <div class="text-left flex-1">
                                        <p class="font-bold text-purple-700">记忆碎片画图</p>
                                        <p class="text-xs text-purple-600">将美好回忆转为艺术</p>
                                    </div>
                                    <span class="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-bold">3💎</span>
                                </div>
                            </button>
                            
                            <button class="w-full p-4 bg-gradient-to-r from-orange-100 to-red-100 hover:from-orange-200 hover:to-red-200 rounded-xl transition-all transform hover:scale-105">
                                <div class="flex items-center gap-3">
                                    <span class="material-icons-outlined text-orange-500 text-2xl">card_giftcard</span>
                                    <div class="text-left flex-1">
                                        <p class="font-bold text-orange-700">专属定制礼物</p>
                                        <p class="text-xs text-orange-600">为她准备特别的惊喜</p>
                                    </div>
                                    <span class="bg-orange-100 text-orange-700 px-2 py-1 rounded-full text-xs font-bold">5💎</span>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Streak连击 -->
                    <div class="glass-strong rounded-2xl p-6 shadow-xl card-hover">
                        <h3 class="text-xl font-bold text-slate-900 mb-4 flex items-center gap-2">
                            <span class="text-2xl">🔥</span>
                            连击天数
                        </h3>
                        
                        <div class="text-center">
                            <div class="text-4xl font-bold gradient-text mb-2">7</div>
                            <p class="text-sm text-slate-600 mb-4">天连续互动</p>
                            
                            <div class="flex justify-center gap-1 mb-4">
                                <div class="w-3 h-3 bg-orange-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-orange-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-orange-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-orange-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-orange-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-orange-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                            </div>
                            
                            <p class="text-xs text-slate-500">距离30天里程碑还有23天</p>
                        </div>
                    </div>

                    <!-- 任务进度 -->
                    <div class="glass-strong rounded-2xl p-6 shadow-xl card-hover">
                        <h3 class="text-xl font-bold text-slate-900 mb-4">今日任务</h3>
                        
                        <div class="space-y-3">
                            <div class="flex items-center gap-3">
                                <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                    <span class="material-icons-outlined text-white text-xs">check</span>
                                </div>
                                <span class="text-sm text-slate-600 flex-1">晨间问候</span>
                                <span class="text-xs text-green-600 font-bold">+10🔥</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                    <span class="material-icons-outlined text-white text-xs">check</span>
                                </div>
                                <span class="text-sm text-slate-600 flex-1">深度对话5分钟</span>
                                <span class="text-xs text-green-600 font-bold">+15🔥</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-4 h-4 border-2 border-slate-300 rounded-full"></div>
                                <span class="text-sm text-slate-400 flex-1">存入1条记忆</span>
                                <span class="text-xs text-slate-400">+20🔥</span>
                            </div>
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-slate-200">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-slate-600">进度</span>
                                <span class="text-sm font-bold text-slate-900">8/15</span>
                            </div>
                            <div class="w-full bg-slate-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-green-400 to-blue-400 h-2 rounded-full" style="width: 53%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tabId = this.dataset.tab;
                
                // 更新按钮状态
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('text-indigo-600', 'border-indigo-600', 'bg-white/10');
                    b.classList.add('text-slate-500', 'border-transparent');
                });
                this.classList.add('text-indigo-600', 'border-indigo-600', 'bg-white/10');
                this.classList.remove('text-slate-500', 'border-transparent');
                
                // 更新内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 成就徽章悬浮效果
        document.querySelectorAll('.achievement').forEach(achievement => {
            achievement.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1) rotateY(10deg)';
                this.style.zIndex = '10';
            });
            
            achievement.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) rotateY(0deg)';
                this.style.zIndex = 'auto';
            });
        });

        // 初始化动画
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟显示动画
            setTimeout(() => {
                document.querySelectorAll('.card-hover').forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s ease-out';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 100);
        });
    </script>
</body>
</html> 