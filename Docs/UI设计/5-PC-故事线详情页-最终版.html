<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故事线：Aiko的校园奇遇 - Alphane.ai</title>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        :root {
            --alphane-primary: #6366f1;
            --alphane-secondary: #8b5cf6;
            --alphane-accent: #ec4899;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
        }

        .gradient-blur-story {
            filter: blur(80px);
            opacity: 0.2;
            z-index: -1;
        }

        /* 故事进度条动画 */
        .story-progress {
            background: linear-gradient(90deg, #10b981 0%, #10b981 45%, #e5e7eb 45%, #e5e7eb 100%);
            position: relative;
            overflow: hidden;
        }

        .story-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 45%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(220%); }
        }

        /* 章节节点样式 */
        .chapter-node {
            position: relative;
            transition: all 0.3s ease;
        }

        .chapter-node.completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
        }

        .chapter-node.current {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            animation: pulse 2s infinite;
            box-shadow: 0 0 25px rgba(59, 130, 246, 0.5);
        }

        .chapter-node.locked {
            background: #f3f4f6;
            color: #9ca3af;
            border: 2px dashed #d1d5db;
        }

        /* 奖励卡片动画 */
        .reward-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
        }

        .reward-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
        }

        /* 选择分支动画 */
        .choice-branch {
            position: relative;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            border: 2px solid #6366f1;
            transition: all 0.3s ease;
        }

        .choice-branch:hover {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            transform: translateX(10px);
        }

        .choice-branch::before {
            content: '→';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .choice-branch:hover::before {
            opacity: 1;
            right: 10px;
        }

        /* 难度标识 */
        .difficulty-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .difficulty-easy { color: #10b981; }
        .difficulty-normal { color: #f59e0b; }
        .difficulty-hard { color: #ef4444; }

        /* 角色卡片3D效果 */
        .character-card {
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
        }

        .character-card:hover {
            transform: rotateY(10deg) rotateX(5deg);
        }

        /* 成就预览闪光 */
        .achievement-glow {
            position: relative;
            overflow: hidden;
        }

        .achievement-glow::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,215,0,0.6), transparent);
            animation: achievementShine 3s infinite;
        }

        @keyframes achievementShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-900 min-h-screen">
    <div class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden">
        <!-- 动态背景 -->
        <div class="absolute top-0 left-0 w-2/3 h-2/3 bg-gradient-to-br from-sky-400 to-indigo-500 gradient-blur-story animate-pulse"></div>
        <div class="absolute bottom-0 right-0 w-1/2 h-1/2 bg-gradient-to-tl from-rose-400 to-amber-500 gradient-blur-story animate-pulse"></div>

        <!-- 头部导航 -->
        <header class="sticky top-0 z-50 flex items-center justify-between whitespace-nowrap border-b border-solid border-slate-200 bg-white/80 px-6 py-4 backdrop-blur-md shadow-sm sm:px-10">
            <div class="flex items-center gap-3 text-slate-900">
                <button onclick="history.back()" class="text-slate-600 hover:text-sky-600 p-2 hover:bg-sky-50 rounded-full transition-colors">
                    <span class="material-icons-outlined">arrow_back</span>
                </button>
                <div class="size-7 text-sky-600">
                    <svg class="h-7 w-7" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path>
                    </svg>
                </div>
                <h2 class="text-xl font-semibold tracking-tight text-slate-900">故事线详情</h2>
            </div>
            
            <!-- 顶部代币显示 -->
            <div class="hidden md:flex items-center gap-4">
                <div class="flex items-center gap-2 bg-orange-100 px-3 py-1 rounded-full">
                    <span class="text-orange-500">🔥</span>
                    <span class="text-sm font-semibold text-orange-700">1,247</span>
                </div>
                <div class="flex items-center gap-2 bg-blue-100 px-3 py-1 rounded-full">
                    <span class="text-blue-500">💎</span>
                    <span class="text-sm font-semibold text-blue-700">89</span>
                </div>
            </div>
        </header>

        <main class="z-10 flex flex-1 justify-center px-4 py-8 sm:px-8 md:px-16 lg:px-24 xl:px-32">
            <div class="w-full max-w-6xl">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    
                    <!-- 左侧：故事主体内容 -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- 故事封面与进度 -->
                        <div class="relative overflow-hidden rounded-xl shadow-2xl bg-white/80 backdrop-blur-lg">
                            <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuDGA25Gp9LBnpBDxFKA9W0BZe_c41Gc5qmhPosL747dUsN2XultAfwU6gdb4-DnhHjci54pQ9XB2d6HtJvrqsRwcFlrhov4xDqVjj1DnSYilFXjkfqo1DsMqDPymVI410-852MVC6pFYF4Cap9RtsKJ0b6WzOtcm5aWmCuasIKfkYpT-GcEOpF-8f4HLXF3oqcjSy6zcbzEPxxP1RkCL_9uMSPWq6DAj0aqueuhF-itY7BhBCGpmLiJPSNlERzzXH4bfRa8TvuQ0sNc" alt="故事封面" class="h-64 w-full object-cover sm:h-80 md:h-96">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                            
                            <!-- 故事标题与难度 -->
                            <div class="absolute bottom-0 left-0 p-6 sm:p-8">
                                <div class="flex items-center gap-3 mb-2">
                                    <h1 class="text-3xl font-bold text-white shadow-lg sm:text-4xl md:text-5xl">Aiko的校园奇遇</h1>
                                    <span class="difficulty-badge difficulty-normal">
                                        ⭐⭐⭐
                                    </span>
                                </div>
                                <p class="text-sm text-slate-300 sm:text-base">
                                    由 <a href="4-PC-角色详情.html" class="hover:underline font-medium text-sky-300">Aiko</a> 主演 
                                    · 预计游戏时长：30-45分钟
                                </p>
                                
                                <!-- 故事进度条 -->
                                <div class="mt-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm text-white">故事进度</span>
                                        <span class="text-sm text-white">3/7 章节</span>
                                    </div>
                                    <div class="story-progress h-3 rounded-full"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 故事信息与操作 -->
                        <div class="rounded-xl bg-white/80 p-6 shadow-xl backdrop-blur-lg sm:p-8">
                            <div class="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
                                <div>
                                    <p class="text-sm text-slate-600">创建者: <a href="#" class="font-medium text-sky-600 hover:underline">@StoryMaster</a></p>
                                    <div class="mt-1 flex items-center gap-4 text-sm text-slate-500">
                                        <span class="flex items-center gap-1">
                                            <span class="material-icons-outlined text-base">forum</span>
                                            1.2k 次体验
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <span class="material-icons-outlined text-base">thumb_up_off_alt</span>
                                            345 次点赞
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <span class="material-icons-outlined text-base">star</span>
                                            4.8 评分
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="flex shrink-0 gap-2">
                                    <button class="flex h-10 items-center justify-center gap-1.5 rounded-lg bg-slate-200 px-3 text-sm font-semibold text-slate-700 hover:bg-slate-300 transition-colors">
                                        <span class="material-icons-outlined text-lg">thumb_up_off_alt</span>
                                        <span class="hidden sm:inline">点赞</span>
                                    </button>
                                    <button class="flex h-10 items-center justify-center gap-1.5 rounded-lg bg-slate-200 px-3 text-sm font-semibold text-slate-700 hover:bg-slate-300 transition-colors">
                                        <span class="material-icons-outlined text-lg">share</span>
                                        <span class="hidden sm:inline">分享</span>
                                    </button>
                                    <button class="flex h-10 items-center justify-center gap-1.5 rounded-lg bg-yellow-100 px-3 text-sm font-semibold text-yellow-700 hover:bg-yellow-200 transition-colors">
                                        <span class="material-icons-outlined text-lg">star_border</span>
                                        <span class="hidden sm:inline">收藏</span>
                                    </button>
                                </div>
                            </div>

                            <hr class="my-6 border-slate-200">

                            <!-- 故事简介 -->
                            <div>
                                <h2 class="mb-3 text-xl font-semibold text-slate-800">故事简介</h2>
                                <p class="text-base leading-relaxed text-slate-700">
                                    加入 Aiko 在 Alphane 学院的奇妙冒险！在这个充满活力的校园里，Aiko 将遇到形形色色的人物，解开神秘的谜题，并发现隐藏在日常学习生活之下的秘密。你的选择将决定故事的走向和 Aiko 的命运。
                                </p>
                            </div>

                            <!-- 故事标签 -->
                            <div class="mt-6">
                                <h3 class="mb-3 text-lg font-semibold text-slate-800">故事标签</h3>
                                <div class="flex gap-2 flex-wrap">
                                    <span class="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">校园</span>
                                    <span class="bg-pink-100 text-pink-700 px-3 py-1 rounded-full text-sm font-medium">青春</span>
                                    <span class="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">奇幻</span>
                                    <span class="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">友谊</span>
                                    <span class="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium">冒险</span>
                                </div>
                            </div>

                            <!-- 开场白 -->
                            <div class="mt-6">
                                <h2 class="mb-3 text-xl font-semibold text-slate-800">开场白</h2>
                                <blockquote class="border-l-4 border-sky-500 bg-sky-50 p-4 italic text-slate-700 rounded-r-lg">
                                    "新学期开始了，阳光明媚的早晨，你踏入了 Alphane 学院的大门。微风拂过脸颊，带来了阵阵花香和远处操场上传来的欢笑声。就在这时，一个充满活力的身影向你跑来，是 Aiko！'嘿！你也是新生吗？太巧了，我叫 Aiko，以后请多指教啦！'她笑着对你说，眼中闪烁着期待的光芒。"
                                </blockquote>
                            </div>
                        </div>

                        <!-- 章节进度 -->
                        <div class="rounded-xl bg-white/80 p-6 shadow-xl backdrop-blur-lg">
                            <h2 class="mb-4 text-xl font-semibold text-slate-800 flex items-center gap-2">
                                <span class="material-icons-outlined text-indigo-500">auto_stories</span>
                                章节进度
                            </h2>
                            
                            <div class="space-y-4">
                                <!-- 已完成章节 -->
                                <div class="chapter-node completed p-4 rounded-lg flex items-center gap-4">
                                    <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center font-bold">1</div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold">初入校园</h3>
                                        <p class="text-sm opacity-90">与Aiko的第一次相遇</p>
                                    </div>
                                    <span class="material-icons-outlined">check_circle</span>
                                </div>

                                <div class="chapter-node completed p-4 rounded-lg flex items-center gap-4">
                                    <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center font-bold">2</div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold">食堂奇遇</h3>
                                        <p class="text-sm opacity-90">午餐时间的意外发现</p>
                                    </div>
                                    <span class="material-icons-outlined">check_circle</span>
                                </div>

                                <!-- 当前章节 -->
                                <div class="chapter-node current p-4 rounded-lg flex items-center gap-4">
                                    <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center font-bold">3</div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold">图书馆的秘密</h3>
                                        <p class="text-sm opacity-90">正在进行中...</p>
                                    </div>
                                    <span class="material-icons-outlined animate-spin">sync</span>
                                </div>

                                <!-- 未解锁章节 -->
                                <div class="chapter-node locked p-4 rounded-lg flex items-center gap-4">
                                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center font-bold">4</div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold">社团招新</h3>
                                        <p class="text-sm">需要完成前续章节</p>
                                    </div>
                                    <span class="material-icons-outlined">lock</span>
                                </div>

                                <div class="chapter-node locked p-4 rounded-lg flex items-center gap-4">
                                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center font-bold">5</div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold">校园祭准备</h3>
                                        <p class="text-sm">需要达到羁绊等级4</p>
                                    </div>
                                    <span class="material-icons-outlined">lock</span>
                                </div>
                            </div>
                        </div>

                        <!-- 关键选择分支 -->
                        <div class="rounded-xl bg-white/80 p-6 shadow-xl backdrop-blur-lg">
                            <h2 class="mb-4 text-xl font-semibold text-slate-800 flex items-center gap-2">
                                <span class="material-icons-outlined text-purple-500">call_split</span>
                                关键选择
                            </h2>
                            
                            <p class="text-sm text-slate-600 mb-4">在第3章中，你将面临重要的选择分支：</p>
                            
                            <div class="space-y-3">
                                <button class="choice-branch w-full p-4 rounded-lg text-left">
                                    <h3 class="font-semibold">🔍 深入调查神秘事件</h3>
                                    <p class="text-sm mt-1 opacity-80">探索图书馆深处的秘密，可能发现隐藏剧情</p>
                                </button>
                                
                                <button class="choice-branch w-full p-4 rounded-lg text-left">
                                    <h3 class="font-semibold">👥 寻求朋友帮助</h3>
                                    <p class="text-sm mt-1 opacity-80">与Aiko一起行动，提升羁绊值</p>
                                </button>
                                
                                <button class="choice-branch w-full p-4 rounded-lg text-left">
                                    <h3 class="font-semibold">📚 专注学习任务</h3>
                                    <p class="text-sm mt-1 opacity-80">按部就班完成作业，获得额外奖励</p>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：游戏化信息面板 -->
                    <div class="space-y-6">
                        <!-- 完成奖励预览 -->
                        <div class="bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
                                <span class="material-icons-outlined text-yellow-500">emoji_events</span>
                                完成奖励
                            </h3>
                            
                            <div class="space-y-3">
                                <div class="reward-card p-3 rounded-lg">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm flex items-center gap-2">
                                            <span class="text-orange-500">🔥</span>
                                            曦光微尘
                                        </span>
                                        <span class="font-bold text-orange-600">+120</span>
                                    </div>
                                </div>
                                
                                <div class="reward-card p-3 rounded-lg">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm flex items-center gap-2">
                                            <span class="text-pink-500">💧</span>
                                            羁绊之露
                                        </span>
                                        <span class="font-bold text-pink-600">+50</span>
                                    </div>
                                </div>
                                
                                <div class="reward-card p-3 rounded-lg">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm flex items-center gap-2">
                                            <span class="text-purple-500">🧩</span>
                                            拼图碎片
                                        </span>
                                        <span class="font-bold text-purple-600">+3</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 可解锁成就 -->
                        <div class="bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
                                <span class="material-icons-outlined text-indigo-500">military_tech</span>
                                可解锁成就
                            </h3>
                            
                            <div class="space-y-3">
                                <div class="achievement-glow p-3 bg-gradient-to-r from-yellow-100 to-amber-100 rounded-lg border border-yellow-300">
                                    <div class="flex items-center gap-3">
                                        <span class="text-2xl">🎓</span>
                                        <div>
                                            <p class="font-semibold text-yellow-700">学院新星</p>
                                            <p class="text-xs text-yellow-600">完成校园奇遇故事线</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="p-3 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-lg border border-blue-300">
                                    <div class="flex items-center gap-3">
                                        <span class="text-2xl">🔍</span>
                                        <div>
                                            <p class="font-semibold text-blue-700">真相探索者</p>
                                            <p class="text-xs text-blue-600">发现所有隐藏线索</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="p-3 bg-gradient-to-r from-pink-100 to-rose-100 rounded-lg border border-pink-300">
                                    <div class="flex items-center gap-3">
                                        <span class="text-2xl">💕</span>
                                        <div>
                                            <p class="font-semibold text-pink-700">最佳伙伴</p>
                                            <p class="text-xs text-pink-600">与Aiko达到最高羁绊</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 参与角色 -->
                        <div class="bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold text-slate-900 mb-4">参与角色</h3>
                            
                            <div class="space-y-3">
                                <div class="character-card flex items-center gap-3 p-3 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200 hover:shadow-md transition-all">
                                    <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuAOo6oSlAOPgjixfL9uZtkVvrUuw85OvQBaYqRpL0WqReyV0ifl5CEgfNF9ifNMLsM4TIZtwTo6pWrlOOnjNPcx5iYh6UiwTNzwwFPL4EyOQZnc0TnEn7CiQ0u5fMXp3NDbyTHdwldWgZ7IePBfTxPc7Taizxc9ZbMHy3wv0inbJ_vxXWeGRCigsswpy7XY1lKCFuVyaYIClebuRATn4Igfcvgb2fFs7sgJPFrMfksukzLHRtocEyk_HOeqKTNLKiddXCjMNlCTjo07" alt="Aiko" class="h-12 w-12 rounded-full border-2 border-pink-300">
                                    <div class="flex-1">
                                        <a href="4-PC-角色详情.html" class="font-semibold text-pink-600 hover:underline">Aiko</a>
                                        <p class="text-xs text-pink-500">主角，活泼开朗</p>
                                        <div class="flex items-center gap-1 mt-1">
                                            <span class="text-xs bg-pink-100 text-pink-700 px-2 py-1 rounded-full">羁绊 Lv.5</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="character-card flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 hover:shadow-md transition-all">
                                    <div class="h-12 w-12 rounded-full bg-blue-200 flex items-center justify-center text-blue-600">
                                        <span class="material-icons-outlined">person</span>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-semibold text-blue-600">神秘学长</p>
                                        <p class="text-xs text-blue-500">故事关键人物</p>
                                        <div class="flex items-center gap-1 mt-1">
                                            <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">未解锁</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 解锁条件 -->
                        <div class="bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold text-slate-900 mb-4">解锁条件</h3>
                            
                            <div class="space-y-3 text-sm">
                                <div class="flex items-center gap-2">
                                    <span class="material-icons-outlined text-green-500 text-lg">check_circle</span>
                                    <span class="text-slate-700">与Aiko羁绊等级达到3级</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="material-icons-outlined text-green-500 text-lg">check_circle</span>
                                    <span class="text-slate-700">完成新手引导</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="material-icons-outlined text-gray-400 text-lg">radio_button_unchecked</span>
                                    <span class="text-slate-500">拥有月卡特权（推荐）</span>
                                </div>
                            </div>
                        </div>

                        <!-- 开始游戏按钮 -->
                        <div class="bg-gradient-to-r from-sky-500 to-indigo-600 p-6 rounded-xl shadow-lg text-white">
                            <h3 class="text-lg font-semibold mb-2">准备开始冒险？</h3>
                            <p class="text-sky-100 text-sm mb-4">
                                继续Aiko的校园故事，探索未知的奇妙世界！
                            </p>
                            <button onclick="window.location.href='3-PC-聊天页.html'" class="w-full bg-white text-sky-600 font-semibold py-3 px-6 rounded-lg hover:bg-sky-50 transition-colors shadow-md">
                                继续故事
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 章节节点交互
        document.querySelectorAll('.chapter-node:not(.locked)').forEach(node => {
            node.addEventListener('click', function() {
                if (!this.classList.contains('locked')) {
                    console.log('进入章节:', this.querySelector('h3').textContent);
                }
            });
        });

        // 选择分支交互
        document.querySelectorAll('.choice-branch').forEach(choice => {
            choice.addEventListener('click', function() {
                // 移除其他选择的选中状态
                document.querySelectorAll('.choice-branch').forEach(c => c.classList.remove('selected'));
                // 添加选中状态
                this.classList.add('selected');
                console.log('选择:', this.querySelector('h3').textContent);
            });
        });

        // 角色卡片点击
        document.querySelectorAll('.character-card').forEach(card => {
            card.addEventListener('click', function() {
                const characterName = this.querySelector('a, p').textContent;
                console.log('查看角色:', characterName);
            });
        });

        // 奖励卡片动画
        document.querySelectorAll('.reward-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html> 