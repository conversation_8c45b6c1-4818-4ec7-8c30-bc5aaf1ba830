<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建/编辑角色 - Alphane.ai</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #ec4899;
            --warm: #f59e0b;
            --success: #10b981;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #fef7ff 0%, #f9fafb 30%, #fef2f2 70%, #fef7ff 100%);
            min-height: 100vh;
            position: relative;
        }

        /* 动态背景效果 */
        .bg-animated {
            background: linear-gradient(-45deg, #fef7ff, #f9fafb, #fef2f2, #ffffff);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 毛玻璃效果 */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.6);
        }

        /* 浮动动画 */
        .float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 卡片悬浮效果 */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        /* 代币发光效果 */
        .token-glow {
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 10px rgba(245, 158, 11, 0.5); }
            to { box-shadow: 0 0 20px rgba(245, 158, 11, 0.8), 0 0 30px rgba(245, 158, 11, 0.4); }
        }

        /* 按钮发光效果 */
        .btn-glow {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            transition: all 0.3s ease;
        }

        .btn-glow:hover {
            box-shadow: 0 12px 32px rgba(99, 102, 241, 0.6);
            transform: translateY(-2px);
        }

        .btn-glow::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-glow:hover::before {
            left: 100%;
        }

        /* Tab按钮美化 */
        .tab-button {
            @apply border-transparent text-slate-600 hover:text-sky-600 hover:border-slate-300;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .tab-button.active {
            @apply border-sky-500 text-sky-600 font-semibold;
            background: linear-gradient(135deg, 
                rgba(56, 189, 248, 0.1), 
                rgba(99, 102, 241, 0.15),
                rgba(139, 92, 246, 0.1)
            );
            box-shadow: 
                0 8px 32px rgba(56, 189, 248, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(56, 189, 248, 0.3), 
                transparent
            );
            transition: left 0.5s;
        }

        .tab-button:hover::before {
            left: 100%;
        }

        .tab-content { 
            display: none; 
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }
        .tab-content.active { 
            display: block; 
            opacity: 1;
            transform: translateY(0);
        }

        .form-section-title { @apply text-base font-semibold text-slate-700 mb-1; }
        .form-section-desc { @apply text-xs text-slate-500 mb-3; }
        .ai-tool-button { 
            @apply mt-1.5 text-xs text-sky-600 hover:text-sky-700 hover:underline flex items-center gap-1;
            transition: all 0.3s ease;
            position: relative;
        }

        .ai-tool-button:hover {
            transform: translateY(-1px);
        }

        .input-group { @apply space-y-1.5; }

        /* 表单元素美化 */
        .form-input, .form-select, .form-textarea {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 
                inset 0 2px 8px rgba(0, 0, 0, 0.05),
                0 4px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(99, 102, 241, 0.5);
            box-shadow: 
                inset 0 2px 8px rgba(0, 0, 0, 0.05),
                0 8px 24px rgba(99, 102, 241, 0.15),
                0 0 0 3px rgba(99, 102, 241, 0.1);
            transform: translateY(-2px);
        }

        /* 头像预览美化 */
        .avatar-preview {
            position: relative;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .avatar-preview::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ffecd2, #fcb69f, #ffecd2);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .avatar-preview:hover::before {
            opacity: 0.6;
            animation: avatarGlow 2s ease-in-out infinite alternate;
        }

        @keyframes avatarGlow {
            from { transform: scale(1); opacity: 0.6; }
            to { transform: scale(1.1); opacity: 0.3; }
        }

        /* 拖拽区域美化 */
        .drop-zone {
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(15px);
            border: 2px dashed rgba(56, 189, 248, 0.3);
        }

        .drop-zone:hover {
            background: rgba(56, 189, 248, 0.05);
            border-color: rgba(56, 189, 248, 0.5);
            transform: scale(1.02);
        }

        /* AI助手美化 */
        .ai-assistant-card {
            background: linear-gradient(135deg, 
                rgba(139, 92, 246, 0.1), 
                rgba(236, 72, 153, 0.08),
                rgba(59, 130, 246, 0.1)
            );
            border: 1px solid rgba(139, 92, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .ai-assistant-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(139, 92, 246, 0.15), 
                transparent
            );
            transition: left 0.8s ease;
        }

        .ai-assistant-card:hover::before {
            left: 100%;
        }

        /* 预览区域美化 */
        .preview-area {
            background: linear-gradient(135deg, 
                rgba(248, 250, 252, 0.9), 
                rgba(241, 245, 249, 0.85),
                rgba(226, 232, 240, 0.8)
            );
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 
                0 12px 40px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 成功指示器 */
        .success-indicator {
            background: linear-gradient(135deg, #10b981, #059669);
            animation: successPulse 2s ease-in-out infinite;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .glass-strong {
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body class="text-slate-800 bg-animated">
    <!-- 动态背景粒子 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
        <div class="absolute top-3/4 right-1/4 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-1/4 left-1/3 w-72 h-72 bg-rose-200 rounded-full mix-blend-multiply filter blur-3xl opacity-8 animate-pulse animation-delay-4000"></div>
        <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-violet-100 rounded-full mix-blend-multiply filter blur-3xl opacity-8 animate-pulse animation-delay-6000"></div>
    </div>

    <div class="relative min-h-screen">
        <header class="glass-strong sticky top-0 z-50 shadow-2xl">
            <div class="max-w-7xl mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-full transition-all duration-300 transform hover:scale-110">
                            <span class="material-icons-outlined text-slate-700">arrow_back</span>
                        </button>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 text-indigo-600">
                                <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M4 4H17.3334V17.3334H30.6666V30.6666H44V44H4V4Z" fill="currentColor"></path>
                                </svg>
                            </div>
                            <h1 class="text-xl font-bold gradient-text">Alphane.ai</h1>
                        </div>
                        <div class="hidden sm:block h-6 w-px bg-slate-300 mx-2"></div>
                        <h2 class="text-xl font-semibold tracking-tight text-slate-800">创建新角色</h2>
                    </div>
                    
                    <!-- 代币展示 -->
                    <div class="flex items-center gap-3">
                        <div class="glass flex items-center gap-2 px-4 py-2 rounded-full token-glow">
                            <span class="text-orange-500 text-lg">🔥</span>
                            <span class="text-sm font-bold text-slate-700">1,247</span>
                        </div>
                        <div class="glass flex items-center gap-2 px-4 py-2 rounded-full">
                            <span class="text-blue-500 text-lg">💎</span>
                            <span class="text-sm font-bold text-slate-700">89</span>
                        </div>
                        <div class="flex gap-2">
                            <button class="glass flex items-center gap-1.5 px-4 py-2 text-sm font-medium text-sky-600 rounded-lg hover:bg-white/30 transition-all duration-300">
                                <span class="material-icons-outlined text-base">auto_fix_high</span> AI润色
                            </button>
                            <button class="btn-glow flex items-center gap-1.5 px-6 py-2 text-sm font-semibold text-white rounded-lg">
                                <span class="material-icons-outlined text-base">cloud_upload</span> 发布角色
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <main class="max-w-7xl mx-auto px-6 py-8 relative z-10">
            <div class="grid grid-cols-1 gap-x-8 gap-y-6 lg:grid-cols-3">
                <div class="lg:col-span-2">
                    <div class="mb-5 border-b border-white/30 glass-strong shadow-2xl rounded-t-3xl">
                        <nav class="-mb-px flex space-x-0.5 sm:space-x-2 px-6 pt-2" aria-label="Tabs">
                            <button onclick="showTab('coreIdentity')" class="tab-button active flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-sm rounded-t-2xl font-medium">✨ 核心身份</button>
                            <button onclick="showTab('personalityBehavior')" class="tab-button flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-sm rounded-t-2xl font-medium">🎭 性格行为</button>
                            <button onclick="showTab('dialogueMemory')" class="tab-button flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-sm rounded-t-2xl font-medium">💭 对话记忆</button>
                            <button onclick="showTab('advancedTuning')" class="tab-button flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-sm rounded-t-2xl font-medium">⚙️ 高级调校</button>
                            <button onclick="showTab('publishShare')" class="tab-button flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-sm rounded-t-2xl font-medium">🚀 发布分享</button>
                        </nav>
                    </div>

                    <form id="characterForm" class="space-y-8 glass-strong p-8 shadow-2xl rounded-b-3xl card-hover">
                        <!-- Tab: Core Identity -->
                        <div id="coreIdentityTab" class="tab-content active space-y-8">
                            <div class="glass p-6 rounded-2xl shadow-lg">
                                <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                    <span class="material-icons-outlined text-indigo-500">account_circle</span>
                                    角色基础信息
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="input-group">
                                        <label for="char_name" class="block text-sm font-semibold text-slate-700 mb-2">角色名称*</label>
                                        <input type="text" id="char_name" name="char_name" required class="form-input mt-1 block w-full rounded-xl border-slate-300 shadow-sm focus:border-sky-500 focus:ring-sky-500" placeholder="例如：艾拉">
                                    </div>
                                    <div class="input-group">
                                        <label for="char_faction" class="block text-sm font-semibold text-slate-700 mb-2">派系*</label>
                                        <select id="char_faction" name="char_faction" required class="form-select mt-1 block w-full rounded-xl border-slate-300 shadow-sm focus:border-sky-500 focus:ring-sky-500">
                                            <option value="anime">🌸 动漫系</option>
                                            <option value="realistic">🌍 真实系</option>
                                            <option value="fantasy">✨ 奇幻系</option>
                                            <option value="scifi">🚀 科幻系</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="input-group">
                                    <label for="char_description" class="block text-sm font-semibold text-slate-700 mb-2">一句话简介*</label>
                                    <input type="text" id="char_description" name="char_description" required class="form-input mt-1 block w-full rounded-xl border-slate-300 shadow-sm focus:border-sky-500 focus:ring-sky-500" placeholder="用一句话概括你的角色">
                                    <button type="button" class="ai-tool-button mt-2"><span class="material-icons-outlined text-sm">auto_awesome</span>AI辅助生成简介</button>
                                </div>
                            </div>

                            <div class="glass p-6 rounded-2xl shadow-lg">
                                <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                    <span class="material-icons-outlined text-pink-500">face</span>
                                    角色形象
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                                    <div class="input-group">
                                        <label class="block text-sm font-semibold text-slate-700 mb-3">角色头像</label>
                                        <div class="flex items-center justify-center">
                                            <div class="avatar-preview">
                                                <img id="avatar_preview_img" src="https://via.placeholder.com/120/E0E7FF/4F46E5?text=AI" alt="Avatar Preview" class="h-32 w-32 rounded-full object-cover border-4 border-white shadow-2xl">
                                            </div>
                                        </div>
                                        <input type="file" id="char_avatar" name="char_avatar" accept="image/*" class="mt-4 text-sm text-slate-500 file:mr-4 file:py-3 file:px-6 file:rounded-xl file:border-0 file:text-sm file:font-semibold file:bg-gradient-to-r file:from-sky-50 file:to-indigo-50 file:text-sky-700 hover:file:from-sky-100 hover:file:to-indigo-100 cursor-pointer w-full"/>
                                    </div>
                                    
                                    <div class="input-group">
                                        <label class="block text-sm font-semibold text-slate-700 mb-3">角色立绘</label>
                                        <div class="drop-zone rounded-2xl px-6 py-8">
                                            <div class="text-center">
                                                <img id="cover_image_preview_img" src="https://via.placeholder.com/300x400/E0E7FF/4F46E5?text=Cover" alt="Cover Preview" class="mx-auto h-32 object-contain mb-2 hidden rounded-lg">
                                                <svg id="cover_image_placeholder_icon" class="mx-auto h-12 w-12 text-slate-400" stroke="currentColor" fill="none" viewBox="0 0 48 48"><path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" /></svg>
                                                <div class="flex text-sm text-slate-600 justify-center mt-2">
                                                    <label for="char_image" class="relative cursor-pointer rounded-md font-medium text-sky-600 hover:text-sky-500">
                                                        <span>上传立绘</span>
                                                        <input id="char_image" name="char_image" type="file" class="sr-only" accept="image/*">
                                                    </label>
                                                    <p class="pl-1">或拖拽到此处</p>
                                                </div>
                                                <p class="text-xs text-slate-500 mt-1">推荐比例 3:4, PNG, JPG, WEBP up to 10MB</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab: Personality & Behavior -->
                        <div id="personalityBehaviorTab" class="tab-content space-y-8">
                            <div class="glass p-6 rounded-2xl shadow-lg">
                                <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                    <span class="material-icons-outlined text-purple-500">psychology</span>
                                    性格体系设计
                                </h3>
                                
                                <div class="space-y-6">
                                    <div>
                                        <h4 class="form-section-title">核心性格标签</h4>
                                        <p class="form-section-desc">选择或添加标签来定义角色的主要性格特征</p>
                                        
                                        <div class="grid grid-cols-2 sm:grid-cols-3 gap-3">
                                            <select class="form-select rounded-xl border-slate-300 text-sm"><option>🌟 主动/被动</option><option>主动</option><option>被动</option></select>
                                            <select class="form-select rounded-xl border-slate-300 text-sm"><option>☀️ 明亮/阴暗</option><option>明亮</option><option>阴暗</option></select>
                                            <select class="form-select rounded-xl border-slate-300 text-sm"><option>💬 话多/话少</option><option>话多</option><option>话少</option></select>
                                        </div>
                                        
                                        <div class="mt-4">
                                            <input type="text" id="char_custom_tags" class="form-input block w-full rounded-xl border-slate-300 shadow-sm" placeholder="自定义标签: 傲娇, 吃货, 天然呆 (逗号分隔)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab: Dialogue & Memory -->
                        <div id="dialogueMemoryTab" class="tab-content space-y-8">
                            <div class="glass p-6 rounded-2xl shadow-lg">
                                <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                    <span class="material-icons-outlined text-green-500">chat</span>
                                    对话系统设置
                                </h3>
                                
                                <div class="space-y-6">
                                    <div class="input-group">
                                        <label for="char_background_story" class="block text-sm font-semibold text-slate-700 mb-2">背景故事*</label>
                                        <textarea id="char_background_story" name="char_background_story" rows="5" required class="form-textarea block w-full rounded-xl border-slate-300 shadow-sm" placeholder="详细描述角色的背景故事，预埋可互动的"伏笔""></textarea>
                                        <button type="button" class="ai-tool-button mt-2"><span class="material-icons-outlined text-sm">auto_awesome</span>AI辅助生成背景故事</button>
                                    </div>
                                    
                                    <div class="input-group">
                                        <label for="char_greeting_message" class="block text-sm font-semibold text-slate-700 mb-2">问候语*</label>
                                        <textarea id="char_greeting_message" name="char_greeting_message" rows="3" required class="form-textarea block w-full rounded-xl border-slate-300 shadow-sm" placeholder="用户初次与角色聊天时收到的消息"></textarea>
                                        <button type="button" class="ai-tool-button mt-2"><span class="material-icons-outlined text-sm">auto_awesome</span>AI辅助生成问候语</button>
                                    </div>
                                    
                                    <div class="input-group">
                                        <label for="char_example_dialogues" class="block text-sm font-semibold text-slate-700 mb-2">对话示例*</label>
                                        <textarea id="char_example_dialogues" name="char_example_dialogues" rows="8" required class="form-textarea block w-full rounded-xl border-slate-300 shadow-sm" placeholder="提供多组高质量示例对话，AI将学习其风格。
User: 你喜欢什么？
{{char}}: 我喜欢星空和一切神秘的事物。

User: 今天心情怎么样？
{{char}}: 还不错，你呢？有什么开心的事分享吗？"></textarea>
                                        <p class="mt-2 text-xs text-slate-500">使用 `{{user}}` 和 `{{char}}`。提供3-5组高质量示例效果更佳。</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab: Advanced Tuning -->
                        <div id="advancedTuningTab" class="tab-content space-y-8">
                            <div class="glass p-6 rounded-2xl shadow-lg">
                                <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                    <span class="material-icons-outlined text-orange-500">tune</span>
                                    高级参数调校
                                </h3>
                                
                                <div class="input-group">
                                    <label for="char_prompt_prefix_adv" class="block text-sm font-semibold text-slate-700 mb-2">自定义 Prompt 前缀</label>
                                    <textarea id="char_prompt_prefix_adv" name="char_prompt_prefix_adv" rows="4" class="form-textarea block w-full rounded-xl border-slate-300 shadow-sm" placeholder="为高级用户提供，用于精细调整AI行为"></textarea>
                                    <p class="mt-2 text-xs text-slate-500">此内容将直接添加到AI模型输入的最前端</p>
                                </div>
                            </div>
                        </div>

                        <!-- Tab: Publish & Share -->
                        <div id="publishShareTab" class="tab-content space-y-8">
                            <div class="glass p-6 rounded-2xl shadow-lg">
                                <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                    <span class="material-icons-outlined text-green-500">public</span>
                                    发布设置
                                </h3>
                                
                                <div class="space-y-6">
                                    <div class="input-group">
                                        <label for="char_visibility_publish" class="block text-sm font-semibold text-slate-700 mb-2">可见性设置</label>
                                        <select id="char_visibility_publish" name="char_visibility_publish" class="form-select block w-full rounded-xl border-slate-300 shadow-sm">
                                            <option value="public">🌍 公开 (所有人可见，将在角色广场展示)</option>
                                            <option value="unlisted">🔗 不公开 (拥有链接的人可见)</option>
                                            <option value="private">🔒 私密 (仅自己可见)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="flex items-center pt-4">
                                        <input id="char_set_as_template_publish" name="char_set_as_template_publish" type="checkbox" class="form-checkbox h-5 w-5 text-sky-600 border-slate-300 rounded focus:ring-sky-500">
                                        <label for="char_set_as_template_publish" class="ml-3 text-sm text-slate-700 font-medium">将此角色设为创作模板</label>
                                    </div>
                                    
                                    <div class="border-t border-white/20 pt-6 flex justify-end gap-4">
                                        <button type="button" class="glass px-6 py-3 text-sm font-medium text-slate-700 rounded-xl hover:bg-white/30 transition-all duration-300 transform hover:scale-105">
                                            📝 保存草稿
                                        </button>
                                        <button type="submit" class="btn-glow px-8 py-3 text-sm font-semibold text-white rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2">
                                            <span class="material-icons-outlined text-base">cloud_upload</span>发布角色
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="lg:col-span-1 space-y-6">
                    <div class="ai-assistant-card glass-strong p-6 shadow-2xl rounded-3xl card-hover float">
                        <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                            <span class="material-icons-outlined text-purple-500">auto_fix_high</span>
                            AI 辅助创作
                        </h3>
                         <div class="space-y-3">
                            <button type="button" class="w-full flex items-center justify-center gap-3 rounded-xl glass px-4 py-3 text-sm font-medium text-sky-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                                <span class="material-icons-outlined text-lg">auto_fix_high</span> 
                                <span>Meta Character (快速构建)</span>
                            </button>
                            <button type="button" class="w-full flex items-center justify-center gap-3 rounded-xl glass px-4 py-3 text-sm font-medium text-purple-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                                <span class="material-icons-outlined text-lg">movie_filter</span> 
                                <span>Story Agent (剧情辅助)</span>
                            </button>
                            <button type="button" class="w-full flex items-center justify-center gap-3 rounded-xl glass px-4 py-3 text-sm font-medium text-slate-600 transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                                <span class="material-icons-outlined text-lg">library_books</span> 
                                <span>从模板创建</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="glass-strong p-6 shadow-2xl rounded-3xl card-hover">
                        <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                            <span class="material-icons-outlined text-green-500">visibility</span>
                            实时预览
                        </h3>
                        <div class="preview-area rounded-2xl p-4 min-h-[300px] space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="avatar-preview">
                                    <img id="preview_avatar" src="https://via.placeholder.com/48/E0E7FF/4F46E5?text=AI" alt="Preview Avatar" class="h-12 w-12 rounded-full object-cover border-2 border-white shadow-lg">
                                </div>
                                <div class="flex-1">
                                    <p id="preview_name" class="font-bold text-slate-700 text-lg">[角色名称]</p>
                                    <div class="glass p-3 rounded-2xl mt-2 shadow-sm">
                                        <p id="preview_greeting" class="text-sm text-slate-600">[问候语预览...]</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-xs text-slate-400 text-center py-2 border-t border-dashed border-slate-300">--- 输入测试对话 ---</div>
                            
                            <input type="text" id="preview_input" class="form-input w-full rounded-xl text-sm py-3" placeholder="你：你好呀！">
                            
                            <div class="flex items-start space-x-3">
                                <div class="avatar-preview">
                                    <img id="preview_avatar_reply" src="https://via.placeholder.com/48/E0E7FF/4F46E5?text=AI" alt="Preview Avatar" class="h-12 w-12 rounded-full object-cover border-2 border-white shadow-lg">
                                </div>
                                <div class="glass p-3 rounded-2xl shadow-sm flex-1">
                                    <p id="preview_reply" class="text-sm text-slate-600">[AI回复预览...]</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.id === tabName + 'Tab' ? tab.classList.add('active') : tab.classList.remove('active'));
            document.querySelectorAll('.tab-button').forEach(btn => btn.getAttribute('onclick').includes(tabName) ? btn.classList.add('active') : btn.classList.remove('active'));
        }

        document.addEventListener('DOMContentLoaded', () => {
            showTab('coreIdentity');
            
            const avatarInput = document.getElementById('char_avatar');
            const avatarPreview = document.getElementById('avatar_preview_img');
            const previewAvatar = document.getElementById('preview_avatar');
            const previewAvatarReply = document.getElementById('preview_avatar_reply');

            avatarInput.addEventListener('change', function(event) {
                if (event.target.files && event.target.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        avatarPreview.src = e.target.result;
                        previewAvatar.src = e.target.result;
                        previewAvatarReply.src = e.target.result;
                    }
                    reader.readAsDataURL(event.target.files[0]);
                }
            });

            const coverInput = document.getElementById('char_image');
            const coverPreview = document.getElementById('cover_image_preview_img');
            const coverPlaceholder = document.getElementById('cover_image_placeholder_icon');
             coverInput.addEventListener('change', function(event) {
                if (event.target.files && event.target.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        coverPreview.src = e.target.result;
                        coverPreview.classList.remove('hidden');
                        coverPlaceholder.classList.add('hidden');
                    }
                    reader.readAsDataURL(event.target.files[0]);
                } else {
                    coverPreview.classList.add('hidden');
                    coverPlaceholder.classList.remove('hidden');
                }
            });
            
            document.getElementById('char_name').addEventListener('input', e => document.getElementById('preview_name').textContent = e.target.value || '[角色名称]');
            document.getElementById('char_greeting_message').addEventListener('input', e => document.getElementById('preview_greeting').textContent = e.target.value || '[问候语预览...]');
            
            // 延迟显示动画
            setTimeout(() => {
                document.querySelectorAll('.card-hover').forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s ease-out';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 100);
        });
    </script>
</body>
</html>