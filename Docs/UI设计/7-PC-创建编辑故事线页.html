<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建/编辑故事线 - Alphane.ai</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #ec4899;
            --warm: #f59e0b;
            --success: #10b981;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #fef7ff 0%, #f9fafb 30%, #fef2f2 70%, #fef7ff 100%);
            min-height: 100vh;
            position: relative;
        }

        /* 动态背景效果 */
        .bg-animated {
            background: linear-gradient(-45deg, #fef7ff, #f9fafb, #fef2f2, #ffffff);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 毛玻璃效果 */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.6);
        }

        /* 故事进度条动画 */
        .story-progress {
            background: linear-gradient(90deg, #10b981 0%, #10b981 45%, #e5e7eb 45%, #e5e7eb 100%);
            position: relative;
            overflow: hidden;
        }

        .story-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 45%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(220%); }
        }

        /* 浮动动画 */
        .float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 卡片悬浮效果 */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        /* 代币发光效果 */
        .token-glow {
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 10px rgba(245, 158, 11, 0.5); }
            to { box-shadow: 0 0 20px rgba(245, 158, 11, 0.8), 0 0 30px rgba(245, 158, 11, 0.4); }
        }

        /* 按钮发光效果 */
        .btn-glow {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            transition: all 0.3s ease;
        }

        .btn-glow:hover {
            box-shadow: 0 12px 32px rgba(99, 102, 241, 0.6);
            transform: translateY(-2px);
        }

        .btn-glow::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-glow:hover::before {
            left: 100%;
        }

        /* 表单元素美化 */
        .form-input, .form-select, .form-textarea {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 
                inset 0 2px 8px rgba(0, 0, 0, 0.05),
                0 4px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(99, 102, 241, 0.5);
            box-shadow: 
                inset 0 2px 8px rgba(0, 0, 0, 0.05),
                0 8px 24px rgba(99, 102, 241, 0.15),
                0 0 0 3px rgba(99, 102, 241, 0.1);
            transform: translateY(-2px);
        }

        /* Story Agent 美化 */
        .story-agent-card {
            background: linear-gradient(135deg, 
                rgba(139, 92, 246, 0.1), 
                rgba(236, 72, 153, 0.08),
                rgba(59, 130, 246, 0.1)
            );
            border: 1px solid rgba(139, 92, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .story-agent-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(139, 92, 246, 0.15), 
                transparent
            );
            transition: left 0.8s ease;
        }

        .story-agent-card:hover::before {
            left: 100%;
        }

        /* AI生成建议美化 */
        .ai-suggestion {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(139, 92, 246, 0.2);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .ai-suggestion:hover {
            transform: translateY(-2px) scale(1.02);
            background: rgba(139, 92, 246, 0.05);
            box-shadow: 
                0 10px 30px rgba(139, 92, 246, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .ai-suggestion::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(139, 92, 246, 0.1) 50%, 
                transparent 100%
            );
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .ai-suggestion:hover::before {
            transform: translateX(100%);
        }

        /* 预览区域美化 */
        .preview-area {
            background: linear-gradient(135deg, 
                rgba(248, 250, 252, 0.9), 
                rgba(241, 245, 249, 0.85),
                rgba(226, 232, 240, 0.8)
            );
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 
                0 12px 40px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 故事标签美化 */
        .story-tag {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            border: 1px solid rgba(99, 102, 241, 0.2);
            transition: all 0.3s ease;
        }

        .story-tag:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
            transform: translateY(-1px);
        }

        .form-section-title { @apply text-base font-semibold text-slate-700 mb-1; }
        .form-section-desc { @apply text-xs text-slate-500 mb-3; }
        .ai-tool-button { 
            @apply mt-1.5 text-xs text-sky-600 hover:text-sky-700 hover:underline flex items-center gap-1;
            transition: all 0.3s ease;
        }

        .ai-tool-button:hover {
            transform: translateY(-1px);
        }

        .input-group { @apply space-y-1.5 mb-5; }

        /* 成功动画 */
        .success-pulse {
            animation: successPulse 2s ease-in-out infinite;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .glass-strong {
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body class="text-slate-800 bg-animated">
    <!-- 动态背景粒子 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
        <div class="absolute top-3/4 right-1/4 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-1/4 left-1/3 w-72 h-72 bg-rose-200 rounded-full mix-blend-multiply filter blur-3xl opacity-8 animate-pulse animation-delay-4000"></div>
        <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-violet-100 rounded-full mix-blend-multiply filter blur-3xl opacity-8 animate-pulse animation-delay-6000"></div>
    </div>

    <div class="relative min-h-screen">
        <header class="glass-strong sticky top-0 z-50 shadow-2xl">
            <div class="max-w-7xl mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-full transition-all duration-300 transform hover:scale-110">
                            <span class="material-icons-outlined text-slate-700">arrow_back</span>
                        </button>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 text-indigo-600">
                                <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path>
                                </svg>
                            </div>
                            <h1 class="text-xl font-bold gradient-text">Alphane.ai</h1>
                        </div>
                        <div class="hidden sm:block h-6 w-px bg-slate-300 mx-2"></div>
                        <h2 class="text-xl font-semibold tracking-tight text-slate-800">创建新故事线</h2>
                    </div>
                    
                    <!-- 代币展示 -->
                    <div class="flex items-center gap-3">
                        <div class="glass flex items-center gap-2 px-4 py-2 rounded-full token-glow">
                            <span class="text-orange-500 text-lg">🔥</span>
                            <span class="text-sm font-bold text-slate-700">1,247</span>
                        </div>
                        <div class="glass flex items-center gap-2 px-4 py-2 rounded-full">
                            <span class="text-blue-500 text-lg">💎</span>
                            <span class="text-sm font-bold text-slate-700">89</span>
                        </div>
                        <div class="flex gap-2">
                            <button class="glass flex items-center gap-1.5 px-4 py-2 text-sm font-medium text-slate-700 rounded-lg hover:bg-white/30 transition-all duration-300">
                                📝 保存草稿
                            </button>
                            <button class="btn-glow flex items-center gap-1.5 px-6 py-2 text-sm font-semibold text-white rounded-lg">
                                <span class="material-icons-outlined text-base">cloud_upload</span> 发布故事线
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <main class="max-w-7xl mx-auto px-6 py-8 relative z-10">
            <div class="grid grid-cols-1 gap-x-8 gap-y-6 lg:grid-cols-3">
                <!-- Left Column: Form -->
                <div class="lg:col-span-2 glass-strong p-8 shadow-2xl rounded-3xl card-hover">
                    <form id="storyForm" class="space-y-8">
                        <div class="glass p-6 rounded-2xl shadow-lg">
                            <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                <span class="material-icons-outlined text-purple-500">auto_stories</span>
                                故事线核心设定
                            </h3>
                            <p class="text-sm text-slate-500 mb-6">为你的故事线注入灵魂，打造引人入胜的开端。</p>

                            <div class="space-y-6">
                                <div class="input-group">
                                    <label for="story_title" class="block text-sm font-semibold text-slate-700 mb-2">故事线标题*</label>
                                    <div class="flex items-center gap-2">
                                        <input type="text" id="story_title" name="story_title" required class="form-input flex-grow block w-full rounded-xl shadow-sm focus:border-sky-500 focus:ring-sky-500" placeholder="例如：神秘古堡的秘密">
                                        <button type="button" class="ai-tool-button px-3 py-2 bg-sky-50 rounded-lg hover:bg-sky-100"><span class="material-icons-outlined text-base">auto_fix_high</span></button>
                                    </div>
                                </div>

                                <div class="input-group">
                                    <label for="story_description" class="block text-sm font-semibold text-slate-700 mb-2">故事线描述*</label>
                                    <textarea id="story_description" name="story_description" rows="4" required class="form-textarea block w-full rounded-xl shadow-sm focus:border-sky-500 focus:ring-sky-500" placeholder="简要介绍故事线的背景、主要情节和特色。"></textarea>
                                    <button type="button" class="ai-tool-button mt-2"><span class="material-icons-outlined text-sm">auto_fix_high</span>AI辅助</button>
                                </div>

                                <div class="input-group">
                                    <label for="story_opening" class="block text-sm font-semibold text-slate-700 mb-2">开场白*</label>
                                    <textarea id="story_opening" name="story_opening" rows="3" required class="form-textarea block w-full rounded-xl shadow-sm focus:border-sky-500 focus:ring-sky-500" placeholder="用户开始此故事线时看到的第一段引导性文字。"></textarea>
                                    <button type="button" class="ai-tool-button mt-2"><span class="material-icons-outlined text-sm">auto_fix_high</span>AI辅助</button>
                                </div>

                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                    <div class="input-group">
                                        <label for="story_associated_character" class="block text-sm font-semibold text-slate-700 mb-2">关联角色*</label>
                                        <select id="story_associated_character" name="story_associated_character" required class="form-select block w-full rounded-xl shadow-sm focus:border-sky-500 focus:ring-sky-500">
                                            <option value="">-- 选择一个角色 --</option>
                                            <option value="aiko">🌸 Aiko (元气少女)</option>
                                            <option value="ethan">🗡️ Ethan (探险家)</option>
                                            <option value="luna">🌙 Luna (神秘术士)</option>
                                        </select>
                                        <p class="mt-1 text-xs text-slate-500">选择此故事线的主要参与角色</p>
                                    </div>
                                    <div class="input-group">
                                        <label for="story_tags" class="block text-sm font-semibold text-slate-700 mb-2">故事线标签</label>
                                        <input type="text" id="story_tags" name="story_tags" class="form-input block w-full rounded-xl shadow-sm focus:border-sky-500 focus:ring-sky-500" placeholder="冒险, 解谜, 校园 (逗号分隔)">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="glass p-6 rounded-2xl shadow-lg">
                            <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                <span class="material-icons-outlined text-pink-500">image</span>
                                视觉设计
                            </h3>
                            
                            <div class="input-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-3">故事线封面图片</label>
                                <div class="flex items-center gap-6">
                                    <img id="cover_image_preview" src="https://via.placeholder.com/150x200/E0E7FF/4F46E5?text=故事封面" alt="封面预览" class="h-40 w-32 object-cover rounded-2xl border-4 border-white shadow-2xl">
                                    <div class="flex-1">
                                        <input type="file" id="story_cover_image" name="story_cover_image" accept="image/*" class="text-sm text-slate-500 file:mr-4 file:py-3 file:px-6 file:rounded-xl file:border-0 file:text-sm file:font-semibold file:bg-gradient-to-r file:from-purple-50 file:to-pink-50 file:text-purple-700 hover:file:from-purple-100 hover:file:to-pink-100 cursor-pointer w-full"/>
                                        <p class="mt-2 text-xs text-slate-500">推荐比例 3:4, PNG, JPG, WEBP up to 5MB</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="glass p-6 rounded-2xl shadow-lg">
                            <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                <span class="material-icons-outlined text-indigo-500">settings</span>
                                高级设置
                            </h3>
                            
                            <div class="space-y-6">
                                <div class="input-group">
                                    <label for="story_user_persona" class="block text-sm font-semibold text-slate-700 mb-2">玩家人设建议</label>
                                    <textarea id="story_user_persona" name="story_user_persona" rows="2" class="form-textarea block w-full rounded-xl shadow-sm" placeholder="例如："你是一位寻找真相的侦探""></textarea>
                                </div>
                                
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                    <div class="input-group">
                                        <label for="story_bg_music_url" class="block text-sm font-semibold text-slate-700 mb-2">背景音乐URL</label>
                                        <input type="url" id="story_bg_music_url" name="story_bg_music_url" class="form-input block w-full rounded-xl shadow-sm" placeholder="https://example.com/music.mp3">
                                    </div>
                                    <div class="flex items-start pt-8">
                                        <div class="flex h-5 items-center">
                                            <input id="story_is_private" name="story_is_private" type="checkbox" class="form-checkbox h-4 w-4 rounded border-slate-300 text-sky-600 focus:ring-sky-500">
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="story_is_private" class="font-medium text-slate-700">🔒 设为私密故事线</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 新增：章节管理 -->
                        <div class="glass p-6 rounded-2xl shadow-lg">
                            <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                                <span class="material-icons-outlined text-amber-500">auto_stories</span>
                                章节管理
                            </h3>
                            <p class="text-sm text-slate-500 mb-6">为你的故事线设计章节结构，打造层次丰富的体验。</p>
                            
                            <div class="space-y-4">
                                <!-- 章节列表 -->
                                <div id="chapters_list" class="space-y-3">
                                    <div class="chapter-item glass p-4 rounded-xl border-l-4 border-blue-500">
                                        <div class="flex items-center justify-between mb-3">
                                            <h4 class="text-sm font-semibold text-slate-700 flex items-center gap-2">
                                                <span class="w-6 h-6 bg-blue-500 text-white rounded-full text-xs flex items-center justify-center">1</span>
                                                第一章
                                            </h4>
                                            <div class="flex gap-2">
                                                <button type="button" class="edit-chapter text-xs text-blue-600 hover:text-blue-700 px-2 py-1 rounded">
                                                    <span class="material-icons-outlined text-sm">edit</span>
                                                </button>
                                                <button type="button" class="delete-chapter text-xs text-red-500 hover:text-red-600 px-2 py-1 rounded">
                                                    <span class="material-icons-outlined text-sm">delete</span>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="space-y-2">
                                            <input type="text" class="chapter-title form-input w-full text-sm rounded-lg" placeholder="章节标题" value="开始的邂逅">
                                            <textarea class="chapter-description form-textarea w-full text-sm rounded-lg" rows="2" placeholder="章节描述">主角初次进入这个神秘的世界，遇到了第一个重要角色。</textarea>
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                                <select class="chapter-unlock-condition form-select text-xs rounded-lg">
                                                    <option value="none">无解锁条件</option>
                                                    <option value="previous">完成前一章节</option>
                                                    <option value="bond_level">羁绊等级要求</option>
                                                    <option value="item">收集特定物品</option>
                                                </select>
                                                <input type="text" class="chapter-unlock-value form-input text-xs rounded-lg" placeholder="解锁条件值" style="display: none;">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="chapter-item glass p-4 rounded-xl border-l-4 border-green-500">
                                        <div class="flex items-center justify-between mb-3">
                                            <h4 class="text-sm font-semibold text-slate-700 flex items-center gap-2">
                                                <span class="w-6 h-6 bg-green-500 text-white rounded-full text-xs flex items-center justify-center">2</span>
                                                第二章
                                            </h4>
                                            <div class="flex gap-2">
                                                <button type="button" class="edit-chapter text-xs text-blue-600 hover:text-blue-700 px-2 py-1 rounded">
                                                    <span class="material-icons-outlined text-sm">edit</span>
                                                </button>
                                                <button type="button" class="delete-chapter text-xs text-red-500 hover:text-red-600 px-2 py-1 rounded">
                                                    <span class="material-icons-outlined text-sm">delete</span>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="space-y-2">
                                            <input type="text" class="chapter-title form-input w-full text-sm rounded-lg" placeholder="章节标题" value="深入探索">
                                            <textarea class="chapter-description form-textarea w-full text-sm rounded-lg" rows="2" placeholder="章节描述">故事进入深入阶段，主角面临第一个重要选择。</textarea>
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                                <select class="chapter-unlock-condition form-select text-xs rounded-lg">
                                                    <option value="none">无解锁条件</option>
                                                    <option value="previous" selected>完成前一章节</option>
                                                    <option value="bond_level">羁绊等级要求</option>
                                                    <option value="item">收集特定物品</option>
                                                </select>
                                                <input type="text" class="chapter-unlock-value form-input text-xs rounded-lg" placeholder="解锁条件值" style="display: none;">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 添加新章节按钮 -->
                                <button type="button" id="add_chapter" class="w-full flex items-center justify-center gap-2 rounded-xl glass px-4 py-3 text-sm font-medium text-purple-700 hover:bg-purple-50 transition-all duration-300 border-2 border-dashed border-purple-300 hover:border-purple-400">
                                    <span class="material-icons-outlined text-base">add</span> 
                                    <span>添加新章节</span>
                                </button>
                            </div>

                            <!-- 章节选择分支设置 -->
                            <div class="mt-6 pt-6 border-t border-white/20">
                                <h4 class="text-sm font-semibold text-slate-700 mb-3 flex items-center gap-2">
                                    <span class="material-icons-outlined text-purple-500">call_split</span>
                                    关键选择分支
                                </h4>
                                <p class="text-xs text-slate-500 mb-3">为故事线添加互动选择，让用户的决定影响剧情走向。</p>
                                
                                <div id="choices_list" class="space-y-3">
                                    <div class="choice-item glass p-3 rounded-lg">
                                        <div class="flex items-center justify-between mb-2">
                                            <h5 class="text-xs font-medium text-slate-600">选择分支 1</h5>
                                            <button type="button" class="delete-choice text-xs text-red-500 hover:text-red-600">
                                                <span class="material-icons-outlined text-sm">close</span>
                                            </button>
                                        </div>
                                        <div class="space-y-2">
                                            <input type="text" class="choice-text form-input w-full text-xs rounded-lg" placeholder="选择项文本" value="🔍 深入调查神秘事件">
                                            <textarea class="choice-result form-textarea w-full text-xs rounded-lg" rows="1" placeholder="选择结果描述">探索图书馆深处的秘密，可能发现隐藏剧情</textarea>
                                            <select class="choice-chapter form-select text-xs rounded-lg">
                                                <option value="1">第一章</option>
                                                <option value="2" selected>第二章</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="choice-item glass p-3 rounded-lg">
                                        <div class="flex items-center justify-between mb-2">
                                            <h5 class="text-xs font-medium text-slate-600">选择分支 2</h5>
                                            <button type="button" class="delete-choice text-xs text-red-500 hover:text-red-600">
                                                <span class="material-icons-outlined text-sm">close</span>
                                            </button>
                                        </div>
                                        <div class="space-y-2">
                                            <input type="text" class="choice-text form-input w-full text-xs rounded-lg" placeholder="选择项文本" value="👥 寻求朋友帮助">
                                            <textarea class="choice-result form-textarea w-full text-xs rounded-lg" rows="1" placeholder="选择结果描述">与角色一起行动，提升羁绊值</textarea>
                                            <select class="choice-chapter form-select text-xs rounded-lg">
                                                <option value="1">第一章</option>
                                                <option value="2" selected>第二章</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <button type="button" id="add_choice" class="w-full flex items-center justify-center gap-2 rounded-lg glass px-3 py-2 text-xs font-medium text-indigo-600 hover:bg-indigo-50 transition-all duration-300 border border-dashed border-indigo-300 hover:border-indigo-400 mt-3">
                                    <span class="material-icons-outlined text-sm">add</span> 
                                    <span>添加选择分支</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Right Column: AI Story Agent & Preview -->
                <div class="space-y-6">
                    <div class="story-agent-card glass-strong p-6 shadow-2xl rounded-3xl card-hover float">
                        <h3 class="text-xl font-bold gradient-text mb-2 flex items-center gap-2">
                            <span class="material-icons-outlined text-purple-500">psychology</span>
                            Story Agent 
                            <span class="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full font-medium">AI辅助</span>
                        </h3>
                        <p class="text-xs text-slate-500 mb-4">输入核心要素，让AI为你构思剧情线索和发展方向。</p>
                        
                        <div class="space-y-4">
                            <div class="input-group">
                                <label for="agent_story_bg" class="block text-xs font-medium text-slate-600 mb-1">故事背景/主题</label>
                                <textarea id="agent_story_bg" rows="2" class="form-textarea block w-full rounded-xl text-sm" placeholder="例如：一座被遗忘的魔法学院，每年特定夜晚会发生怪事。"></textarea>
                            </div>
                            <div class="input-group">
                                <label for="agent_main_conflict" class="block text-xs font-medium text-slate-600 mb-1">主要冲突/悬念</label>
                                <textarea id="agent_main_conflict" rows="2" class="form-textarea block w-full rounded-xl text-sm" placeholder="例如：主角需要解开学院的秘密，否则将面临危险。"></textarea>
                            </div>
                            <button type="button" class="w-full flex items-center justify-center gap-2 rounded-xl btn-glow px-4 py-3 text-sm font-semibold text-white transition-all">
                                <span class="material-icons-outlined text-base">auto_awesome_motion</span> 生成剧情线索
                            </button>
                        </div>

                        <div class="mt-5 border-t border-white/20 pt-4 space-y-3">
                            <h4 class="text-sm font-semibold text-slate-700 mb-2 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm text-purple-500">lightbulb</span>
                                AI生成的剧情发展建议:
                            </h4>
                            <div class="ai-suggestion rounded-xl p-3">
                                <p class="text-sm text-slate-600 relative z-10">📖 主角在图书馆发现一张古老的地图，指向禁忌区域。</p>
                            </div>
                            <div class="ai-suggestion rounded-xl p-3">
                                <p class="text-sm text-slate-600 relative z-10">👤 一位神秘的NPC出现，警告主角不要深入调查，但又暗示了重要线索。</p>
                            </div>
                            <div class="ai-suggestion rounded-xl p-3">
                                <p class="text-sm text-slate-600 relative z-10">🤝 主角需要在两位性格迥异的伙伴中选择一位共同冒险。</p>
                            </div>
                            <p class="text-xs text-slate-400 text-center mt-2">💡 点击建议可用于填充或启发您的创作</p>
                        </div>
                    </div>

                    <div class="glass-strong p-6 shadow-2xl rounded-3xl card-hover">
                        <h3 class="text-xl font-bold gradient-text mb-4 flex items-center gap-2">
                            <span class="material-icons-outlined text-green-500">preview</span>
                            故事线预览
                        </h3>
                        <div class="preview-area rounded-2xl overflow-hidden">
                            <img id="preview_cover_img" src="https://via.placeholder.com/300x150/E0E7FF/4F46E5?text=故事封面" alt="故事封面预览" class="w-full h-32 object-cover">
                            <div class="p-4">
                                <h4 id="preview_title" class="text-lg font-bold text-slate-700 truncate">[故事线标题]</h4>
                                <p id="preview_opening" class="text-sm text-slate-500 mt-1 line-clamp-2">[开场白预览...]</p>
                                <div class="flex items-center gap-2 mt-3">
                                    <span class="text-xs text-slate-400">关联角色:</span>
                                    <span id="preview_character" class="text-xs font-medium text-sky-600 px-2 py-1 bg-sky-50 rounded-full">[未选择]</span>
                                </div>
                                <div class="flex items-center gap-1 mt-3 flex-wrap" id="preview_tags">
                                    <!-- 标签将在这里动态显示 -->
                                </div>
                                
                                <!-- 章节预览 -->
                                <div class="mt-4 pt-4 border-t border-slate-200">
                                    <h5 class="text-sm font-semibold text-slate-700 mb-2 flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-amber-500">auto_stories</span>
                                        章节结构
                                    </h5>
                                    <div id="preview_chapters" class="space-y-2">
                                        <div class="flex items-center gap-2 text-xs">
                                            <span class="w-4 h-4 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">1</span>
                                            <span class="text-slate-600">开始的邂逅</span>
                                        </div>
                                        <div class="flex items-center gap-2 text-xs">
                                            <span class="w-4 h-4 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
                                            <span class="text-slate-600">深入探索</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 选择分支预览 -->
                                <div class="mt-4 pt-4 border-t border-slate-200">
                                    <h5 class="text-sm font-semibold text-slate-700 mb-2 flex items-center gap-1">
                                        <span class="material-icons-outlined text-sm text-purple-500">call_split</span>
                                        关键选择 <span class="text-xs bg-purple-100 text-purple-600 px-1 py-0.5 rounded" id="preview_choices_count">2</span>
                                    </h5>
                                    <div id="preview_choices" class="space-y-1">
                                        <div class="text-xs text-slate-500 flex items-center gap-1">
                                            <span>🔍</span>
                                            <span>深入调查神秘事件</span>
                                        </div>
                                        <div class="text-xs text-slate-500 flex items-center gap-1">
                                            <span>👥</span>
                                            <span>寻求朋友帮助</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <div class="glass p-4 rounded-2xl">
                            <p class="text-sm text-slate-600">
                                <span class="text-lg">💡</span>
                                <strong>创作提示：</strong>建议创作5-12分钟的短篇故事，更易于用户完成和获得满足感。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 封面图片预览
            const storyCoverInput = document.getElementById('story_cover_image');
            const previewCoverImg = document.getElementById('preview_cover_img');
            
            storyCoverInput.addEventListener('change', function(event) {
                if (event.target.files && event.target.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewCoverImg.src = e.target.result;
                        const formPreview = document.getElementById('cover_image_preview');
                        if(formPreview) formPreview.src = e.target.result;
                    }
                    reader.readAsDataURL(event.target.files[0]);
                } else {
                    previewCoverImg.src = "https://via.placeholder.com/300x150/E0E7FF/4F46E5?text=故事封面";
                }
            });

            // Live preview for title, opening, and character
            document.getElementById('story_title').addEventListener('input', e => {
                document.getElementById('preview_title').textContent = e.target.value || '[故事线标题]';
            });
            
            document.getElementById('story_opening').addEventListener('input', e => {
                document.getElementById('preview_opening').textContent = e.target.value || '[开场白预览...]';
            });
            
            document.getElementById('story_associated_character').addEventListener('change', e => {
                const selectedOption = e.target.options[e.target.selectedIndex];
                document.getElementById('preview_character').textContent = selectedOption.value ? selectedOption.text : '[未选择]';
            });

            // 标签预览
            document.getElementById('story_tags').addEventListener('input', e => {
                const tagsContainer = document.getElementById('preview_tags');
                const tags = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag);
                
                tagsContainer.innerHTML = '';
                if (tags.length > 0) {
                    tags.forEach(tag => {
                        const tagElement = document.createElement('span');
                        tagElement.className = 'story-tag text-xs px-2 py-1 rounded-full';
                        tagElement.textContent = tag;
                        tagsContainer.appendChild(tagElement);
                    });
                }
            });

            // AI建议点击事件
            document.querySelectorAll('.ai-suggestion').forEach(suggestion => {
                suggestion.addEventListener('click', function() {
                    this.style.background = 'rgba(139, 92, 246, 0.1)';
                    setTimeout(() => {
                        this.style.background = '';
                    }, 500);
                });
            });

            // 章节管理功能
            let chapterCount = 2;
            const chapterColors = ['blue', 'green', 'purple', 'orange', 'pink', 'indigo', 'red', 'yellow'];

            // 添加新章节
            document.getElementById('add_chapter').addEventListener('click', function() {
                chapterCount++;
                const colorIndex = (chapterCount - 1) % chapterColors.length;
                const color = chapterColors[colorIndex];
                
                const chapterHTML = `
                    <div class="chapter-item glass p-4 rounded-xl border-l-4 border-${color}-500">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-semibold text-slate-700 flex items-center gap-2">
                                <span class="w-6 h-6 bg-${color}-500 text-white rounded-full text-xs flex items-center justify-center">${chapterCount}</span>
                                第${chapterCount}章
                            </h4>
                            <div class="flex gap-2">
                                <button type="button" class="edit-chapter text-xs text-blue-600 hover:text-blue-700 px-2 py-1 rounded">
                                    <span class="material-icons-outlined text-sm">edit</span>
                                </button>
                                <button type="button" class="delete-chapter text-xs text-red-500 hover:text-red-600 px-2 py-1 rounded">
                                    <span class="material-icons-outlined text-sm">delete</span>
                                </button>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <input type="text" class="chapter-title form-input w-full text-sm rounded-lg" placeholder="章节标题" value="新章节 ${chapterCount}">
                            <textarea class="chapter-description form-textarea w-full text-sm rounded-lg" rows="2" placeholder="章节描述">第${chapterCount}章的故事内容...</textarea>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                <select class="chapter-unlock-condition form-select text-xs rounded-lg">
                                    <option value="none">无解锁条件</option>
                                    <option value="previous" selected>完成前一章节</option>
                                    <option value="bond_level">羁绊等级要求</option>
                                    <option value="item">收集特定物品</option>
                                </select>
                                <input type="text" class="chapter-unlock-value form-input text-xs rounded-lg" placeholder="解锁条件值" style="display: none;">
                            </div>
                        </div>
                    </div>
                `;
                
                document.getElementById('chapters_list').insertAdjacentHTML('beforeend', chapterHTML);
                updateChapterPreview();
                updateChoiceChapterOptions();
            });

            // 删除章节
            document.addEventListener('click', function(e) {
                if (e.target.closest('.delete-chapter')) {
                    const chapterItem = e.target.closest('.chapter-item');
                    if (document.querySelectorAll('.chapter-item').length > 1) {
                        chapterItem.remove();
                        updateChapterPreview();
                        updateChoiceChapterOptions();
                    } else {
                        alert('至少需要保留一个章节');
                    }
                }
            });

            // 添加新选择分支
            let choiceCount = 2;
            document.getElementById('add_choice').addEventListener('click', function() {
                choiceCount++;
                
                const choiceHTML = `
                    <div class="choice-item glass p-3 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <h5 class="text-xs font-medium text-slate-600">选择分支 ${choiceCount}</h5>
                            <button type="button" class="delete-choice text-xs text-red-500 hover:text-red-600">
                                <span class="material-icons-outlined text-sm">close</span>
                            </button>
                        </div>
                        <div class="space-y-2">
                            <input type="text" class="choice-text form-input w-full text-xs rounded-lg" placeholder="选择项文本" value="新选择 ${choiceCount}">
                            <textarea class="choice-result form-textarea w-full text-xs rounded-lg" rows="1" placeholder="选择结果描述">描述这个选择的后果...</textarea>
                            <select class="choice-chapter form-select text-xs rounded-lg">
                                ${getChapterOptions()}
                            </select>
                        </div>
                    </div>
                `;
                
                document.getElementById('choices_list').insertAdjacentHTML('beforeend', choiceHTML);
                updateChoicePreview();
            });

            // 删除选择分支
            document.addEventListener('click', function(e) {
                if (e.target.closest('.delete-choice')) {
                    const choiceItem = e.target.closest('.choice-item');
                    choiceItem.remove();
                    updateChoicePreview();
                }
            });

            // 实时更新章节预览
            function updateChapterPreview() {
                const previewContainer = document.getElementById('preview_chapters');
                const chapters = document.querySelectorAll('.chapter-item');
                
                previewContainer.innerHTML = '';
                chapters.forEach((chapter, index) => {
                    const title = chapter.querySelector('.chapter-title').value || `第${index + 1}章`;
                    const colorClass = chapter.className.match(/border-(\w+)-500/)?.[1] || 'blue';
                    
                    const chapterPreview = document.createElement('div');
                    chapterPreview.className = 'flex items-center gap-2 text-xs';
                    chapterPreview.innerHTML = `
                        <span class="w-4 h-4 bg-${colorClass}-500 text-white rounded-full flex items-center justify-center text-xs font-bold">${index + 1}</span>
                        <span class="text-slate-600">${title}</span>
                    `;
                    previewContainer.appendChild(chapterPreview);
                });
            }

            // 实时更新选择预览
            function updateChoicePreview() {
                const previewContainer = document.getElementById('preview_choices');
                const previewCount = document.getElementById('preview_choices_count');
                const choices = document.querySelectorAll('.choice-item');
                
                previewContainer.innerHTML = '';
                previewCount.textContent = choices.length;
                
                choices.forEach(choice => {
                    const text = choice.querySelector('.choice-text').value || '新选择';
                    const emoji = text.match(/^([🔍👥📚🗡️⚔️🛡️🏃‍♂️💭🎯🔮])/)?.[1] || '•';
                    
                    const choicePreview = document.createElement('div');
                    choicePreview.className = 'text-xs text-slate-500 flex items-center gap-1';
                    choicePreview.innerHTML = `
                        <span>${emoji}</span>
                        <span>${text.replace(/^[🔍👥📚🗡️⚔️🛡️🏃‍♂️💭🎯🔮]\s*/, '')}</span>
                    `;
                    previewContainer.appendChild(choicePreview);
                });
            }

            // 获取章节选项
            function getChapterOptions() {
                const chapters = document.querySelectorAll('.chapter-item');
                let options = '';
                chapters.forEach((chapter, index) => {
                    const title = chapter.querySelector('.chapter-title').value || `第${index + 1}章`;
                    options += `<option value="${index + 1}">${title}</option>`;
                });
                return options;
            }

            // 更新选择分支的章节选项
            function updateChoiceChapterOptions() {
                const choiceSelects = document.querySelectorAll('.choice-chapter');
                const newOptions = getChapterOptions();
                
                choiceSelects.forEach(select => {
                    const currentValue = select.value;
                    select.innerHTML = newOptions;
                    if (currentValue && select.querySelector(`option[value="${currentValue}"]`)) {
                        select.value = currentValue;
                    }
                });
            }

            // 监听章节标题变化
            document.addEventListener('input', function(e) {
                if (e.target.classList.contains('chapter-title')) {
                    updateChapterPreview();
                    updateChoiceChapterOptions();
                }
                if (e.target.classList.contains('choice-text')) {
                    updateChoicePreview();
                }
            });

            // 监听解锁条件变化
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('chapter-unlock-condition')) {
                    const valueInput = e.target.parentNode.querySelector('.chapter-unlock-value');
                    if (e.target.value === 'bond_level' || e.target.value === 'item') {
                        valueInput.style.display = 'block';
                        valueInput.placeholder = e.target.value === 'bond_level' ? '等级 (如: 3)' : '物品名称';
                    } else {
                        valueInput.style.display = 'none';
                    }
                }
            });
            
            // 延迟显示动画
            setTimeout(() => {
                document.querySelectorAll('.card-hover').forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s ease-out';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 100);
        });
    </script>
</body>
</html>