<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - Alphane.ai</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --accent: #ec4899;
            --warm: #f59e0b;
            --success: #10b981;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #fef7ff 0%, #f9fafb 30%, #fef2f2 70%, #fef7ff 100%);
            min-height: 100vh;
        }

        /* 动态背景效果 */
        .bg-animated {
            background: linear-gradient(-45deg, #fef7ff, #f9fafb, #fef2f2, #ffffff);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 毛玻璃效果 */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.6);
        }

        /* 浮动动画 */
        .float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 卡片悬浮效果 */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        /* 代币发光效果 */
        .token-glow {
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 10px rgba(245, 158, 11, 0.5); }
            to { box-shadow: 0 0 20px rgba(245, 158, 11, 0.8), 0 0 30px rgba(245, 158, 11, 0.4); }
        }

        /* Streak火焰效果 */
        .streak-flame {
            position: relative;
            display: inline-block;
        }

        .streak-flame::before {
            content: '🔥';
            position: absolute;
            top: -5px;
            left: -5px;
            font-size: 1.2em;
            animation: flicker 1.5s ease-in-out infinite alternate;
        }

        @keyframes flicker {
            0%, 100% { transform: scale(1) rotate(-2deg); opacity: 1; }
            50% { transform: scale(1.1) rotate(2deg); opacity: 0.8; }
        }

        /* 成就徽章效果 */
        .achievement-badge {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border: 2px solid #f59e0b;
            transition: all 0.3s ease;
        }

        .achievement-badge:hover {
            transform: scale(1.05) rotate(2deg);
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.4);
        }

        .achievement-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .achievement-badge:hover::before {
            left: 100%;
        }

        /* 羁绊等级进度条 */
        .bond-progress {
            background: linear-gradient(90deg, #ec4899 0%, #ec4899 60%, #e5e7eb 60%, #e5e7eb 100%);
            position: relative;
            overflow: hidden;
        }

        .bond-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 60%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: bondShine 2s infinite;
        }

        @keyframes bondShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(167%); }
        }

        /* 等级徽章 */
        .level-badge {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .level-badge::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: levelShine 3s linear infinite;
        }

        @keyframes levelShine {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 统计数字动画 */
        .stat-number {
            font-weight: bold;
            background: linear-gradient(135deg, #6366f1, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: numberPulse 2s ease-in-out infinite;
        }

        @keyframes numberPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 快捷操作网格 */
        .profile-grid-item {
            @apply glass p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer flex flex-col items-center text-center;
            transform: translateY(0);
        }

        .profile-grid-item:hover {
            transform: translateY(-8px) scale(1.02);
            background: rgba(255, 255, 255, 0.4);
        }

        .profile-grid-item .material-icons-outlined {
            @apply text-4xl mb-3 transition-all duration-300;
        }

        .profile-grid-item:hover .material-icons-outlined {
            transform: scale(1.2) rotate(5deg);
        }

        /* VIP标识 */
        .vip-crown {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #92400e;
            animation: crownGlow 2s ease-in-out infinite alternate;
        }

        @keyframes crownGlow {
            from { box-shadow: 0 0 10px rgba(255, 215, 0, 0.5); }
            to { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.4); }
        }

        /* 任务进度环 */
        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            transition: stroke-dashoffset 0.5s ease-in-out;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .glass-strong {
                margin: 0.5rem;
                backdrop-filter: blur(15px);
            }
        }
    </style>
</head>
<body class="bg-animated text-slate-900">
    <!-- 动态背景粒子 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
        <div class="absolute top-3/4 right-1/4 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-1/4 left-1/3 w-72 h-72 bg-rose-200 rounded-full mix-blend-multiply filter blur-3xl opacity-8 animate-pulse animation-delay-4000"></div>
    </div>
    <!-- Main navigation sidebar (similar to home page) -->
    <div class="flex h-screen overflow-hidden relative z-10">
        <aside class="fixed left-0 top-0 z-40 flex h-full w-72 flex-col border-r border-white/20 glass-strong shadow-2xl">
            <div class="flex items-center gap-3 px-6 py-5 border-b border-white/20">
                <svg class="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path>
                </svg>
                <span class="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Alphane.ai</span>
            </div>
            <nav class="flex-1 space-y-1.5 overflow-y-auto px-4 pb-4">
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-white/30 hover:text-indigo-600 transition-all" href="2-PC-首页.html">
                    <span class="material-icons-outlined text-lg">home</span> 首页
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-white/30 hover:text-indigo-600 transition-all" href="6-PC-创建编辑角色页.html">
                     <span class="material-icons-outlined text-lg">add_circle_outline</span> 创建角色
                </a>
                 <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-white/30 hover:text-indigo-600 transition-all" href="7-PC-创建编辑故事线页.html">
                    <span class="material-icons-outlined text-lg">auto_stories</span> 创建故事线
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-white/30 hover:text-indigo-600 transition-all" href="11-PC-荣耀战令页.html">
                    <span class="material-icons-outlined text-lg">military_tech</span> 荣耀战令
                </a>
                 <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-white/30 hover:text-indigo-600 transition-all" href="13-PC-商店付费页.html">
                    <span class="material-icons-outlined text-lg">store</span> 商店
                </a>
                <div class="pt-2">
                    <h3 class="mb-1 px-3 text-xs font-semibold uppercase text-slate-500">个人空间</h3>
                    <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium bg-indigo-100 text-indigo-700 transition-all" href="8-PC-个人中心页.html">
                        <span class="material-icons-outlined text-lg">person</span> 个人中心
                    </a>
                    <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-white/30 hover:text-indigo-600 transition-all" href="8A-PC-创作者中心页.html">
                        <span class="material-icons-outlined text-lg">hub</span> 创作者中心
                    </a>
                    <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-white/30 hover:text-indigo-600 transition-all" href="15-PC-通知中心页.html">
                        <span class="material-icons-outlined text-lg">notifications</span> 通知中心
                    </a>
                     <a class="group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-white/30 hover:text-indigo-600 transition-all" href="14-PC-设置页.html">
                        <span class="material-icons-outlined text-lg">settings</span> 设置
                    </a>
                </div>
            </nav>
             <div class="mt-auto border-t border-white/20 p-4">
                <button class="w-full group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-slate-700 hover:bg-red-50 hover:text-red-600 transition-all">
                    <span class="material-icons-outlined text-lg">logout</span> 退出登录
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="ml-72 flex-1 overflow-y-auto">
            <header class="sticky top-0 z-30 glass-strong shadow-xl border-b border-white/20">
                <div class="flex h-20 items-center justify-between px-8">
                    <div class="flex items-center gap-4">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">个人中心</h1>
                        <div class="level-badge px-3 py-1 rounded-full text-sm font-semibold">
                            Lv.25 元气使者
                        </div>
                    </div>
                    <div class="flex items-center gap-4">
                        <!-- 代币展示 -->
                        <div class="flex items-center gap-3">
                            <div class="glass flex items-center gap-2 px-3 py-2 rounded-full token-glow">
                                <span class="text-orange-500 text-lg">🔥</span>
                                <span class="text-sm font-bold text-slate-700">1,247</span>
                            </div>
                            <div class="glass flex items-center gap-2 px-3 py-2 rounded-full">
                                <span class="text-blue-500 text-lg">💎</span>
                                <span class="text-sm font-bold text-slate-700">89</span>
                            </div>
                            <div class="glass flex items-center gap-2 px-3 py-2 rounded-full">
                                <span class="text-purple-500 text-lg">🧩</span>
                                <span class="text-sm font-bold text-slate-700">156</span>
                            </div>
                            <div class="glass flex items-center gap-2 px-3 py-2 rounded-full">
                                <span class="text-pink-500 text-lg">💧</span>
                                <span class="text-sm font-bold text-slate-700">342</span>
                            </div>
                        </div>
                        <button title="编辑资料" class="glass p-3 rounded-full hover:bg-white/30 transition-all duration-300 transform hover:scale-110">
                            <span class="material-icons-outlined text-indigo-600">edit</span>
                        </button>
                    </div>
                </div>
            </header>

            <div class="p-8 space-y-8">
                <!-- 用户信息卡片 -->
                <div class="glass-strong shadow-2xl rounded-3xl p-8 card-hover">
                    <div class="flex flex-col lg:flex-row items-center gap-8">
                        <div class="relative float">
                            <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuCJ4cxwUDTHBUas9jQUHfn0EOCkO1_hPd39uWb5nGf3MJa9YPWUxexNoApBUfmrvoofIWUEeLCTGpN915lEKGVmRc1mOskvD_PlwWj2ZVIZEcY7QgzgVWXAqG89CUnFWKzhvu_appXzWBQ_Db9hGg-7DVxpUJIe0LBdKD665kvOKWniSTt4LHD3TH13XRqk2ncSqUp9tZIr2i-_VUdXvxBp2QdeygaxyJqQ2QypVaTTOdZXy24NQZNYkv1vbHQMv8m80kpVLHo4aOIm" alt="User Avatar" class="w-32 h-32 lg:w-40 lg:h-40 rounded-full border-4 border-gradient-to-r from-indigo-500 to-purple-500 shadow-2xl">
                            <div class="vip-crown absolute -top-2 -right-2 p-2 rounded-full border-2 border-white shadow-lg" title="钻石会员">
                                <span class="material-icons-outlined text-amber-600 text-xl">workspace_premium</span>
                            </div>
                            <!-- 在线状态 -->
                            <div class="absolute bottom-4 right-4 w-6 h-6 bg-green-500 rounded-full border-3 border-white shadow-lg animate-pulse"></div>
                        </div>
                        <div class="flex-1 text-center lg:text-left space-y-4">
                            <div>
                                <h2 class="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">梦境探索者</h2>
                                <p class="text-slate-600 text-lg">UID: 123456789</p>
                                <p class="text-slate-500 mt-2 max-w-md">✨ 在Alphane的世界里寻找属于自己的故事，与AI伙伴们共同创造无限可能...</p>
                            </div>
                            <div class="flex flex-wrap gap-2 justify-center lg:justify-start">
                                <span class="vip-crown inline-flex items-center gap-1 text-sm font-semibold px-3 py-1.5 rounded-full">
                                    <span class="material-icons-outlined text-sm">diamond</span>
                                    钻石会员
                                </span>
                                <span class="achievement-badge inline-flex items-center gap-1 text-sm font-semibold px-3 py-1.5 rounded-full">
                                    <span class="material-icons-outlined text-sm">star</span>
                                    创作达人 Lv.8
                                </span>
                                <span class="glass inline-flex items-center gap-1 text-sm font-semibold px-3 py-1.5 rounded-full text-green-700">
                                    <span class="material-icons-outlined text-sm">local_fire_department</span>
                                    连击 47天
                                </span>
                            </div>
                        </div>
                        <!-- Streak可视化 -->
                        <div class="text-center">
                            <div class="relative inline-block">
                                <svg class="progress-ring w-24 h-24" viewBox="0 0 120 120">
                                    <circle
                                        class="progress-ring-circle stroke-slate-200"
                                        stroke-width="8"
                                        fill="transparent"
                                        r="52"
                                        cx="60"
                                        cy="60"/>
                                    <circle
                                        class="progress-ring-circle stroke-orange-500"
                                        stroke-width="8"
                                        fill="transparent"
                                        r="52"
                                        cx="60"
                                        cy="60"
                                        stroke-dasharray="327"
                                        stroke-dashoffset="98"/>
                                </svg>
                                <div class="absolute inset-0 flex flex-col items-center justify-center">
                                    <span class="text-2xl font-bold text-orange-500 streak-flame">47</span>
                                    <span class="text-xs text-slate-500">连击天数</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assets Overview -->
                <div class="bg-white shadow-lg rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-slate-800 mb-4">我的资产</h3>
                    <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
                        <div>
                            <p class="text-2xl font-bold text-sky-600">1,250</p>
                            <p class="text-sm text-slate-500">星钻 (付费)</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-green-600">8,800</p>
                            <p class="text-sm text-slate-500">银币 (活动)</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-amber-600">500</p>
                            <p class="text-sm text-slate-500">记忆碎片 (交互)</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-purple-600">2,300</p>
                            <p class="text-sm text-slate-500">贡献积分 (创作)</p>
                        </div>
                    </div>
                </div>
                
                <!-- Daily Streak / Sign-in -->
                <div class="bg-white shadow-lg rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-slate-800">每日签到 Streak</h3>
                        <span class="text-sm text-slate-500">已连续签到 <span class="font-bold text-orange-500">15</span> 天</span>
                    </div>
                    <div class="w-full bg-slate-200 rounded-full h-2.5 mb-2">
                        <div class="bg-gradient-to-r from-orange-400 to-red-500 h-2.5 rounded-full" style="width: 75%"></div>
                    </div>
                    <div class="text-right">
                         <button class="text-sm font-medium text-sky-600 hover:text-sky-700 disabled:opacity-50 disabled:cursor-not-allowed" disabled>今日已签到</button>
                        <!-- <button class="text-sm font-medium text-sky-600 hover:text-sky-700">立即签到</button> -->
                    </div>
                </div>

                <!-- 四种代币详情展示 -->
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="p-3 bg-orange-100 rounded-full">
                                <span class="text-orange-500 text-2xl">🔥</span>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-slate-800">曦光微尘</h3>
                                <p class="text-sm text-slate-500">基础活跃代币</p>
                            </div>
                        </div>
                        <div class="stat-number text-3xl mb-2">1,247</div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-green-600 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm">trending_up</span>
                                +50 今日
                            </span>
                            <button class="text-indigo-600 hover:text-indigo-700 font-medium">获取途径</button>
                        </div>
                    </div>

                    <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="p-3 bg-blue-100 rounded-full">
                                <span class="text-blue-500 text-2xl">💎</span>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-slate-800">心悦晶石</h3>
                                <p class="text-sm text-slate-500">进阶活跃代币</p>
                            </div>
                        </div>
                        <div class="stat-number text-3xl mb-2">89</div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-green-600 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm">trending_up</span>
                                +8 今日
                            </span>
                            <button class="text-indigo-600 hover:text-indigo-700 font-medium">查看用途</button>
                        </div>
                    </div>

                    <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="p-3 bg-purple-100 rounded-full">
                                <span class="text-purple-500 text-2xl">🧩</span>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-slate-800">忆境拼图</h3>
                                <p class="text-sm text-slate-500">收集探索代币</p>
                            </div>
                        </div>
                        <div class="stat-number text-3xl mb-2">156</div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-orange-600 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm">auto_fix_high</span>
                                5套待集齐
                            </span>
                            <button class="text-indigo-600 hover:text-indigo-700 font-medium">查看收藏</button>
                        </div>
                    </div>

                    <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="p-3 bg-pink-100 rounded-full">
                                <span class="text-pink-500 text-2xl">💧</span>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-slate-800">羁绊之露</h3>
                                <p class="text-sm text-slate-500">角色羁绊代币</p>
                            </div>
                        </div>
                        <div class="stat-number text-3xl mb-2">342</div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-blue-600 flex items-center gap-1">
                                <span class="material-icons-outlined text-sm">favorite</span>
                                3个角色可升级
                            </span>
                            <button class="text-indigo-600 hover:text-indigo-700 font-medium">管理羁绊</button>
                        </div>
                    </div>
                </div>

                <!-- 今日任务与Streak -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-bold text-slate-800 flex items-center gap-2">
                                <span class="material-icons-outlined text-indigo-500">task_alt</span>
                                今日任务进度
                            </h3>
                            <span class="achievement-badge px-3 py-1 rounded-full text-sm font-semibold">13/15 完成</span>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex items-center gap-3">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="flex-1 text-sm text-slate-600">与AI角色进行深度对话</span>
                                <span class="text-xs text-green-600 font-medium">已完成</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="flex-1 text-sm text-slate-600">存储3条新记忆胶囊</span>
                                <span class="text-xs text-green-600 font-medium">已完成</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                                <span class="flex-1 text-sm text-slate-600">浏览并点赞5个角色卡</span>
                                <span class="text-xs text-orange-600 font-medium">3/5</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                                <span class="flex-1 text-sm text-slate-600">完成记忆碎片画图许愿</span>
                                <span class="text-xs text-orange-600 font-medium">进行中</span>
                            </div>
                        </div>

                        <div class="mt-6 pt-4 border-t border-white/20">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-slate-600">今日奖励进度</span>
                                <span class="text-sm font-semibold text-indigo-600">87%</span>
                            </div>
                            <div class="w-full bg-slate-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-500" style="width: 87%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-bold text-slate-800 flex items-center gap-2">
                                <span class="material-icons-outlined text-orange-500">local_fire_department</span>
                                Streak连击系统
                            </h3>
                            <span class="glass px-3 py-1 rounded-full text-sm font-semibold text-orange-600">47天连击</span>
                        </div>
                        
                        <div class="flex items-center justify-center mb-6">
                            <div class="relative">
                                <svg class="progress-ring w-32 h-32" viewBox="0 0 120 120">
                                    <circle class="stroke-slate-200" stroke-width="6" fill="transparent" r="52" cx="60" cy="60"/>
                                    <circle class="stroke-orange-500" stroke-width="6" fill="transparent" r="52" cx="60" cy="60" 
                                            stroke-dasharray="327" stroke-dashoffset="33" 
                                            style="transition: stroke-dashoffset 0.5s ease-in-out;"/>
                                </svg>
                                <div class="absolute inset-0 flex flex-col items-center justify-center">
                                    <span class="text-3xl font-bold text-orange-500 streak-flame">47</span>
                                    <span class="text-sm text-slate-500">距离里程碑</span>
                                    <span class="text-xs text-slate-400">3天</span>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 glass rounded-lg">
                                <span class="text-sm font-medium">下个里程碑</span>
                                <span class="text-sm text-orange-600 font-semibold">50天 🏆</span>
                            </div>
                            <div class="flex items-center justify-between p-3 glass rounded-lg">
                                <span class="text-sm font-medium">Freeze卡剩余</span>
                                <span class="text-sm text-blue-600 font-semibold">3张 ❄️</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 成就与羁绊展示 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-bold text-slate-800 flex items-center gap-2">
                                <span class="material-icons-outlined text-yellow-500">emoji_events</span>
                                最新成就
                            </h3>
                            <a href="10-PC-成就徽章页.html" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">查看全部</a>
                        </div>

                        <div class="space-y-4">
                            <div class="achievement-badge flex items-center gap-4 p-4 rounded-2xl">
                                <div class="text-3xl">🎓</div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-amber-800">学院新星</h4>
                                    <p class="text-sm text-amber-600">完成校园奇遇故事线</p>
                                </div>
                                <span class="text-xs bg-white/30 px-2 py-1 rounded-full">新获得</span>
                            </div>

                            <div class="glass flex items-center gap-4 p-4 rounded-2xl">
                                <div class="text-3xl">🔍</div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-slate-700">真相探索者</h4>
                                    <p class="text-sm text-slate-500">发现所有隐藏线索</p>
                                </div>
                                <span class="text-xs bg-slate-200 px-2 py-1 rounded-full">85%</span>
                            </div>

                            <div class="glass flex items-center gap-4 p-4 rounded-2xl">
                                <div class="text-3xl">💕</div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-slate-700">最佳伙伴</h4>
                                    <p class="text-sm text-slate-500">与Aiko达到最高羁绊</p>
                                </div>
                                <span class="text-xs bg-slate-200 px-2 py-1 rounded-full">92%</span>
                            </div>
                        </div>
                    </div>

                    <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-bold text-slate-800 flex items-center gap-2">
                                <span class="material-icons-outlined text-pink-500">favorite</span>
                                我的羁绊伙伴
                            </h3>
                            <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">管理全部</a>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center gap-4 p-4 glass rounded-2xl hover:bg-white/40 transition-all">
                                <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuAOo6oSlAOPgjixfL9uZtkVvrUuw85OvQBaYqRpL0WqReyV0ifl5CEgfNF9ifNMLsM4TIZtwTo6pWrlOOnjNPcx5iYh6UiwTNzwwFPL4EyOQZnc0TnEn7CiQ0u5fMXp3NDbyTHdwldWgZ7IePBfTxPc7Taizxc9ZbMHy3wv0inbJ_vxXWeGRCigsswpy7XY1lKCFuVyaYIClebuRATn4Igfcvgb2fFs7sgJPFrMfksukzLHRtocEyk_HOeqKTNLKiddXCjMNlCTjo07" alt="Aiko" class="w-16 h-16 rounded-full border-3 border-pink-300">
                                <div class="flex-1">
                                    <div class="flex items-center gap-2">
                                        <h4 class="font-semibold text-slate-700">Aiko</h4>
                                        <span class="level-badge text-xs px-2 py-1 rounded-full">Lv.8</span>
                                    </div>
                                    <div class="mt-1">
                                        <div class="bond-progress h-2 rounded-full"></div>
                                        <p class="text-xs text-slate-500 mt-1">知己 → 灵魂伙伴 (1,245/1,500)</p>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center gap-4 p-4 glass rounded-2xl hover:bg-white/40 transition-all">
                                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center border-3 border-blue-300">
                                    <span class="material-icons-outlined text-white text-2xl">person</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center gap-2">
                                        <h4 class="font-semibold text-slate-700">神秘学长</h4>
                                        <span class="level-badge text-xs px-2 py-1 rounded-full">Lv.5</span>
                                    </div>
                                    <div class="mt-1">
                                        <div class="w-full bg-slate-200 rounded-full h-2">
                                            <div class="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full" style="width: 65%"></div>
                                        </div>
                                        <p class="text-xs text-slate-500 mt-1">亲密 → 知己 (650/1,000)</p>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center gap-4 p-4 glass rounded-2xl hover:bg-white/40 transition-all">
                                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-400 to-pink-500 flex items-center justify-center border-3 border-purple-300">
                                    <span class="material-icons-outlined text-white text-2xl">auto_fix_high</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center gap-2">
                                        <h4 class="font-semibold text-slate-700">魔法导师Luna</h4>
                                        <span class="level-badge text-xs px-2 py-1 rounded-full">Lv.3</span>
                                    </div>
                                    <div class="mt-1">
                                        <div class="w-full bg-slate-200 rounded-full h-2">
                                            <div class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full" style="width: 30%"></div>
                                        </div>
                                        <p class="text-xs text-slate-500 mt-1">熟悉 → 亲密 (150/500)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作网格 -->
                <div class="glass-strong shadow-xl rounded-2xl p-6 card-hover">
                    <h3 class="text-xl font-bold text-slate-800 mb-6 flex items-center gap-2">
                        <span class="material-icons-outlined text-indigo-500">dashboard</span>
                        快捷功能
                    </h3>
                    
                    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                        <a href="14-PC-设置页.html#account" class="profile-grid-item">
                            <span class="material-icons-outlined text-indigo-500">manage_accounts</span>
                            <span class="text-sm font-medium text-slate-700">账户与安全</span>
                        </a>
                        <a href="13-PC-商店付费页.html#subscription" class="profile-grid-item">
                            <span class="material-icons-outlined text-yellow-500">card_membership</span>
                            <span class="text-sm font-medium text-slate-700">会员中心</span>
                        </a>
                        <a href="#followed-characters" class="profile-grid-item">
                            <span class="material-icons-outlined text-pink-500">supervisor_account</span>
                            <span class="text-sm font-medium text-slate-700">关注的角色</span>
                        </a>
                        <a href="#my-memories" class="profile-grid-item">
                            <span class="material-icons-outlined text-purple-500">collections_bookmark</span>
                            <span class="text-sm font-medium text-slate-700">记忆胶囊</span>
                        </a>
                        <a href="9-PC-任务中心页.html" class="profile-grid-item">
                            <span class="material-icons-outlined text-green-500">task_alt</span>
                            <span class="text-sm font-medium text-slate-700">任务中心</span>
                        </a>
                        <a href="10-PC-成就徽章页.html" class="profile-grid-item">
                            <span class="material-icons-outlined text-orange-500">emoji_events</span>
                            <span class="text-sm font-medium text-slate-700">成就与徽章</span>
                        </a>
                        <a href="11-PC-荣耀战令页.html" class="profile-grid-item">
                            <span class="material-icons-outlined text-red-500">military_tech</span>
                            <span class="text-sm font-medium text-slate-700">荣耀战令</span>
                        </a>
                        <a href="#invite-friends" class="profile-grid-item">
                            <span class="material-icons-outlined text-blue-500">group_add</span>
                            <span class="text-sm font-medium text-slate-700">邀请好友</span>
                        </a>
                        <a href="8A-PC-创作者中心页.html" class="profile-grid-item bg-gradient-to-br from-indigo-50 to-purple-50 border-2 border-indigo-200">
                            <span class="material-icons-outlined text-indigo-600">hub</span>
                            <span class="text-sm font-medium text-indigo-700">创作者中心</span>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 页面加载完成后的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 数字动画效果
            function animateNumbers() {
                const numberElements = document.querySelectorAll('.stat-number');
                numberElements.forEach(element => {
                    const finalNumber = parseInt(element.textContent.replace(/,/g, ''));
                    const duration = 2000;
                    const startTime = Date.now();
                    
                    function updateNumber() {
                        const elapsed = Date.now() - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        const currentNumber = Math.floor(finalNumber * progress);
                        element.textContent = currentNumber.toLocaleString();
                        
                        if (progress < 1) {
                            requestAnimationFrame(updateNumber);
                        }
                    }
                    
                    updateNumber();
                });
            }

            // 延迟动画启动
            setTimeout(() => {
                animateNumbers();
                
                // 卡片渐入动画
                const cards = document.querySelectorAll('.card-hover');
                cards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s ease-out';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 300);

            // Streak进度环动画
            function updateStreakProgress() {
                const circle = document.querySelector('.streak-orange-500');
                if (circle) {
                    const radius = 52;
                    const circumference = 2 * Math.PI * radius;
                    const progress = 47 / 50; // 47天连击，目标50天
                    const offset = circumference - (progress * circumference);
                    
                    circle.style.strokeDasharray = circumference;
                    circle.style.strokeDashoffset = offset;
                }
            }

            // 羁绊进度条动画
            function animateBondProgress() {
                const progressBars = document.querySelectorAll('.bond-progress');
                progressBars.forEach(bar => {
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.transition = 'width 1.5s ease-out';
                        bar.style.width = '60%';
                    }, 500);
                });
            }

            // 代币卡片点击效果
            const tokenCards = document.querySelectorAll('.glass-strong');
            tokenCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    if (this.querySelector('.stat-number')) {
                        // 创建涟漪效果
                        const ripple = document.createElement('div');
                        const rect = this.getBoundingClientRect();
                        const size = Math.max(rect.width, rect.height);
                        const x = e.clientX - rect.left - size / 2;
                        const y = e.clientY - rect.top - size / 2;
                        
                        ripple.style.cssText = `
                            position: absolute;
                            width: ${size}px;
                            height: ${size}px;
                            left: ${x}px;
                            top: ${y}px;
                            background: rgba(99, 102, 241, 0.3);
                            border-radius: 50%;
                            transform: scale(0);
                            animation: ripple 0.6s ease-out;
                            pointer-events: none;
                        `;
                        
                        this.style.position = 'relative';
                        this.style.overflow = 'hidden';
                        this.appendChild(ripple);
                        
                        setTimeout(() => ripple.remove(), 600);
                    }
                });
            });

            // 任务项点击动画
            const taskItems = document.querySelectorAll('.space-y-4 > div');
            taskItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // 启动动画
            updateStreakProgress();
            setTimeout(animateBondProgress, 1000);
        });

        // CSS动画定义
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            
            .animate-bounce-slow {
                animation: bounce 3s infinite;
            }
            
            .animate-pulse-slow {
                animation: pulse 4s ease-in-out infinite;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
