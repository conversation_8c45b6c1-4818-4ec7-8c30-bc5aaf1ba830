<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创作者中心 - Alphane.ai</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B600%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --accent: #ec4899;
            --warm: #f59e0b;
            --success: #10b981;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #fef7ff 0%, #f9fafb 30%, #fef2f2 70%, #fef7ff 100%);
            min-height: 100vh;
        }

        /* 动态背景效果 */
        .bg-animated {
            background: linear-gradient(-45deg, #fef7ff, #f9fafb, #fef2f2, #ffffff);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 毛玻璃效果 */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.6);
        }

        /* 创作者侧边栏 */
        .creator-hub-sidebar a.active {
            @apply bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 border-indigo-500 font-semibold shadow-lg;
            transform: translateX(5px);
        }
        
        .creator-hub-sidebar a {
            @apply border-transparent text-slate-600 hover:bg-white/40 hover:text-slate-800 transition-all duration-300;
        }

        .creator-hub-sidebar a:hover {
            transform: translateX(3px);
        }

        /* Tab内容 */
        .tab-content-creator { 
            display: none; 
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }
        
        .tab-content-creator.active { 
            display: block; 
            opacity: 1;
            transform: translateY(0);
        }

        /* 数据卡片 */
        .data-card { 
            @apply glass-strong p-6 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500;
            position: relative;
            overflow: hidden;
        }

        .data-card:hover {
            transform: translateY(-8px) scale(1.02);
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .data-card:hover::before {
            left: 100%;
        }

        /* 创作者等级徽章 */
        .creator-badge {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #92400e;
            animation: creatorGlow 3s ease-in-out infinite alternate;
            position: relative;
            overflow: hidden;
        }

        @keyframes creatorGlow {
            from { box-shadow: 0 0 15px rgba(255, 215, 0, 0.5); }
            to { box-shadow: 0 0 25px rgba(255, 215, 0, 0.8), 0 0 35px rgba(255, 215, 0, 0.4); }
        }

        .creator-badge::after {
            content: '✨';
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 1.2em;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
            50% { transform: scale(1.2) rotate(180deg); opacity: 0.7; }
        }

        /* 统计数字动画 */
        .stat-number {
            font-weight: bold;
            background: linear-gradient(135deg, #6366f1, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: numberPulse 2s ease-in-out infinite;
        }

        @keyframes numberPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 收益卡片特效 */
        .earnings-card {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .earnings-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: earningsShine 4s linear infinite;
        }

        @keyframes earningsShine {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 表格操作按钮 */
        .list-item-action button, .list-item-action a { 
            @apply text-xs px-3 py-1.5 rounded-lg transition-all duration-300 font-medium;
        }

        .list-item-action button:hover, .list-item-action a:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 浮动动画 */
        .float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 卡片悬浮效果 */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        /* 进度条动画 */
        .progress-bar {
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
            transition: width 1.5s ease-out;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .glass-strong {
                margin: 0.5rem;
                backdrop-filter: blur(15px);
            }
        }
    </style>
</head>
<body class="text-slate-800 bg-animated">
    <!-- 动态背景粒子 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
        <div class="absolute top-3/4 right-1/4 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-1/4 left-1/3 w-72 h-72 bg-rose-200 rounded-full mix-blend-multiply filter blur-3xl opacity-8 animate-pulse animation-delay-4000"></div>
    </div>
    <div class="flex h-screen overflow-hidden relative z-10">
        <aside class="creator-hub-sidebar fixed left-0 top-0 z-40 flex h-full w-72 flex-col border-r border-white/20 glass-strong shadow-2xl">
            <div class="flex items-center gap-3 px-6 py-6 border-b border-white/20">
                <svg class="h-9 w-9 text-indigo-600" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><path d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z" fill="currentColor"></path></svg>
                <span class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Alphane.ai</span>
            </div>
            <nav class="flex-1 space-y-1 overflow-y-auto px-4 py-4">
                <a href="javascript:void(0)" onclick="showCreatorTab('dashboard')" class="group flex items-center gap-3 rounded-xl border-l-4 px-4 py-3 text-sm font-medium active">
                    <span class="material-icons-outlined text-xl">dashboard</span> 
                    <span>仪表盘</span>
                    <div class="ml-auto w-2 h-2 bg-green-500 rounded-full"></div>
                </a>
                <a href="javascript:void(0)" onclick="showCreatorTab('myCreations')" class="group flex items-center gap-3 rounded-xl border-l-4 px-4 py-3 text-sm font-medium">
                    <span class="material-icons-outlined text-xl">palette</span> 
                    <span>我的创作</span>
                    <span class="ml-auto text-xs bg-indigo-100 text-indigo-600 px-2 py-1 rounded-full">25</span>
                </a>
                <a href="javascript:void(0)" onclick="showCreatorTab('analytics')" class="group flex items-center gap-3 rounded-xl border-l-4 px-4 py-3 text-sm font-medium">
                    <span class="material-icons-outlined text-xl">analytics</span> 
                    <span>数据分析</span>
                    <span class="ml-auto text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">新</span>
                </a>
                <a href="javascript:void(0)" onclick="showCreatorTab('community')" class="group flex items-center gap-3 rounded-xl border-l-4 px-4 py-3 text-sm font-medium">
                    <span class="material-icons-outlined text-xl">groups</span> 
                    <span>创作者社区</span>
                </a>
                <a href="javascript:void(0)" onclick="showCreatorTab('voiceSamples')" class="group flex items-center gap-3 rounded-xl border-l-4 px-4 py-3 text-sm font-medium">
                    <span class="material-icons-outlined text-xl">mic</span> 
                    <span>语音库</span>
                    <span class="ml-auto text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full">Beta</span>
                </a>
                <a href="javascript:void(0)" onclick="showCreatorTab('earnings')" class="group flex items-center gap-3 rounded-xl border-l-4 px-4 py-3 text-sm font-medium">
                    <span class="material-icons-outlined text-xl">monetization_on</span> 
                    <span>激励收益</span>
                    <span class="ml-auto text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">+15%</span>
                </a>
                <a href="javascript:void(0)" onclick="showCreatorTab('resources')" class="group flex items-center gap-3 rounded-xl border-l-4 px-4 py-3 text-sm font-medium">
                    <span class="material-icons-outlined text-xl">school</span> 
                    <span>创作资源</span>
                </a>
                <a href="javascript:void(0)" onclick="showCreatorTab('leaderboard')" class="group flex items-center gap-3 rounded-xl border-l-4 px-4 py-3 text-sm font-medium">
                    <span class="material-icons-outlined text-xl">leaderboard</span> 
                    <span>排行榜</span>
                    <span class="ml-auto text-xs bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full">#5</span>
                </a>
                 <div class="pt-4 mt-4 border-t border-white/20">
                     <a class="group flex items-center gap-3 rounded-xl px-4 py-3 text-sm font-medium text-slate-500 hover:bg-white/30 hover:text-slate-700 transition-all" href="8-PC-个人中心页.html">
                        <span class="material-icons-outlined text-xl">arrow_back_ios_new</span> 
                        <span>返回个人中心</span>
                    </a>
                </div>
            </nav>
        </aside>

        <main class="ml-72 flex-1 overflow-y-auto">
            <header class="sticky top-0 z-30 glass-strong shadow-2xl border-b border-white/20">
                <div class="flex h-20 items-center justify-between px-8">
                    <div class="flex items-center gap-4">
                        <h1 id="creatorPageTitle" class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">创作者仪表盘</h1>
                        <div class="creator-badge px-4 py-2 rounded-full text-sm font-bold flex items-center gap-2">
                            <span class="material-icons-outlined text-sm">diamond</span>
                            钻石创作者
                        </div>
                    </div>
                    <div class="flex items-center gap-6">
                        <!-- 创作者统计概览 -->
                        <div class="flex items-center gap-4">
                            <div class="text-center">
                                <div class="text-lg font-bold text-indigo-600">Lv.12</div>
                                <div class="text-xs text-slate-500">创作等级</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-green-600">15.6K</div>
                                <div class="text-xs text-slate-500">总粉丝</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-amber-600">8,500</div>
                                <div class="text-xs text-slate-500">本月收益</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuCJ4cxwUDTHBUas9jQUHfn0EOCkO1_hPd39uWb5nGf3MJa9YPWUxexNoApBUfmrvoofIWUEeLCTGpN915lEKGVmRc1mOskvD_PlwWj2ZVIZEcY7QgzgVWXAqG89CUnFWKzhvu_appXzWBQ_Db9hGg-7DVxpUJIe0LBdKD665kvOKWniSTt4LHD3TH13XRqk2ncSqUp9tZIr2i-_VUdXvxBp2QdeygaxyJqQ2QypVaTTOdZXy24NQZNYkv1vbHQMv8m80kpVLHo4aOIm" alt="User Avatar" class="h-12 w-12 rounded-full border-3 border-amber-300 shadow-lg float">
                            <div class="text-right">
                                <p class="text-sm font-semibold text-slate-700">@梦境探索者</p>
                                <p class="text-xs text-slate-500">UID: 123456789</p>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <div class="p-8 space-y-8">
                <!-- 仪表盘 -->
                <div id="dashboardTab" class="tab-content-creator active space-y-8">
                    <!-- 核心数据卡片 -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                        <div class="data-card text-center card-hover">
                            <div class="mb-4">
                                <span class="material-icons-outlined text-5xl text-indigo-500 float">people_alt</span>
                            </div>
                            <div class="stat-number text-4xl mb-2">25</div>
                            <p class="text-sm font-medium text-slate-600">已发布角色</p>
                            <div class="mt-3 w-full bg-slate-200 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: 83%"></div>
                            </div>
                            <p class="text-xs text-slate-500 mt-1">距离下级还需5个</p>
                        </div>

                        <div class="data-card text-center card-hover">
                            <div class="mb-4">
                                <span class="material-icons-outlined text-5xl text-green-500 float">schedule</span>
                            </div>
                            <div class="stat-number text-4xl mb-2">1.3K<span class="text-lg font-normal">h</span></div>
                            <p class="text-sm font-medium text-slate-600">总互动时长</p>
                            <div class="mt-3 flex items-center justify-between text-xs">
                                <span class="text-green-600">+125h 本月</span>
                                <span class="text-slate-500">同比+23%</span>
                            </div>
                        </div>

                        <div class="data-card text-center card-hover">
                            <div class="mb-4">
                                <span class="material-icons-outlined text-5xl text-pink-500 float">favorite</span>
                            </div>
                            <div class="stat-number text-4xl mb-2">15.6K</div>
                            <p class="text-sm font-medium text-slate-600">总粉丝数</p>
                            <div class="mt-3 flex items-center justify-between text-xs">
                                <span class="text-pink-600">+342 本周</span>
                                <span class="text-slate-500">增长率2.2%</span>
                            </div>
                        </div>

                        <div class="earnings-card data-card text-center card-hover">
                            <div class="mb-4">
                                <span class="material-icons-outlined text-5xl float">monetization_on</span>
                            </div>
                            <div class="text-4xl font-bold mb-2">8,500</div>
                            <p class="text-sm font-medium opacity-90">本月预估收益</p>
                            <div class="mt-3 flex items-center justify-between text-xs opacity-80">
                                <span>+15% 环比</span>
                                <span>🚀 TOP 5%</span>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
                        <div class="lg:col-span-2 data-card card-hover">
                            <h3 class="mb-6 text-xl font-bold text-slate-700 flex items-center gap-2">
                                <span class="material-icons-outlined text-indigo-500">trending_up</span>
                                粉丝增长趋势 (近30天)
                            </h3>
                            <canvas id="fansChart" height="300"></canvas>
                        </div>
                        
                        <div class="data-card card-hover">
                            <h3 class="mb-6 text-xl font-bold text-slate-700 flex items-center gap-2">
                                <span class="material-icons-outlined text-green-500">donut_large</span>
                                收入构成 (本月)
                            </h3>
                            <canvas id="earningsPieChart" height="300"></canvas>
                        </div>
                    </div>

                    <!-- 最近动态 -->
                    <div class="data-card card-hover">
                        <h3 class="mb-6 text-xl font-bold text-slate-700 flex items-center gap-2">
                            <span class="material-icons-outlined text-blue-500">notifications</span>
                            最近动态与成就
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center gap-4 p-4 glass rounded-xl">
                                <div class="text-2xl">🎉</div>
                                <div class="flex-1">
                                    <p class="font-semibold text-slate-700">恭喜升级！</p>
                                    <p class="text-sm text-slate-500">您的创作者等级已提升至 Lv.12</p>
                                </div>
                                <span class="text-xs text-slate-400">2小时前</span>
                            </div>
                            <div class="flex items-center gap-4 p-4 glass rounded-xl">
                                <div class="text-2xl">⭐</div>
                                <div class="flex-1">
                                    <p class="font-semibold text-slate-700">角色卡"Aiko"获得1000+收藏</p>
                                    <p class="text-sm text-slate-500">触发里程碑奖励：+500贡献积分</p>
                                </div>
                                <span class="text-xs text-slate-400">6小时前</span>
                            </div>
                            <div class="flex items-center gap-4 p-4 glass rounded-xl">
                                <div class="text-2xl">💎</div>
                                <div class="flex-1">
                                    <p class="font-semibold text-slate-700">本月收益预估已更新</p>
                                    <p class="text-sm text-slate-500">环比增长15%，创历史新高！</p>
                                </div>
                                <span class="text-xs text-slate-400">1天前</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据分析 -->
                <div id="analyticsTab" class="tab-content-creator space-y-8">
                    <div class="data-card card-hover">
                        <h2 class="text-2xl font-bold text-slate-700 mb-6 flex items-center gap-2">
                            <span class="material-icons-outlined text-blue-500">analytics</span>
                            详细数据分析
                        </h2>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="glass p-6 rounded-xl">
                                <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
                                    <span class="material-icons-outlined text-orange-500">timeline</span>
                                    互动热力图
                                </h3>
                                <div class="text-center py-8">
                                    <div class="text-4xl mb-3">📊</div>
                                    <p class="text-slate-500 text-sm">AI数据分析功能开发中</p>
                                    <p class="text-slate-400 text-xs mt-1">将提供用户行为、互动时段等深度洞察</p>
                                </div>
                            </div>
                            <div class="glass p-6 rounded-xl">
                                <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
                                    <span class="material-icons-outlined text-purple-500">person_search</span>
                                    用户画像分析
                                </h3>
                                <div class="text-center py-8">
                                    <div class="text-4xl mb-3">🎯</div>
                                    <p class="text-slate-500 text-sm">用户群体分析功能开发中</p>
                                    <p class="text-slate-400 text-xs mt-1">将展示粉丝年龄、兴趣偏好等维度</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 创作者社区 -->
                <div id="communityTab" class="tab-content-creator space-y-8">
                    <div class="data-card card-hover">
                        <h2 class="text-2xl font-bold text-slate-700 mb-6 flex items-center gap-2">
                            <span class="material-icons-outlined text-green-500">groups</span>
                            创作者社区
                        </h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="glass p-6 rounded-xl text-center">
                                <div class="text-4xl mb-4">💬</div>
                                <h3 class="text-lg font-semibold mb-2">讨论论坛</h3>
                                <p class="text-slate-500 text-sm mb-4">与其他创作者交流经验、分享技巧</p>
                                <button class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-600 transition-colors">
                                    即将开放
                                </button>
                            </div>
                            
                            <div class="glass p-6 rounded-xl text-center">
                                <div class="text-4xl mb-4">🤝</div>
                                <h3 class="text-lg font-semibold mb-2">协作项目</h3>
                                <p class="text-slate-500 text-sm mb-4">参与联合创作，扩大影响力</p>
                                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors">
                                    筹备中
                                </button>
                            </div>
                            
                            <div class="glass p-6 rounded-xl text-center">
                                <div class="text-4xl mb-4">🏆</div>
                                <h3 class="text-lg font-semibold mb-2">创作大赛</h3>
                                <p class="text-slate-500 text-sm mb-4">参与官方举办的创作比赛</p>
                                <button class="bg-purple-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-600 transition-colors">
                                    敬请期待
                                </button>
                            </div>
                            
                            <div class="glass p-6 rounded-xl text-center">
                                <div class="text-4xl mb-4">📚</div>
                                <h3 class="text-lg font-semibold mb-2">学习中心</h3>
                                <p class="text-slate-500 text-sm mb-4">官方教程和创作指南</p>
                                <button class="bg-amber-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-amber-600 transition-colors">
                                    建设中
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="myCreationsTab" class="tab-content-creator space-y-8">
                    <div class="data-card card-hover">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                            <h2 class="text-2xl font-bold text-slate-700 flex items-center gap-2">
                                <span class="material-icons-outlined text-indigo-500">palette</span>
                                我的角色卡作品
                            </h2>
                            <div class="flex items-center gap-3">
                                <select class="glass px-3 py-2 rounded-lg text-sm border-0 focus:ring-2 focus:ring-indigo-500">
                                    <option>全部状态</option>
                                    <option>已发布</option>
                                    <option>审核中</option>
                                    <option>草稿</option>
                                </select>
                                <a href="6-PC-创建编辑角色页.html" class="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-semibold flex items-center gap-2 transition-all duration-300 transform hover:scale-105">
                                    <span class="material-icons-outlined text-sm">add</span>
                                    新建角色
                                </a>
                            </div>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm text-left text-slate-600">
                                <thead class="text-xs text-slate-700 uppercase bg-gradient-to-r from-slate-50 to-slate-100">
                                    <tr>
                                        <th scope="col" class="px-6 py-4 font-semibold">作品</th>
                                        <th scope="col" class="px-6 py-4 font-semibold">状态</th>
                                        <th scope="col" class="px-6 py-4 font-semibold">数据</th>
                                        <th scope="col" class="px-6 py-4 font-semibold">最后编辑</th>
                                        <th scope="col" class="px-6 py-4 font-semibold">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-slate-100">
                                    <tr class="hover:bg-slate-50 transition-colors">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuAOo6oSlAOPgjixfL9uZtkVvrUuw85OvQBaYqRpL0WqReyV0ifl5CEgfNF9ifNMLsM4TIZtwTo6pWrlOOnjNPcx5iYh6UiwTNzwwFPL4EyOQZnc0TnEn7CiQ0u5fMXp3NDbyTHdwldWgZ7IePBfTxPc7Taizxc9ZbMHy3wv0inbJ_vxXWeGRCigsswpy7XY1lKCFuVyaYIClebuRATn4Igfcvgb2fFs7sgJPFrMfksukzLHRtocEyk_HOeqKTNLKiddXCjMNlCTjo07" alt="Aiko" class="w-12 h-12 rounded-lg shadow-md">
                                                <div>
                                                    <p class="font-semibold text-indigo-600 hover:text-indigo-700 cursor-pointer">Aiko</p>
                                                    <p class="text-xs text-slate-500">校园青春AI伙伴</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="inline-flex items-center gap-1 bg-green-100 text-green-700 text-xs font-medium px-2.5 py-1 rounded-full">
                                                <div class="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                                已发布
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="space-y-1">
                                                <div class="flex items-center gap-2 text-xs">
                                                    <span class="material-icons-outlined text-xs text-pink-500">favorite</span>
                                                    <span>5.2K 收藏</span>
                                                </div>
                                                <div class="flex items-center gap-2 text-xs">
                                                    <span class="material-icons-outlined text-xs text-blue-500">chat</span>
                                                    <span>100K+ 互动</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 text-slate-500">2天前</td>
                                        <td class="px-6 py-4">
                                            <div class="list-item-action flex gap-2">
                                                <a href="6-PC-创建编辑角色页.html" class="bg-indigo-50 text-indigo-600 hover:bg-indigo-100">编辑</a>
                                                <button class="bg-blue-50 text-blue-600 hover:bg-blue-100">统计</button>
                                                <button class="bg-slate-100 text-slate-600 hover:bg-slate-200">更多</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-slate-50 transition-colors">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
                                                    <span class="material-icons-outlined text-white text-xl">auto_fix_high</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-indigo-600 hover:text-indigo-700 cursor-pointer">神秘侦探X</p>
                                                    <p class="text-xs text-slate-500">推理解谜专家</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="inline-flex items-center gap-1 bg-yellow-100 text-yellow-700 text-xs font-medium px-2.5 py-1 rounded-full">
                                                <div class="w-1.5 h-1.5 bg-yellow-500 rounded-full animate-pulse"></div>
                                                审核中
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="space-y-1 text-slate-400">
                                                <div class="text-xs">待发布</div>
                                                <div class="text-xs">预期上线：今日</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 text-slate-500">5小时前</td>
                                        <td class="px-6 py-4">
                                            <div class="list-item-action flex gap-2">
                                                <a href="6-PC-创建编辑角色页.html" class="bg-indigo-50 text-indigo-600 hover:bg-indigo-100">编辑</a>
                                                <button class="bg-red-50 text-red-600 hover:bg-red-100">撤回</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div id="draftsTab" class="tab-content-creator"><div class="data-card"><h2 class="text-xl font-semibold text-slate-700">我的草稿箱</h2><p class="mt-2 text-slate-500">暂无草稿。</p></div></div>
                <div id="voiceSamplesTab" class="tab-content-creator"><div class="data-card"><h2 class="text-xl font-semibold text-slate-700">语音样本库</h2><p class="mt-2 text-slate-500">功能开发中，敬请期待。</p></div></div>
                <div id="earningsTab" class="tab-content-creator space-y-8">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div class="earnings-card data-card card-hover">
                            <h3 class="text-lg font-semibold mb-2">当前分成比例</h3>
                            <div class="text-3xl font-bold mb-2">70%</div>
                            <p class="text-sm opacity-80">创作者激励计划</p>
                        </div>
                        
                        <div class="data-card card-hover">
                            <h3 class="text-lg font-semibold mb-2 text-amber-600">本月预估收入</h3>
                            <div class="stat-number text-3xl mb-2">3,500</div>
                            <p class="text-sm text-slate-500">星钻 (+15% 环比)</p>
                        </div>
                        
                        <div class="data-card card-hover">
                            <h3 class="text-lg font-semibold mb-2 text-slate-700">累计已结算</h3>
                            <div class="stat-number text-3xl mb-2">18,200</div>
                            <p class="text-sm text-slate-500">星钻 (总计)</p>
                        </div>
                    </div>
                    
                    <div class="data-card card-hover">
                        <h3 class="text-xl font-bold text-slate-700 mb-6">收入报表与结算记录</h3>
                        <p class="text-slate-500">详细财务数据正在完善中...</p>
                    </div>
                </div>
                <div id="resourcesTab" class="tab-content-creator space-y-8">
                    <div class="data-card card-hover">
                        <h2 class="text-2xl font-bold text-slate-700 mb-6 flex items-center gap-2">
                            <span class="material-icons-outlined text-orange-500">school</span>
                            创作资源中心
                        </h2>
                        <p class="text-slate-500">官方教程、创作指南和社区资源正在整理中...</p>
                    </div>
                </div>
                <div id="leaderboardTab" class="tab-content-creator space-y-8">
                    <div class="data-card card-hover">
                        <h2 class="text-2xl font-bold text-slate-700 mb-6 flex items-center gap-2">
                            <span class="material-icons-outlined text-yellow-500">leaderboard</span>
                            创作者排行榜
                        </h2>
                        <div class="text-center py-8">
                            <div class="text-6xl mb-4">🏆</div>
                            <p class="text-slate-500">排行榜功能开发中，将展示各类创作达人榜单。</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        let fansChart, earningsPieChart;
        const pageTitles = { 
            dashboard: "创作者仪表盘", 
            myCreations: "我的创作管理", 
            analytics: "数据分析中心",
            community: "创作者社区",
            voiceSamples: "语音样本库", 
            earnings: "激励与收益中心", 
            resources: "创作者资源中心", 
            leaderboard: "创作者排行榜" 
        };

        function showCreatorTab(tabName) {
            document.querySelectorAll('.tab-content-creator').forEach(tab => {
                tab.classList.remove('active');
            });
            
            const selectedTab = document.getElementById(tabName + 'Tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
            }
            
            document.querySelectorAll('.creator-hub-sidebar a').forEach(link => {
                link.classList.remove('active');
            });
            const activeLink = document.querySelector(`.creator-hub-sidebar a[onclick*="${tabName}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
            
            const titleElement = document.getElementById('creatorPageTitle');
            if (titleElement) {
                titleElement.textContent = pageTitles[tabName] || "创作者中心";
            }
            
            // 在切换到仪表盘时重新绘制图表
            if (tabName === 'dashboard') {
                setTimeout(() => {
                    initializeCharts();
                }, 100);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            showCreatorTab('dashboard');
            setTimeout(() => {
                initializeCharts();
                animateNumbers();
                animateCards();
            }, 200);
        });

        function initializeCharts() {
            try {
                const fansCtx = document.getElementById('fansChart');
                if (fansCtx) {
                    // 清除旧图表实例以避免重复绘制
                    if (fansChart) {
                        fansChart.destroy();
                    }
                    
                    // 准备渐变背景
                    const ctx = fansCtx.getContext('2d');
                    const gradient = ctx.createLinearGradient(0, 0, 0, 300);
                    gradient.addColorStop(0, 'rgba(99, 102, 241, 0.4)');
                    gradient.addColorStop(1, 'rgba(99, 102, 241, 0.05)');
                    
                    fansChart = new Chart(fansCtx, {
                        type: 'line',
                        data: {
                            labels: ['1日', '5日', '10日', '15日', '20日', '25日', '30日'],
                            datasets: [{
                                label: '粉丝数',
                                data: [13200, 13890, 14300, 14750, 15100, 15400, 15600],
                                borderColor: 'rgba(99, 102, 241, 0.8)',
                                backgroundColor: gradient,
                                tension: 0.4,
                                fill: true,
                                pointBackgroundColor: '#ffffff',
                                pointBorderColor: 'rgba(99, 102, 241, 1)',
                                pointBorderWidth: 2,
                                pointRadius: 4,
                                pointHoverRadius: 6,
                                pointHoverBackgroundColor: '#ffffff',
                                pointHoverBorderColor: 'rgba(139, 92, 246, 1)',
                                pointHoverBorderWidth: 3,
                                borderWidth: 3
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animations: {
                                tension: {
                                    duration: 1000,
                                    easing: 'linear',
                                    from: 0.2,
                                    to: 0.4,
                                    loop: false
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    enabled: true,
                                    mode: 'index',
                                    intersect: false,
                                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                    titleColor: '#1f2937',
                                    bodyColor: '#4b5563',
                                    borderColor: 'rgba(99, 102, 241, 0.3)',
                                    borderWidth: 1,
                                    padding: 12,
                                    cornerRadius: 8,
                                    titleFont: {
                                        size: 14,
                                        weight: 'bold'
                                    },
                                    bodyFont: {
                                        size: 13
                                    },
                                    displayColors: false,
                                    callbacks: {
                                        title: function(tooltipItems) {
                                            return `${tooltipItems[0].label}`;
                                        },
                                        label: function(context) {
                                            return `粉丝数: ${context.raw.toLocaleString()} 人`;
                                        }
                                    }
                                }
                            },
                            interaction: {
                                intersect: false,
                                mode: 'index'
                            },
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.03)',
                                        drawBorder: false
                                    },
                                    border: {
                                        display: false
                                    },
                                    ticks: {
                                        padding: 10,
                                        color: '#64748b',
                                        font: {
                                            size: 11
                                        },
                                        callback: function(value) {
                                            if (value >= 1000) {
                                                return (value / 1000).toFixed(1) + 'K';
                                            }
                                            return value;
                                        }
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false,
                                        drawBorder: false
                                    },
                                    border: {
                                        display: false
                                    },
                                    ticks: {
                                        padding: 10,
                                        color: '#64748b',
                                        font: {
                                            size: 11
                                        }
                                    }
                                }
                            }
                        }
                    });
                }

                const earningsPieCtx = document.getElementById('earningsPieChart');
                if (earningsPieCtx) {
                    // 清除旧图表实例以避免重复绘制
                    if (earningsPieChart) {
                        earningsPieChart.destroy();
                    }
                    
                    earningsPieChart = new Chart(earningsPieCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['角色卡收益', '专属内容', '打赏收入', '推广奖励'],
                            datasets: [{
                                data: [3500, 1200, 800, 500],
                                backgroundColor: [
                                    'rgba(99, 102, 241, 0.9)',
                                    'rgba(139, 92, 246, 0.9)',
                                    'rgba(236, 72, 153, 0.9)',
                                    'rgba(245, 158, 11, 0.9)'
                                ],
                                borderWidth: 2,
                                borderColor: '#ffffff',
                                hoverOffset: 15,
                                hoverBorderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            cutout: '75%',
                            radius: '90%',
                            layout: {
                                padding: 20
                            },
                            animation: {
                                animateRotate: true,
                                animateScale: true
                            },
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        usePointStyle: true,
                                        padding: 20,
                                        font: {
                                            size: 12,
                                            family: "'Inter', 'Noto Sans SC', sans-serif"
                                        },
                                        color: '#64748b',
                                        boxWidth: 8,
                                        boxHeight: 8
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                    titleColor: '#1f2937',
                                    bodyColor: '#4b5563',
                                    borderColor: 'rgba(229, 231, 235, 0.5)',
                                    borderWidth: 1,
                                    cornerRadius: 8,
                                    padding: 12,
                                    boxPadding: 6,
                                    displayColors: true,
                                    usePointStyle: true,
                                    callbacks: {
                                        title: function(tooltipItems) {
                                            return '收益构成';
                                        },
                                        label: function(context) {
                                            const label = context.label || '';
                                            const value = context.raw || 0;
                                            const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                            const percentage = Math.round((value / total) * 100);
                                            return `${label}: ${value} (${percentage}%)`;
                                        }
                                    }
                                }
                            }
                        }
                    });
                    
                    // 添加中心文本
                    const pieChartContainer = earningsPieCtx.parentNode;
                    let centerTextDiv = document.getElementById('pieChartCenterText');
                    if (!centerTextDiv) {
                        centerTextDiv = document.createElement('div');
                        centerTextDiv.id = 'pieChartCenterText';
                        centerTextDiv.style.cssText = `
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            text-align: center;
                            pointer-events: none;
                        `;
                        centerTextDiv.innerHTML = `
                            <div class="text-xl font-bold text-slate-800">6,000</div>
                            <div class="text-xs text-slate-500">总收益</div>
                        `;
                        pieChartContainer.style.position = 'relative';
                        pieChartContainer.appendChild(centerTextDiv);
                    }
                }
            } catch (error) {
                console.error('图表初始化失败:', error);
            }
        }

        function animateNumbers() {
            const numberElements = document.querySelectorAll('.stat-number');
            numberElements.forEach(element => {
                const text = element.textContent;
                const number = parseInt(text.replace(/[^\d]/g, ''));
                const suffix = text.replace(/[\d,]/g, '');
                
                if (number) {
                    const duration = 2000;
                    const startTime = Date.now();
                    
                    function updateNumber() {
                        const elapsed = Date.now() - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        const currentNumber = Math.floor(number * progress);
                        element.textContent = currentNumber.toLocaleString() + suffix;
                        
                        if (progress < 1) {
                            requestAnimationFrame(updateNumber);
                        }
                    }
                    
                    updateNumber();
                }
            });
        }

        function animateCards() {
            const cards = document.querySelectorAll('.card-hover');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        document.addEventListener('click', function(e) {
            if (e.target.closest('.data-card')) {
                const card = e.target.closest('.data-card');
                const ripple = document.createElement('div');
                const rect = card.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(99, 102, 241, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;
                
                card.style.position = 'relative';
                card.style.overflow = 'hidden';
                card.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            }
        });

        // 确保窗口大小调整时重新渲染图表
        window.addEventListener('resize', function() {
            if (document.getElementById('dashboardTab').classList.contains('active')) {
                setTimeout(initializeCharts, 100);
            }
        });

        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            
            #fansChart, #earningsPieChart {
                width: 100% !important;
                height: 300px !important;
                border-radius: 12px;
                padding: 16px;
            }
            
            .chart-container {
                position: relative;
                margin-top: 20px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
                border-radius: 12px;
                transition: all 0.3s ease;
            }
            
            .chart-container:hover {
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                transform: translateY(-5px);
            }
            
            .data-card {
                overflow: visible;
            }
            
            /* 美化图表的标签和标注 */
            .chart-label {
                font-size: 12px;
                color: #64748b;
                margin-bottom: 4px;
            }
            
            /* 动画效果 */
            @keyframes chartFadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            .chart-animation {
                animation: chartFadeIn 0.8s ease-out forwards;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>