<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务中心 - Alphane.ai</title>
    
    <!-- 字体和图标 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        :root {
            /* Alphane品牌色彩 */
            --alphane-primary: #6366f1;
            --alphane-secondary: #8b5cf6;
            --alphane-accent: #ec4899;
            --alphane-warm: #f59e0b;
            --alphane-success: #10b981;
            --alphane-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 动画效果 */
        .hover-lift {
            transition: all 0.3s ease;
        }
        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, var(--alphane-primary), var(--alphane-accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 侧边栏样式 */
        .task-sidebar a.active {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            color: var(--alphane-primary);
            border-left-color: var(--alphane-primary);
            font-weight: 600;
        }
        .task-sidebar a {
            @apply border-transparent hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-600 transition-all duration-200;
        }

        /* Tab按钮样式 */
        .task-tab-button.active {
            background: linear-gradient(135deg, var(--alphane-primary), var(--alphane-secondary));
            color: white;
            border-color: var(--alphane-primary);
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
        .task-tab-button {
            @apply border-gray-300 text-slate-600 hover:text-indigo-600 hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200;
        }

        /* Tab内容 */
        .task-tab-content { 
            display: none; 
            animation: fadeInUp 0.3s ease-out;
        }
        .task-tab-content.active { display: block; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 任务卡片样式 */
        .task-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        /* 任务奖励标签 */
        .task-reward-item {
            @apply flex items-center gap-1.5 text-xs px-3 py-1.5 rounded-full font-medium border shadow-sm;
            transition: all 0.2s ease;
        }
        .task-reward-item:hover {
            transform: scale(1.05);
        }

        /* 代币样式 */
        .token-alphane { 
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #d97706;
            border-color: #f59e0b;
        }
        .token-endora { 
            background: linear-gradient(135deg, #fce7f3, #fbcfe8);
            color: #be185d;
            border-color: #ec4899;
        }
        .token-serotile { 
            background: linear-gradient(135deg, #e6fffa, #ccfbf1);
            color: #0f766e;
            border-color: #14b8a6;
        }
        .token-oxytol { 
            background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
            color: #7c3aed;
            border-color: #8b5cf6;
        }
        .token-exp { 
            background: linear-gradient(135deg, #fef3c7, #fed7aa);
            color: #ea580c;
            border-color: #f97316;
        }

        /* 进度条动画 */
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.5s ease;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
            to { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
        }

        /* 完成状态样式 */
        .task-completed {
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            border-color: #10b981;
        }

        /* 里程碑奖励样式 */
        .milestone-reward {
            transition: all 0.3s ease;
        }
        .milestone-reward:hover {
            transform: scale(1.1);
        }
        .milestone-reward.claimed {
            filter: drop-shadow(0 0 10px #f59e0b);
        }

        /* 倒计时样式 */
        .countdown {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            color: #dc2626;
            font-weight: 600;
            animation: pulse 2s ease-in-out infinite;
        }

        /* 响应式布局 */
        @media (max-width: 1023px) {
            .desktop-sidebar {
                display: none;
            }
            .main-content {
                margin-left: 0 !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- 侧边栏 -->
    <aside class="task-sidebar desktop-sidebar fixed left-0 top-0 z-50 h-full w-80 bg-white shadow-xl border-r border-gray-200">
        <!-- 品牌头部 -->
        <div class="flex items-center justify-between px-6 py-5 border-b border-gray-100">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <span class="text-white text-xl font-bold">A</span>
                </div>
                <div>
                    <h1 class="text-lg font-bold gradient-text">Alphane.ai</h1>
                    <p class="text-xs text-gray-500">有温度的AI伴侣</p>
                </div>
            </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="flex-1 space-y-2 overflow-y-auto px-4 py-6">
            <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4" href="2-PC-首页-最终版.html">
                <span class="material-icons-outlined text-xl">cottage</span> 
                <span>主页</span>
            </a>
            <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4" href="8-PC-个人中心页.html">
                <span class="material-icons-outlined text-xl">account_circle</span> 
                <span>个人中心</span>
            </a>
            
            <div class="pt-4">
                <h3 class="mb-3 px-3 text-xs font-semibold uppercase text-gray-500 tracking-wider">激励与成长</h3>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4 active" href="9-PC-任务中心页.html">
                    <span class="material-icons-outlined text-xl">checklist_rtl</span> 
                    <span>任务中心</span>
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4" href="10-PC-成就徽章页.html">
                    <span class="material-icons-outlined text-xl">emoji_events</span> 
                    <span>成就徽章</span>
                </a>
                <a class="group flex items-center gap-3 rounded-lg px-3 py-3 text-sm font-medium border-l-4" href="11-PC-荣耀战令页.html">
                    <span class="material-icons-outlined text-xl">military_tech</span> 
                    <span>荣耀战令</span>
                </a>
            </div>
        </nav>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content ml-80 overflow-y-auto min-h-screen">
        <!-- 页面头部 -->
        <header class="sticky top-0 z-30 bg-white/90 backdrop-blur-sm border-b border-gray-200 px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold gradient-text">任务中心</h1>
                    <p class="text-gray-600 mt-1">完成任务，赢取丰厚奖励，加速成长！</p>
                </div>
                <div class="countdown px-4 py-2 rounded-lg text-sm">
                    <span class="material-icons-outlined text-base mr-1">schedule</span>
                    日常刷新: <span class="font-bold">23:05:10</span>
                </div>
            </div>
        </header>

        <div class="px-8 py-6">
            <!-- 今日任务概览卡片 -->
            <div class="mb-8 p-6 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-500 text-white shadow-xl relative overflow-hidden">
                <!-- 背景动画 -->
                <div class="absolute inset-0 opacity-20">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16 animate-pulse"></div>
                    <div class="absolute top-1/2 right-0 w-24 h-24 bg-yellow-300 rounded-full translate-x-12 animate-bounce"></div>
                    <div class="absolute bottom-0 left-1/3 w-20 h-20 bg-pink-300 rounded-full translate-y-10 animate-ping"></div>
                </div>
                
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <div class="flex items-center gap-3 mb-2">
                                <h2 class="text-2xl font-bold">今日任务进度</h2>
                                <!-- 连击显示 -->
                                <div class="bg-orange-500 px-3 py-1 rounded-full flex items-center gap-1 animate-pulse">
                                    <span class="text-lg">🔥</span>
                                    <span class="text-sm font-bold">15连击</span>
                                </div>
                            </div>
                            <p class="text-indigo-100">继续保持，你已经超越了80%的用户！</p>
                            <div class="flex items-center gap-4 mt-2">
                                <div class="text-sm text-yellow-200">
                                    🏆 本周排名: <span class="font-bold">#12</span>
                                </div>
                                <div class="text-sm text-green-200">
                                    ⚡ 今日经验: <span class="font-bold">+420 XP</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-5xl font-bold mb-1">8/15</div>
                            <div class="text-indigo-200 text-sm mb-2">已完成</div>
                            <!-- 签到状态 -->
                            <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 transform hover:scale-105">
                                <span class="material-icons-outlined text-sm mr-1">check_circle</span>
                                已签到
                            </button>
                        </div>
                    </div>
                    
                    <!-- 进度条和里程碑 -->
                    <div class="mt-4 bg-white/20 rounded-full h-4 relative overflow-hidden">
                        <div class="bg-gradient-to-r from-yellow-300 to-orange-400 h-4 rounded-full shadow-lg transition-all duration-1000 ease-out" style="width: 53%"></div>
                        <!-- 里程碑标记 -->
                        <div class="absolute top-0 left-0 w-full h-full flex items-center justify-between px-2">
                            <div class="w-2 h-2 bg-yellow-300 rounded-full"></div>
                            <div class="w-2 h-2 bg-white rounded-full opacity-50"></div>
                            <div class="w-2 h-2 bg-white rounded-full opacity-50"></div>
                        </div>
                    </div>
                    
                    <div class="mt-3 flex justify-between items-center text-sm">
                        <div class="flex items-center gap-4">
                            <span class="text-indigo-100">下个里程碑: 10个任务</span>
                            <span class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full font-bold">+150 曦光微尘</span>
                        </div>
                        <button class="text-yellow-200 hover:text-yellow-100 text-sm underline">
                            查看排行榜 →
                        </button>
                    </div>
                </div>
            </div>

            <!-- 每日签到和快捷功能区 -->
            <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 每日签到 -->
                <div class="bg-white rounded-xl p-4 shadow-lg hover-lift">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800 flex items-center gap-2">
                            <span class="material-icons-outlined text-blue-600">event_available</span>
                            每日签到
                        </h3>
                        <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full font-medium">连续15天</span>
                    </div>
                    <div class="grid grid-cols-7 gap-1 mb-3">
                        <div class="w-6 h-6 bg-green-500 rounded text-white text-xs flex items-center justify-center">1</div>
                        <div class="w-6 h-6 bg-green-500 rounded text-white text-xs flex items-center justify-center">2</div>
                        <div class="w-6 h-6 bg-green-500 rounded text-white text-xs flex items-center justify-center">3</div>
                        <div class="w-6 h-6 bg-green-500 rounded text-white text-xs flex items-center justify-center">4</div>
                        <div class="w-6 h-6 bg-green-500 rounded text-white text-xs flex items-center justify-center">5</div>
                        <div class="w-6 h-6 bg-green-500 rounded text-white text-xs flex items-center justify-center">6</div>
                        <div class="w-6 h-6 bg-yellow-400 rounded text-white text-xs flex items-center justify-center animate-pulse">7</div>
                    </div>
                    <p class="text-xs text-gray-500">明日奖励: +200 曦光微尘</p>
                </div>
                
                <!-- 好友排行榜 -->
                <div class="bg-white rounded-xl p-4 shadow-lg hover-lift">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800 flex items-center gap-2">
                            <span class="material-icons-outlined text-purple-600">leaderboard</span>
                            好友排行
                        </h3>
                        <span class="text-xs text-purple-600 font-medium">#12</span>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center gap-2 text-xs">
                            <span class="w-4 text-center">🥇</span>
                            <span class="flex-1">星光小仙女</span>
                            <span class="text-yellow-600 font-bold">2,340</span>
                        </div>
                        <div class="flex items-center gap-2 text-xs">
                            <span class="w-4 text-center">🥈</span>
                            <span class="flex-1">温柔的风</span>
                            <span class="text-gray-600 font-bold">1,890</span>
                        </div>
                        <div class="flex items-center gap-2 text-xs">
                            <span class="w-4 text-center">🥉</span>
                            <span class="flex-1">夜空守望者</span>
                            <span class="text-orange-600 font-bold">1,420</span>
                        </div>
                    </div>
                </div>
                
                <!-- 限时加成 -->
                <div class="bg-gradient-to-br from-orange-400 to-red-500 rounded-xl p-4 shadow-lg text-white">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold flex items-center gap-2">
                            <span class="material-icons-outlined">local_fire_department</span>
                            限时双倍
                        </h3>
                        <span class="countdown-timer px-2 py-1 rounded text-xs font-bold bg-white/20">02:15:30</span>
                    </div>
                    <p class="text-sm opacity-90 mb-2">所有任务奖励翻倍！</p>
                    <div class="flex items-center gap-2">
                        <div class="flex-1 bg-white/20 rounded-full h-2">
                            <div class="bg-yellow-300 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                        <span class="text-xs">剩余45%</span>
                    </div>
                </div>
            </div>

            <!-- 任务标签导航 -->
            <div class="mb-6 bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <nav class="flex" aria-label="Tabs">
                    <button onclick="showTaskTab('daily')" class="task-tab-button active flex-1 px-6 py-4 text-sm font-medium border-b-2 rounded-tl-2xl">
                        <span class="material-icons-outlined mr-2">today</span>
                        每日任务
                    </button>
                    <button onclick="showTaskTab('weekly')" class="task-tab-button flex-1 px-6 py-4 text-sm font-medium border-b-2">
                        <span class="material-icons-outlined mr-2">view_week</span>
                        每周任务
                    </button>
                    <button onclick="showTaskTab('monthly')" class="task-tab-button flex-1 px-6 py-4 text-sm font-medium border-b-2">
                        <span class="material-icons-outlined mr-2">calendar_month</span>
                        每月任务
                    </button>
                    <button onclick="showTaskTab('event')" class="task-tab-button flex-1 px-6 py-4 text-sm font-medium border-b-2 rounded-tr-2xl">
                        <span class="material-icons-outlined mr-2">star</span>
                        限时活动
                    </button>
                </nav>
            </div>

            <!-- 任务内容区域 -->
            <div class="space-y-6">
                <!-- 每日任务 -->
                <div id="dailyTab" class="task-tab-content active space-y-4">
                    <!-- 已完成任务 -->
                    <div class="task-card task-completed rounded-2xl p-6 hover-lift relative overflow-hidden">
                        <!-- 完成特效 -->
                        <div class="absolute top-2 right-2">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center animate-bounce">
                                <span class="material-icons-outlined text-white text-sm">done</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center relative">
                                    <span class="material-icons-outlined text-green-600 text-xl">done</span>
                                    <!-- 光圈效果 -->
                                    <div class="absolute inset-0 bg-green-400 rounded-full animate-ping opacity-30"></div>
                                </div>
                                <div>
                                    <div class="flex items-center gap-2 mb-1">
                                        <h3 class="text-lg font-semibold text-gray-800">晨间问候</h3>
                                        <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium">+15经验连击</span>
                                    </div>
                                    <p class="text-gray-600 text-sm">每日首次与任意AI角色发起对话</p>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        <span class="task-reward-item token-alphane">
                                            <span class="material-icons-outlined text-sm">flare</span> 
                                            100 曦光微尘 (x2)
                                        </span>
                                        <span class="task-reward-item token-exp">
                                            <span class="material-icons-outlined text-sm">local_fire_department</span> 
                                            20 经验 (x2)
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl text-green-500 mb-2 animate-bounce">✨</div>
                                <button class="px-4 py-2 bg-green-500 text-white text-sm font-medium rounded-lg opacity-70 cursor-not-allowed" disabled>
                                    已领取
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 进行中任务 -->
                    <div class="task-card rounded-2xl p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="material-icons-outlined text-purple-600 text-xl">psychology</span>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800">记忆印刻</h3>
                                    <p class="text-gray-600 text-sm">今日向"AI记忆胶囊"主动存入1条新记忆</p>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        <span class="task-reward-item token-alphane">
                                            <span class="material-icons-outlined text-sm">flare</span> 
                                            80 曦光微尘
                                        </span>
                                        <span class="task-reward-item token-serotile">
                                            <span class="material-icons-outlined text-sm">extension</span> 
                                            1 拼图碎片
                                        </span>
                                        <span class="task-reward-item token-exp">
                                            <span class="material-icons-outlined text-sm">local_fire_department</span> 
                                            15 经验
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center min-w-[120px]">
                                <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                    <div class="progress-bar h-2 rounded-full" style="width: 0%"></div>
                                </div>
                                <p class="text-sm text-gray-500 mb-2">0/1</p>
                                <button class="px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-200">
                                    前往完成
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 可领取任务 -->
                    <div class="task-card rounded-2xl p-6 hover-lift border-2 border-yellow-300 bg-gradient-to-br from-yellow-50 to-orange-50 relative overflow-hidden">
                        <!-- 闪烁效果 -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                        
                        <div class="relative z-10 flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center relative">
                                    <span class="material-icons-outlined text-yellow-600 text-xl">mood</span>
                                    <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-ping"></div>
                                    <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                        <span class="text-white text-xs">!</span>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex items-center gap-2 mb-1">
                                        <h3 class="text-lg font-semibold text-gray-800">每日惊喜时刻：分享心情</h3>
                                        <span class="bg-yellow-200 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium animate-pulse">可领取!</span>
                                    </div>
                                    <p class="text-gray-600 text-sm">与AI角色分享你今天的心情（开心/难过/平静等）</p>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        <span class="task-reward-item token-endora">
                                            <span class="material-icons-outlined text-sm">favorite</span> 
                                            10 心悦晶石 (x2)
                                        </span>
                                        <span class="task-reward-item token-oxytol">
                                            <span class="material-icons-outlined text-sm">water_drop</span> 
                                            20 羁绊之露 (x2)
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center min-w-[120px]">
                                <div class="w-full bg-yellow-200 rounded-full h-2 mb-3 overflow-hidden">
                                    <div class="bg-gradient-to-r from-yellow-500 to-orange-500 h-2 rounded-full animate-pulse shadow-lg" style="width: 100%"></div>
                                </div>
                                <p class="text-sm text-yellow-700 mb-2 font-medium">1/1</p>
                                <button onclick="claimReward(this)" class="px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-sm font-medium rounded-lg hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 animate-pulse transform hover:scale-105 shadow-lg">
                                    领取奖励
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 里程碑奖励 -->
                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-700 mb-4 text-center">今日进度里程碑</h4>
                        <div class="flex justify-center items-center space-x-8">
                            <div class="milestone-reward text-center opacity-50">
                                <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-2">
                                    <span class="material-icons-outlined text-gray-400 text-2xl">card_giftcard</span>
                                </div>
                                <p class="text-sm font-medium text-gray-500">5个任务</p>
                                <p class="text-xs text-gray-400">+100 微尘</p>
                            </div>
                            <div class="milestone-reward text-center claimed">
                                <div class="w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mb-2 shadow-lg">
                                    <span class="material-icons-outlined text-white text-3xl">redeem</span>
                                </div>
                                <p class="text-sm font-semibold text-yellow-600">10个任务</p>
                                <p class="text-xs text-yellow-500 font-medium">已领取</p>
                            </div>
                            <div class="milestone-reward text-center opacity-70">
                                <div class="w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mb-2">
                                    <span class="material-icons-outlined text-purple-500 text-2xl">workspace_premium</span>
                                </div>
                                <p class="text-sm font-medium text-purple-600">15个任务</p>
                                <p class="text-xs text-purple-500">+5 心悦晶石</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 每周任务 -->
                <div id="weeklyTab" class="task-tab-content space-y-4">
                    <div class="task-card rounded-2xl p-6 hover-lift">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="material-icons-outlined text-blue-600 text-xl">groups</span>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800">灵魂沟通者</h3>
                                    <p class="text-gray-600 text-sm">本周与至少3个AI角色亲密度各提升1级</p>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        <span class="task-reward-item token-endora">
                                            <span class="material-icons-outlined text-sm">favorite</span> 
                                            50 心悦晶石
                                        </span>
                                        <span class="task-reward-item token-oxytol">
                                            <span class="material-icons-outlined text-sm">water_drop</span> 
                                            100 羁绊之露
                                        </span>
                                        <span class="task-reward-item token-exp">
                                            <span class="material-icons-outlined text-sm">local_fire_department</span> 
                                            200 经验
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center min-w-[120px]">
                                <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                    <div class="progress-bar h-2 rounded-full" style="width: 66%"></div>
                                </div>
                                <p class="text-sm text-gray-500 mb-2">2/3</p>
                                <button class="px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-200">
                                    前往完成
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center py-12">
                        <div class="w-24 h-24 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="material-icons-outlined text-purple-500 text-4xl">schedule</span>
                        </div>
                        <p class="text-gray-500 text-lg">更多周常任务敬请期待！</p>
                    </div>
                </div>

                <!-- 每月任务 -->
                <div id="monthlyTab" class="task-tab-content">
                    <div class="text-center py-16">
                        <div class="w-32 h-32 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="material-icons-outlined text-indigo-500 text-6xl">calendar_month</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">月常任务即将来临</h3>
                        <p class="text-gray-500">月常任务将在每月初更新，敬请期待！</p>
                    </div>
                </div>

                <!-- 限时活动 -->
                <div id="eventTab" class="task-tab-content">
                    <div class="text-center py-16">
                        <div class="w-32 h-32 bg-gradient-to-br from-yellow-100 to-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="material-icons-outlined text-yellow-500 text-6xl">celebration</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">当前暂无限时活动</h3>
                        <p class="text-gray-500">精彩活动即将上线，请持续关注！</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function showTaskTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.task-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + 'Tab').classList.add('active');
            
            // 更新按钮状态
            document.querySelectorAll('.task-tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // 激活当前按钮
            event.target.classList.add('active');
        }

        // 页面加载时默认显示每日任务
        document.addEventListener('DOMContentLoaded', () => {
            showTaskTab('daily');
        });

        // 实时倒计时
        function updateCountdown() {
            const now = new Date();
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            
            const diff = tomorrow - now;
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            const countdownElement = document.querySelector('.countdown span:last-child');
            if (countdownElement) {
                countdownElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // 每秒更新倒计时
        setInterval(updateCountdown, 1000);
        updateCountdown();

        // 领取奖励动画效果
        function claimReward(button) {
            // 防止重复点击
            button.disabled = true;
            
            // 播放领取动画
            const card = button.closest('.task-card');
            
            // 创建庆祝粒子效果
            createCelebrationEffect(button);
            
            // 更新按钮状态
            setTimeout(() => {
                button.textContent = '已领取';
                button.className = 'px-4 py-2 bg-green-500 text-white text-sm font-medium rounded-lg opacity-70 cursor-not-allowed';
                
                // 更新卡片状态
                card.className = 'task-card task-completed rounded-2xl p-6 hover-lift';
                
                // 显示奖励获得提示
                showRewardNotification(['10 心悦晶石 (x2)', '20 羁绊之露 (x2)']);
                
                // 更新连击数
                updateStreakCounter();
                
            }, 1000);
        }

        // 创建庆祝效果
        function createCelebrationEffect(element) {
            const rect = element.getBoundingClientRect();
            const particles = [];
            
            // 创建多个粒子
            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: fixed;
                    width: 8px;
                    height: 8px;
                    background: ${['#f59e0b', '#ec4899', '#8b5cf6', '#10b981'][Math.floor(Math.random() * 4)]};
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                    left: ${rect.left + rect.width/2}px;
                    top: ${rect.top + rect.height/2}px;
                `;
                
                document.body.appendChild(particle);
                
                // 动画
                const angle = (i / 15) * 2 * Math.PI;
                const velocity = 50 + Math.random() * 50;
                const vx = Math.cos(angle) * velocity;
                const vy = Math.sin(angle) * velocity - 50;
                
                let x = rect.left + rect.width/2;
                let y = rect.top + rect.height/2;
                let opacity = 1;
                
                const animate = () => {
                    x += vx * 0.02;
                    y += vy * 0.02;
                    vy += 2; // 重力
                    opacity -= 0.02;
                    
                    particle.style.left = x + 'px';
                    particle.style.top = y + 'px';
                    particle.style.opacity = opacity;
                    
                    if (opacity > 0) {
                        requestAnimationFrame(animate);
                    } else {
                        document.body.removeChild(particle);
                    }
                };
                
                requestAnimationFrame(animate);
            }
        }

        // 显示奖励通知
        function showRewardNotification(rewards) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 16px 24px;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                z-index: 1000;
                font-weight: 600;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            
            notification.innerHTML = `
                <div class="flex items-center gap-3">
                    <span class="text-2xl">🎉</span>
                    <div>
                        <div class="font-bold">任务完成！</div>
                        <div class="text-sm opacity-90">${rewards.join(', ')}</div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动消失
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 更新连击计数器
        function updateStreakCounter() {
            const streakElement = document.querySelector('.bg-orange-500 span:last-child');
            if (streakElement) {
                const currentStreak = parseInt(streakElement.textContent.match(/\d+/)[0]);
                streakElement.textContent = `${currentStreak + 1}连击`;
            }
        }
    </script>
</body>
</html>