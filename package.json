{"name": "prompt-pro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@types/negotiator": "^0.6.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "i18next-resources-to-backend": "^1.2.1", "lucide-react": "^0.509.0", "negotiator": "^1.0.0", "next": "^15.2.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-cookie": "^8.0.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.2", "react-responsive": "^10.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}