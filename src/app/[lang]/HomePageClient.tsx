'use client';

import { useTranslation } from '@/app/i18n/client';
import MomentCard from '@/components/MomentCard';
import MainAppLayout from '@/components/MainAppLayout';

export default function HomePageClient({ allSampleMoments, lang }: { allSampleMoments: any[]; lang: string }) {
  const { t } = useTranslation(lang, 'translation');

  return (
    <MainAppLayout lang={lang}>
      <div className="w-full bg-background text-foreground theme-transition p-4 sm:p-6 lg:p-8">
        <div 
          className="columns-2 md:columns-3 lg:columns-4 xl:columns-5 2xl:columns-5 3xl:columns-5 gap-6 space-y-6"
        >
          {allSampleMoments.map((moment, index) => (
            <div key={index} className="break-inside-avoid">
              <MomentCard
                lang={lang}
                character={moment.character}
                moment={moment.moment}
                storyTemplateId={moment.storyTemplateId}
                stats={moment.stats}
                aspectRatio={moment.aspectRatio}
                publishedAt={moment.publishedAt}
              />
            </div>
          ))}
        </div>
      </div>
    </MainAppLayout>
  );
} 