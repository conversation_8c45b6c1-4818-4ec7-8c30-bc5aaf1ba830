'use client';

import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Brain } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import PageLayout from '@/components/PageLayout';
import { useTranslation } from '@/app/i18n/client';

export default function PlaceholderPage() {
  const { lang, slug } = useParams() as { lang: string, slug: string };
  const { t } = useTranslation(lang, 'translation');

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-white dark:bg-gray-900 theme-transition">
        <PageLayout title="">
          <div className="text-center space-y-8 py-12">
            {/* Brain Icon + Error Symbol */}
            <div className="flex justify-center mb-8">
              <div className="relative">
                <Brain 
                  size={80} 
                  className="text-primary theme-transition" 
                />
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">!</span>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="space-y-6">
              <div className="space-y-2">
                <h1 className="text-6xl font-bold text-foreground theme-transition">
                  {t('placeholderPage.mind')}<span className="text-red-500">{t('placeholderPage.lost')}</span>
                </h1>
                <div className="text-lg text-foreground/70 theme-transition">
                  <p>{t('placeholderPage.oops')}</p>
                </div>
              </div>
              
            </div>

            {/* Action Button */}
            <div className="pt-4">
              <Link
                href={`/${lang}/mindstorm`}
                className="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 dark:bg-primary dark:hover:bg-primary/90 text-white dark:!text-black font-semibold rounded-lg text-lg transition-all duration-300 theme-transition hover:scale-105 hover:shadow-lg"
              >
                <Brain className="w-5 h-5 mr-2" />
                {t('placeholderPage.mindstormAgain')}
              </Link>
            </div>

          </div>
        </PageLayout>
      </div>
    </MainAppLayout>
  );
}
