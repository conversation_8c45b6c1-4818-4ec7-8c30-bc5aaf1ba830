'use client';

import { useEffect, useState } from 'react';

function cx(...classes: (string | false | undefined | null)[]) {
  return classes.filter(Boolean).join(' ');
}

interface AuthOverlayProps {
  lang: string;
}

type Mode = 'login' | 'register';

const AuthOverlay: React.FC<AuthOverlayProps> = ({ lang }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [mode, setMode] = useState<Mode>('login');

  useEffect(() => {
    // trigger slide-in after mount (client side)
    const id = requestAnimationFrame(() => setIsVisible(true));
    return () => cancelAnimationFrame(id);
  }, []);

  const toggleMode = () => setMode((m) => (m === 'login' ? 'register' : 'login'));

  return (
    <div
      className={cx(
        'fixed inset-0 z-50 overflow-y-auto flex justify-end bg-black/40 backdrop-blur-sm transition-opacity',
        isVisible ? 'opacity-100' : 'opacity-0'
      )}
    >
      {/* Sliding panel */}
      <div
        className={cx(
          'relative w-full sm:max-w-md h-full bg-white dark:bg-gray-900 shadow-xl transform transition-transform duration-300 ease-out flex flex-col',
          isVisible ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        {/* Header */}
        <header className="flex items-center justify-between px-6 py-4 border-b border-gray-100 dark:border-gray-800">
          <h1 className="text-lg font-semibold text-gray-800 dark:text-gray-100">{mode === 'login' ? 'Sign in to Alphane.ai' : 'Create your Alphane.ai account'}</h1>
          {/* Switch mode */}
          <button
            onClick={toggleMode}
            className="text-sm font-medium text-indigo-600 hover:text-indigo-500 transition-colors"
          >
            {mode === 'login' ? 'Need an account?' : 'Already have an account?'}
          </button>
        </header>

        {/* Content */}
        <main className="flex-1 px-6 py-8">
          {mode === 'login' ? <LoginForm onSwitchToRegister={toggleMode} /> : <RegisterForm />}
        </main>

        {/* Footer */}
        <footer className="px-6 py-4 text-center text-xs text-gray-500 dark:text-gray-400 border-t border-gray-100 dark:border-gray-800">
          By continuing, you agree to our <a href={`/${lang}/terms`} className="underline">Terms of Service</a> and <a href={`/${lang}/privacy`} className="underline">Privacy Policy</a>.
        </footer>
      </div>
    </div>
  );
};

export default AuthOverlay;

/* ------------------------------------------------------------------ */

const inputBase =
  'w-full h-11 px-3 border-2 border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:outline-none focus:border-indigo-500 dark:bg-gray-800 dark:text-gray-100 transition-colors placeholder-gray-400 dark:placeholder-gray-500';

function LoginForm({ onSwitchToRegister }: { onSwitchToRegister: () => void }) {
  return (
    <form className="space-y-5">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="email">
          Email or Phone
        </label>
        <input id="email" type="text" placeholder="<EMAIL>" className={inputBase} required />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="password">
          Password / OTP
        </label>
        <input id="password" type="password" placeholder="********" className={inputBase} required />
      </div>

      <div className="flex items-center justify-between">
        <label className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
          <input type="checkbox" className="h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500" /> Remember me
        </label>
        <a href="#" className="text-xs text-indigo-600 hover:text-indigo-500 font-medium">
          Forgot password?
        </a>
      </div>

      <button
        type="submit"
        className="w-full h-11 bg-indigo-600 hover:bg-indigo-500 text-white font-semibold rounded-lg shadow-md transition-colors"
      >
        Continue
      </button>

      <div className="flex items-center my-3">
        <span className="flex-1 h-px bg-gray-200 dark:bg-gray-800" />
        <span className="px-3 text-xs text-gray-500 dark:text-gray-400">OR</span>
        <span className="flex-1 h-px bg-gray-200 dark:bg-gray-800" />
      </div>

      <button
        type="button"
        className="w-full h-11 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-200 font-semibold rounded-lg flex items-center justify-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="w-5 h-5" aria-hidden="true">
          <path fill="#EA4335" d="M24 9.5c3.83 0 7.25 1.47 9.87 3.84l7.29-7.29C36.8 2.1 30.73 0 24 0 14.68 0 6.42 4.92 2.18 12.18l8.47 6.59C12.52 12.54 17.71 9.5 24 9.5z" />
          <path fill="#34A853" d="M46.55 24.52c0-1.56-.14-2.69-.44-3.86H24v7.29h12.68c-.25 2.1-1.67 5.27-4.79 7.41l7.36 5.71c4.36-4.07 6.3-10.06 6.3-16.55z" />
          <path fill="#4A90E2" d="M10.47 27.21l-8.7 6.71C4.37 41.65 13.64 48 24 48c6.73 0 12.8-2.2 17.56-6.01l-8.47-6.59c-2.27 1.6-5.24 2.55-9.09 2.55-6.3 0-11.9-4.16-13.89-9.74z" />
          <path fill="#FBBC05" d="M2.18 12.18L10.6 18.8C12.52 12.54 17.71 9.5 24 9.5c3.83 0 7.25 1.47 9.87 3.84l7.29-7.29C36.8 2.1 30.73 0 24 0 14.68 0 6.42 4.92 2.18 12.18z" opacity=".15" />
        </svg>
        Continue with Google
      </button>

      <p className="text-center text-sm mt-6 text-gray-600 dark:text-gray-400">
        Don&apos;t have an account?{' '}
        <button
          type="button"
          onClick={onSwitchToRegister}
          className="text-indigo-600 hover:text-indigo-500 font-semibold"
        >
          Sign up
        </button>
      </p>
    </form>
  );
}

function RegisterForm() {
  return (
    <form className="space-y-5">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="email">
          Email or Phone
        </label>
        <input id="email" type="text" placeholder="<EMAIL>" className={inputBase} required />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="otp">
          Verification Code (OTP)
        </label>
        <div className="flex rounded-lg border-2 border-gray-200 dark:border-gray-700 overflow-hidden">
          <input id="otp" type="text" className={`${inputBase} border-0 rounded-none flex-1`} placeholder="123456" required />
          <button type="button" className="px-4 text-sm font-semibold bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
            Get Code
          </button>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="password">
          Password
        </label>
        <input id="password" type="password" placeholder="At least 8 characters" className={inputBase} required />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="confirm">
          Confirm Password
        </label>
        <input id="confirm" type="password" placeholder="Repeat password" className={inputBase} required />
      </div>

      <div className="flex items-start gap-2 text-xs text-gray-600 dark:text-gray-400">
        <input type="checkbox" className="mt-1 h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500" required />
        <span>
          I agree to the <a href="#" className="underline">Terms of Service</a> and <a href="#" className="underline">Privacy Policy</a>.
        </span>
      </div>

      <button
        type="submit"
        className="w-full h-11 bg-indigo-600 hover:bg-indigo-500 text-white font-semibold rounded-lg shadow-md transition-colors"
      >
        Sign Up
      </button>
    </form>
  );
} 