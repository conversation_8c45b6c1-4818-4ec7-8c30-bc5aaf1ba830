'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  MessageCircle,
  Heart,
  Users,
  MessageSquare,
  TrendingUp,
  BrainCircuit,
  Sparkles,
  Gift,
  Check,
  Clock,
  X,
  BookOpen,
  Image as ImageIcon,
  PlusCircle
} from 'lucide-react';
import type { Character } from '@/lib/mock-data';
import Memory, { MemoryMessage } from '@/components/Memory';
import MemoryRow from '@/components/MemoryRow';
import BottomNavBar from '@/components/BottomNavBar';

// Component Props
interface CharacterProfileProps {
  character: Character;
  lang: string;
}

const TABS = ['memories', 'badges'] as const;
type Tab = (typeof TABS)[number];

// Mock data for memories, this would typically come from a service
// --- MOCK DATA ---
const mockMemoryMessages1: MemoryMessage[] = [
    { id: 1, side: 'left', avatar: 'https://i.pinimg.com/564x/e7/7a/74/e77a7405232d2c1c107629577a83a48e.jpg', name: '<PERSON><PERSON><PERSON>', text: 'That time we explored the glowing caves and found the crystal that hums a forgotten melody.', timestamp: '3 days ago' },
    { id: 2, side: 'right', avatar: 'https://i.pravatar.cc/40?u=user', name: 'Me', text: "It was magical! I still hear the song in my dreams sometimes. ✨", timestamp: '3 days ago' },
];
const mockMemoryMessages2: MemoryMessage[] = [
    { id: 1, side: 'left', avatar: 'https://i.pinimg.com/564x/e7/7a/74/e77a7405232d2c1c107629577a83a48e.jpg', name: 'Seraphina', text: 'You told me you were a little nervous about your big presentation, but I knew you would be amazing.', timestamp: 'Yesterday' },
    { id: 2, side: 'right', avatar: 'https://i.pravatar.cc/40?u=user', name: 'Me', text: "Thanks for the encouragement! It made all the difference.", timestamp: 'Yesterday' },
];
const characterMemories = [
    { id: 'mem1', messages: mockMemoryMessages1 },
    { id: 'mem2', messages: mockMemoryMessages2 },
];

// --- SUB-COMPONENTS ---

const FloatingMemory = ({ memory, anchorEl, onClose }: { memory: { messages: MemoryMessage[] }, anchorEl: HTMLElement, onClose: () => void }) => {
    const memoryRef = useRef<HTMLDivElement>(null);
    const [style, setStyle] = useState({});

    useEffect(() => {
        if (anchorEl && memoryRef.current) {
            const anchorRect = anchorEl.getBoundingClientRect();
            const memRect = memoryRef.current.getBoundingClientRect();
            const viewportW = window.innerWidth;
            const viewportH = window.innerHeight;

            let top = anchorRect.top;
            if (top + memRect.height > viewportH - 20) {
                top = viewportH - memRect.height - 20;
            }

            let left = anchorRect.right + 16;
            if (left + memRect.width > viewportW - 20) {
                left = anchorRect.left - memRect.width - 16;
            }
            
            if (left < 0) left = 20;

            setStyle({ top: `${top}px`, left: `${left}px` });
        }
    }, [anchorEl]);

    return (
        <div className="fixed inset-0 z-[100] bg-black/20 backdrop-blur-sm animate-fade-in" onClick={onClose}>
            <div
                ref={memoryRef}
                style={style}
                className="absolute w-[380px] max-h-[450px] overflow-y-auto rounded-xl shadow-2xl animate-fade-in"
                onClick={e => e.stopPropagation()}
            >
                <Memory messages={memory.messages} />
            </div>
        </div>
    );
};

const CharacterHeader = ({ character, lang }: { character: Character; lang: string }) => {
  const [isFollowed, setIsFollowed] = useState(false);
  const [followerCount, setFollowerCount] = useState(10000);
  const [activeDrawer, setActiveDrawer] = useState<string | null>(null);

  const handleFollow = () => {
    setIsFollowed(!isFollowed);
    setFollowerCount(isFollowed ? followerCount - 1 : followerCount + 1);
  };
  
  const formatFollowers = (count: number) => {
      if (count >= 1000) {
          return `${(count / 1000).toFixed(1)}K`;
      }
      return count.toString();
  };

  return (
    <div className="glass-strong rounded-3xl overflow-hidden card-hover shadow-2xl dark:shadow-black/20">
      <div className="relative h-64 bg-cover bg-center" style={{ backgroundImage: `url(${character.character_bg_image})` }}>
        <div className="absolute -bottom-16 left-8 flex items-center gap-6">
          <div className="relative float">
            <div className="bond-ring" style={{ '--bond-angle': '225deg' } as React.CSSProperties}></div>
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <Image src={character.character_avatar} alt={character.name} width={104} height={104} className="w-[104px] h-[104px] rounded-full border-4 border-white dark:border-slate-900 shadow-xl" unoptimized/>
              <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-pink-500 to-rose-500 text-white text-xs px-3 py-1 rounded-full font-bold border-2 border-white dark:border-slate-900 shadow-lg">Lv.5</div>
            </div>
          </div>
          <div className="glass-strong rounded-2xl p-4 shadow-xl mt-16">
            <div className="flex items-center gap-2 mb-1"><span className="text-pink-500 text-xl">💖</span><h3 className="text-lg font-bold gradient-text">Confidant</h3></div>
            <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">1,250 / 2,000 XP</p>
            <div className="w-32 h-2 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden"><div className="h-full bg-gradient-to-r from-pink-500 to-rose-500 rounded-full progress-bar-animated" style={{ width: '62.5%' }}></div></div>
          </div>
        </div>
      </div>
      <div className="pt-20 px-8 pb-8 space-y-6">
        <div className="space-y-3">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-3 min-w-0">
              <h1 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-slate-50 truncate">{character.name}</h1>
              <div className="flex items-center gap-1">
                <span className="achievement rare text-2xl cursor-pointer" title="Rare Achievement">⭐</span>
                <span className="achievement text-xl cursor-pointer" title="Standard Achievement">🏆</span>
              </div>
            </div>
            <button onClick={handleFollow} className={`glass flex items-center gap-2 px-3 sm:px-4 py-2 font-semibold rounded-2xl hover:bg-white/30 dark:hover:bg-slate-800/50 transition-all shrink-0 ${isFollowed ? 'text-pink-600 dark:text-pink-400' : 'text-slate-700 dark:text-slate-200'}`}>
              <Heart size={20} className={`transition-all ${isFollowed ? 'fill-current text-pink-500' : ''}`} />
              <span className="sm:hidden text-xs font-bold">{formatFollowers(followerCount)}</span>
              <span className="hidden sm:inline text-sm">{formatFollowers(followerCount)} Followers</span>
            </button>
          </div>
          <div className="flex gap-2 flex-nowrap overflow-x-auto pb-2 scrollbar-thin">
              {['Cheerful', 'Lively', 'Kind', 'Adventurous', 'Curious'].map(trait => (
                <span key={trait} className="flex items-center px-2 py-0.5 text-xs bg-slate-200/50 dark:bg-slate-700/50 text-slate-600 dark:text-slate-300 rounded-full font-medium whitespace-nowrap">{trait}</span>
              ))}
          </div>
          <p className="text-slate-600 dark:text-slate-400 font-medium pt-1">Creator: <a className="text-indigo-500 hover:text-indigo-600 font-semibold" href="#">@Sophia</a></p>
        </div>

        <p className="text-slate-600 dark:text-slate-400 text-sm leading-relaxed">
          Aiko is a vibrant and curious girl, full of childlike innocence and enthusiasm. She loves exploring the unknown, making new friends, and inspiring everyone around her with her optimism and kindness.
        </p>

        <div className="flex flex-row gap-3 pt-4 border-t border-slate-200/50 dark:border-slate-800/50">
          <div className="relative flex-1" onMouseEnter={() => setActiveDrawer('chat')} onMouseLeave={() => setActiveDrawer(null)}>
            <Link href={`/${lang}/chats/${character.id}`} className="btn-glow w-full flex items-center justify-center gap-1.5 px-3 py-2 text-white font-bold rounded-xl text-sm shadow-xl">
              <MessageCircle size={14} /><span>Chat</span>
            </Link>
            {activeDrawer === 'chat' && (
              <div className="absolute bottom-full mb-2 w-full p-3 bg-white dark:bg-slate-800 rounded-xl shadow-lg animate-fade-in-up text-center z-10">
                <p className="text-sm font-bold text-slate-800 dark:text-slate-200">500K+ Chats</p>
              </div>
            )}
          </div>

          <div className="relative flex-1" onMouseEnter={() => setActiveDrawer('chapters')} onMouseLeave={() => setActiveDrawer(null)}>
            <button className="glass w-full flex items-center justify-center gap-1.5 px-3 py-2 text-slate-700 dark:text-slate-200 font-semibold rounded-xl text-sm hover:bg-white/30 dark:hover:bg-slate-800/50 transition-colors">
              <BookOpen size={14} /><span>Chapters</span>
            </button>
            {activeDrawer === 'chapters' && (
              <div className="absolute bottom-full mb-2 w-full p-4 bg-white dark:bg-slate-800 rounded-xl shadow-lg animate-fade-in-up space-y-2 z-10">
                <div className="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-indigo-500 to-purple-500" style={{ width: '60%' }}></div>
                </div>
                <p className="text-xs text-center font-semibold text-slate-600 dark:text-slate-400">3/5 Chapters Read (37K Completed)</p>
              </div>
            )}
          </div>

          <div className="relative flex-1" onMouseEnter={() => setActiveDrawer('moments')} onMouseLeave={() => setActiveDrawer(null)}>
            <button className="glass w-full flex items-center justify-center gap-1.5 px-3 py-2 text-slate-700 dark:text-slate-200 font-semibold rounded-xl text-sm hover:bg-white/30 dark:hover:bg-slate-800/50 transition-colors">
              <ImageIcon size={14} /><span>Moments</span>
            </button>
            {activeDrawer === 'moments' && (
              <div className="absolute bottom-full mb-2 w-full p-2 bg-white dark:bg-slate-800 rounded-xl shadow-lg animate-fade-in-up z-10">
                <button className="w-full flex items-center justify-center gap-2 px-3 py-2 text-sm font-semibold text-white bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg hover:opacity-90 transition-opacity">
                  <PlusCircle size={16} /> New Moment
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const CharacterTabs = ({ activeTab, setActiveTab, character, onMemoryClick }: { activeTab: Tab; setActiveTab: (tab: Tab) => void, character: Character, onMemoryClick: (mem: any, e: React.MouseEvent<HTMLDivElement>) => void }) => {
    const [activePeriod, setActivePeriod] = useState('Weekly'); // 'Daily', 'Weekly', 'Monthly'

    const statsData = {
        Daily: { chats: '15', duration: '35m', intimacy: '+50', memories: '2' },
        Weekly: { chats: '127', duration: '4.2h', intimacy: '+350', memories: '10' },
        Monthly: { chats: '500+', duration: '20h+', intimacy: '+1,200', memories: '38' },
    };
    
    const currentStats = statsData[activePeriod as keyof typeof statsData];

    const statsDisplay = [
        { icon: MessageSquare, label: 'Total Chats', value: currentStats.chats, color: 'blue' },
        { icon: Clock, label: 'Interaction Time', value: currentStats.duration, color: 'green' },
        { icon: Heart, label: 'Intimacy Growth', value: currentStats.intimacy, color: 'pink' },
        { icon: Sparkles, label: 'Memories Created', value: currentStats.memories, color: 'purple' },
    ];

    const colorClasses = {
        blue: {
            icon: 'text-blue-500 dark:text-blue-400',
            bg: 'bg-blue-100 dark:bg-blue-500/10',
        },
        green: {
            icon: 'text-green-500 dark:text-green-400',
            bg: 'bg-green-100 dark:bg-green-500/10',
        },
        pink: {
            icon: 'text-pink-500 dark:text-pink-400',
            bg: 'bg-pink-100 dark:bg-pink-500/10',
        },
        purple: {
            icon: 'text-purple-500 dark:text-purple-400',
            bg: 'bg-purple-100 dark:bg-purple-500/10',
        }
    };
    
    return (
        <div className="glass-strong rounded-3xl shadow-2xl dark:shadow-black/20 overflow-hidden">
            <nav className="flex border-b border-slate-200/50 dark:border-slate-800/50">
                {TABS.map(tab => (<button key={tab} onClick={() => setActiveTab(tab)} className={`flex-1 py-4 px-6 font-semibold capitalize transition-all duration-300 border-b-2 ${activeTab === tab ? 'text-indigo-600 border-indigo-600 bg-white/10 dark:bg-slate-900/20' : 'text-slate-500 border-transparent hover:text-slate-700 hover:bg-slate-100/50 dark:hover:text-slate-300 dark:hover:bg-slate-800/50'}`}>{tab}</button>))}
            </nav>
            <div className="p-8 min-h-[300px]">
                <div id="memories" className={`tab-content space-y-4 ${activeTab === 'memories' ? 'active' : ''}`}>
                     <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-50 mb-4">Memory Capsules</h3>
                     {characterMemories.map((mem) => (<MemoryRow key={mem.id} character={character} memory={{ id: mem.id, text: mem.messages[0].text, timestamp: mem.messages[0].timestamp }} onClick={(e) => onMemoryClick(mem, e)}/>))}
                </div>
                <div id="badges" className={`tab-content space-y-6 ${activeTab === 'badges' ? 'active' : ''}`}>
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-50">Badges & Stats</h3>
                        <div className="flex items-center bg-slate-100 dark:bg-slate-800 p-1 rounded-lg">
                            {(['Daily', 'Weekly', 'Monthly'] as const).map(period => (
                                <button
                                    key={period}
                                    onClick={() => setActivePeriod(period)}
                                    className={`px-4 py-1.5 text-sm font-semibold rounded-md transition-colors ${
                                        activePeriod === period
                                            ? 'bg-white dark:bg-slate-700 text-indigo-600 dark:text-white shadow-sm'
                                            : 'text-slate-500 dark:text-slate-400 hover:bg-slate-200/50 dark:hover:bg-slate-700/50'
                                    }`}
                                >
                                    {period}
                                </button>
                            ))}
                        </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                        {statsDisplay.map(stat => {
                            const Icon = stat.icon;
                            const colors = colorClasses[stat.color as keyof typeof colorClasses];
                            return (
                                <div key={stat.label} className={`flex items-center p-4 rounded-xl ${colors.bg}`}>
                                    <div className="mr-4">
                                        <Icon size={24} className={colors.icon} />
                                    </div>
                                    <div>
                                        <p className="text-sm text-slate-600 dark:text-slate-400">{stat.label}</p>
                                        <p className="text-xl font-bold text-slate-900 dark:text-slate-50">{stat.value}</p>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </div>
            </div>
        </div>
    )
};

// --- MAIN CLIENT PAGE ---
export default function CharacterProfileClientPage({ character, lang }: CharacterProfileProps) {
  const [activeTab, setActiveTab] = useState<Tab>('memories');
  const [activeMemory, setActiveMemory] = useState<any>(null);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const handleMemoryClick = (memory: any, event: React.MouseEvent<HTMLDivElement>) => {
      setAnchorEl(event.currentTarget);
      setActiveMemory(memory);
  };

  const handleCloseMemory = () => {
      setAnchorEl(null);
      setActiveMemory(null);
  };

  return (
    <>
      <main className="max-w-7xl mx-auto px-6 py-8 pb-24">
        <div className="space-y-6">
          <CharacterHeader character={character} lang={lang} />
          <CharacterTabs 
            activeTab={activeTab} 
            setActiveTab={setActiveTab}
            character={character}
            onMemoryClick={handleMemoryClick}
          />
        </div>
        {activeMemory && anchorEl && (
          <FloatingMemory
              memory={activeMemory}
              anchorEl={anchorEl}
              onClose={handleCloseMemory}
          />
        )}
      </main>
      <BottomNavBar lang={lang} />
    </>
  );
} 