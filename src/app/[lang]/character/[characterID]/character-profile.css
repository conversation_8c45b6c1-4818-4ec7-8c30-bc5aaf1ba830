:root {
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --secondary: #8b5cf6;
    --accent: #ec4899;
    --warm: #f59e0b;
    --success: #10b981;
}

/* Using classes instead of broad element selectors for better scoping in Next.js */
.character-profile-body {
    font-family: 'Inter', 'Noto Sans SC', sans-serif;
    position: relative;
}

/* Animated background for the page container */
.bg-animated {
    background: linear-gradient(-45deg, #fef7ff, #f9fafb, #fef2f2, #ffffff);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Glassmorphism effects */
.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.dark .glass {
    background: rgba(30, 41, 59, 0.25); /* slate-800/25 */
    border: 1px solid rgba(51, 65, 85, 0.4); /* slate-700/40 */
}

.glass-strong {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.6);
}
.dark .glass-strong {
    background: rgba(15, 23, 42, 0.85); /* slate-900/85 */
    border: 1px solid rgba(30, 41, 59, 0.6); /* slate-800/60 */
}


/* Bond level ring progress */
.bond-ring {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    /* Use CSS variables for dynamic progress */
    background: conic-gradient(from 0deg, var(--accent) 0deg, var(--accent) var(--bond-angle, 225deg), #e5e7eb var(--bond-angle, 225deg), #e5e7eb 360deg);
    position: relative;
    box-shadow: 0 8px 32px rgba(236, 72, 153, 0.3);
}
.dark .bond-ring {
    background: conic-gradient(from 0deg, var(--accent) 0deg, var(--accent) var(--bond-angle, 225deg), #374151 var(--bond-angle, 225deg), #374151 360deg);
}


.bond-ring::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    background: white;
    border-radius: 50%;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
}
.dark .bond-ring::before {
    background: #0f172a; /* slate-900 */
}


/* Floating animation for avatar */
.float {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Card hover effect */
.card-hover {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}
.dark .card-hover:hover {
     box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}


/* Glowing button effect */
.btn-glow {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
    transition: all 0.3s ease;
}

.btn-glow:hover {
    box-shadow: 0 12px 32px rgba(99, 102, 241, 0.6);
    transform: translateY(-2px);
}

.btn-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn-glow:hover::before {
    left: 100%;
}

/* Achievement badge animation */
.achievement {
    position: relative;
    transition: all 0.3s ease;
}

.achievement:hover {
    transform: scale(1.1) rotateY(10deg);
}

.achievement.rare::after {
    content: '✨';
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 12px;
    animation: sparkle 2s infinite;
}

@keyframes sparkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

/* Memory capsule style */
.memory-capsule {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    border: 1px solid rgba(139, 92, 246, 0.2);
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}
.dark .memory-capsule {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(30, 41, 59, 0.7));
    border: 1px solid rgba(139, 92, 246, 0.3);
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.2);
}


.memory-capsule:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 48px rgba(139, 92, 246, 0.2);
}
.dark .memory-capsule:hover {
    box-shadow: 0 12px 48px rgba(139, 92, 246, 0.3);
}

.memory-capsule::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
    transition: left 0.6s;
}

.memory-capsule:hover::before {
    left: 100%;
}

/* Tab content animation */
.tab-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    display: none;
}

.tab-content.active {
    opacity: 1;
    transform: translateY(0);
    display: block;
}

/* Gradient text */
.gradient-text {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    /* In case background-clip is not supported */
    color: var(--primary);
}

/* Progress bar animation */
.progress-bar-animated {
    animation: progressFill 2s ease-out forwards;
}

@keyframes progressFill {
    from { width: 0%; }
} 