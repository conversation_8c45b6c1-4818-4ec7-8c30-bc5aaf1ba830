import CharacterProfileClientPage from './CharacterProfileClientPage';
import { characters } from '@/lib/mock-data';
import './character-profile.css';
import CharacterPageHeader from './CharacterPageHeader';

export default async function CharacterPage({ params }: { params: Promise<{ lang: string; characterID: string }> }) {
  const { lang, characterID } = await params;

  // Find character data; fallback to first if not found
  const character = characters.find((c) => c.id === characterID) ?? characters[0];

  return (
    <div className="character-profile-body min-h-screen bg-animated dark:bg-none dark:bg-slate-900">
      <CharacterPageHeader />
      <CharacterProfileClientPage character={character} lang={lang} />
    </div>
  );
} 