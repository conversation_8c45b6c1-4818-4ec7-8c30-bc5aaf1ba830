import { characters, Character } from '@/lib/mock-data';
import ChatClientPage from './ChatClientPage';

function getCharacterData(id: string): Character | undefined {
  return characters.find(character => character.id === id);
}

export default async function ChatDetailPage({ params }: { params: Promise<{ lang: string, characterID: string }> }) {
  const { lang, characterID } = await params;
  const characterData = getCharacterData(characterID);

  if (!characterData) {
    // Handle case where character is not found
    return <div>Character not found.</div>;
  }
  
  // For this demo, create some consistent initial messages for a long-term chat.
  const initialMessages = [
    { id: 1, text: `Hey, it's me, ${characterData.name}. Glad we can finally talk here.`, side: 'left' },
    { id: 2, text: "It's great to connect with you too!", side: 'right' },
    { id: 3, text: "What's on your mind today?", side: 'left' }
  ];

  return <ChatClientPage lang={lang} characterData={characterData} initialMessages={initialMessages} />;
} 