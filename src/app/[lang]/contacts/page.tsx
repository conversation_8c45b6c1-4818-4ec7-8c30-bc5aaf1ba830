'use client';

import { useParams } from 'next/navigation';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';

export default function ContactsPage() {
  const { lang } = useParams() as { lang: string };
  const { t } = useTranslation(lang, 'translation');

  return (
    <MainAppLayout lang={lang}>
      <div className="p-4 lg:p-6">
        <h1 className="text-2xl lg:text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          {t('contacts.title', 'Contacts')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t('contacts.description', 'Your AI and human friends will be listed here.')}
        </p>
      </div>
    </MainAppLayout>
  );
} 