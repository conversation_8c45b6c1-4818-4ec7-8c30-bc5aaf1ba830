'use client';

import MainAppLayout from '@/components/MainAppLayout';
import CharacterCard from '@/components/CharacterCard';
import { SlidersHorizontal } from 'lucide-react';
import Link from 'next/link';
import { Character } from '@/lib/mock-data';

interface CardData {
  character: Character;
  stats: {
    likes: number;
    friends: number;
    shares: number;
  };
  aspectRatio: number;
}

interface DiscoverClientPageProps {
  lang: string;
  cardsData: CardData[];
}

const DiscoverClientPage: React.FC<DiscoverClientPageProps> = ({ lang, cardsData }) => {
  return (
    <MainAppLayout lang={lang}>
      <div className="space-y-4 p-4 sm:p-6 lg:p-8">
        {/* Top bar */}
        <div className="flex items-center justify-between">
          <Link href="#" className="text-indigo-600 font-semibold">灵魂测试</Link>
          <h1 className="font-bold text-lg text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-pink-500">Alphane.ai</h1>
          <button className="p-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 flex items-center gap-1 text-sm">
            <SlidersHorizontal size={18} /> 筛选
          </button>
        </div>

        {/* Cards masonry */}
        <div className="columns-2 md:columns-3 lg:columns-4 xl:columns-5 2xl:columns-5 3xl:columns-5 gap-6 space-y-6">
          {cardsData.map(({ character, stats, aspectRatio }) => (
            <div key={character.id} className="break-inside-avoid">
              <CharacterCard lang={lang} character={character} stats={stats} aspectRatio={aspectRatio} />
            </div>
          ))}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default DiscoverClientPage; 