import { characters, generateRandomAspectRatio } from '@/lib/mock-data';
import DiscoverClientPage from './DiscoverClientPage';

export default async function DiscoverPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;

  // Generate mock stats for each character
  const cardsData = characters.map((c) => ({
    character: c,
    stats: {
      likes: Math.floor(Math.random() * 5000) + 100,
      friends: Math.floor(Math.random() * 500) + 10,
      shares: Math.floor(Math.random() * 1000) + 20,
    },
    aspectRatio: generateRandomAspectRatio(),
  }));

  return <DiscoverClientPage lang={lang} cardsData={cardsData} />;
} 