'use client'

import { I18nextProvider } from 'react-i18next';
import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next/initReactI18next';
import { getOptions } from '../i18n/settings';

export default function I18nProvider({
  children,
  locale,
  namespaces,
  resources
}: {
  children: React.ReactNode;
  locale: string;
  namespaces: string[];
  resources: any;
}) {
  const i18n = createInstance();

  i18n
    .use(initReactI18next)
    .init({
      ...getOptions(locale, namespaces[0]),
      resources,
      lng: locale
    });

  return <I18nextProvider i18n={i18n}>{children}</I18nextProvider>;
} 