import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "../globals.css";
import { dir } from 'i18next'
import { languages } from '../i18n/settings'
import { initTranslations } from "../i18n";
import I18nProvider from './i18n-provider';
import { Providers } from "@/components/Providers";
import { ThemeProvider } from "@/components/ThemeProvider";

const i18nNamespaces = ['translation'];

export async function generateStaticParams() {
  return languages.map((lang) => ({ lang }))
}

const gantari = localFont({
  src: "../../../public/fonts/Gantari-Variable.ttf",
  variable: "--font-gantari",
  display: "swap",
});

export const metadata: Metadata = {
  title: "prompt.pro - Discover AI-Native Products",
  description: "Share and remix professional prompts, get invite codes , and discover what's trending.",
};

export default async function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode;
  params: {
    lang: string;
  };
}>) {
  const { lang } = await params;
  const { resources } = await initTranslations(lang, i18nNamespaces);

  return (
    <html lang={lang} dir={dir(lang)} className={`${gantari.variable} font-sans`} suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <I18nProvider
            resources={resources}
            locale={lang}
            namespaces={i18nNamespaces}
          >
            <Providers>{children}</Providers>
          </I18nProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
