'use client';

import { FC, useState, useRef, useCallback, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Send, Mic, PlusCircle, ArrowLeft, MoreVertical, GripVertical, AtSign, Smile, ImagePlus } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { useMediaQuery } from 'react-responsive';
import UserBubble from '@/components/bubbles/UserBubble';
import CharBubble from '@/components/bubbles/CharBubble';

// --- Reusable Child Components ---

const PageHeader = ({ momentData }: { momentData: any }) => {
  const router = useRouter();
  const params = useParams() as { lang?: string };
  const lang = params?.lang ?? 'en';
  const headerClasses = "absolute top-0 left-0 right-0 z-10 flex items-center justify-between p-4 text-white bg-gradient-to-b from-black/60 to-transparent h-20";
  const buttonClasses = "p-2 rounded-full bg-black/30 backdrop-blur-sm hover:bg-white/20";

  return (
    <header className={headerClasses}>
      <button onClick={() => router.back()} className={buttonClasses}>
        <ArrowLeft size={24} />
      </button>
      <div className="flex items-center gap-3">
        {momentData.character.character_avatar ? (
          <Link href={`/${lang}/character/${momentData.character.id}`} prefetch={false}>
            <Image
              src={momentData.character.character_avatar.replace(/'/g, "")}
              alt={momentData.character.name}
              width={40}
              height={40}
              className="rounded-full border-2 border-white/60"
              unoptimized
            />
          </Link>
        ) : (
          <div className="w-10 h-10 rounded-full bg-gray-700 border-2 border-white/60" />
        )}
        <div>
          <h1 className="font-bold">{momentData.character.name}</h1>
          <p className="text-xs text-gray-400">Online</p>
        </div>
      </div>
      <button className={buttonClasses}>
        <MoreVertical size={24} />
      </button>
    </header>
  );
};

const MessageList = ({ messages, momentData, chatEndRef, user }: any) => (
  <div className="flex-1 overflow-y-auto p-4 flex flex-col-reverse space-y-4 space-y-reverse min-h-0">
    <div ref={chatEndRef} />
    {[...(messages || [])].map((msg: any, index: number) => (
      <div key={index}>
        {msg.side === 'right' ? (
          <UserBubble message={msg} user={user} />
        ) : (
          <CharBubble message={msg} character={momentData.character} />
        )}
      </div>
    ))}
  </div>
);

const ChatInputBar = ({ value, onChange, onKeyPress, onClick }: any) => (
  <div className="flex-shrink-0 px-4 py-3 bg-gradient-to-t from-black via-black/90 to-transparent h-24 flex items-center">
    <div className="flex w-full items-center gap-2 bg-gray-900/80 rounded-full p-2">
      <div className="flex items-center space-x-1 flex-shrink-0 pl-2">
        <button title="Share" className="py-3 px-1 text-gray-400 hover:text-white transition-colors rounded-full"><AtSign size={28}/></button>
        <button title="Emoji" className="py-3 px-1 text-gray-400 hover:text-white transition-colors rounded-full"><Smile size={28}/></button>
        <button title="Send Image/GIF" className="py-3 px-1 text-gray-400 hover:text-white transition-colors rounded-full"><ImagePlus size={28}/></button>
      </div>
      <input
          type="text"
          placeholder="Message..."
          className="flex-1 w-full min-w-0 bg-transparent focus:outline-none px-2 text-white text-lg"
          value={value}
          onChange={onChange}
          onKeyPress={onKeyPress}
      />
      <button title="Voice Input" className="p-3 text-gray-400 hover:text-white transition-colors flex-shrink-0"><Mic size={28}/></button>
      <button title="Send" className="p-4 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors flex-shrink-0" onClick={onClick}>
          <Send size={24}/>
      </button>
    </div>
  </div>
);

// --- Main Client Component ---

interface MomentClientPageProps {
  momentData: any;
  initialMessages: any[];
}

const MomentClientPage: FC<MomentClientPageProps> = ({ momentData, initialMessages }) => {
  const isDesktop = useMediaQuery({ query: '(min-width: 1024px)' });

  const [messages, setMessages] = useState(initialMessages);
  const [inputValue, setInputValue] = useState('');
  
  const handleSendMessage = () => {
    if (inputValue.trim()) {
      setMessages([{ id: Date.now(), text: inputValue, side: 'right' as const }, ...messages]);
      setInputValue('');
    }
  };
  
  const [handleY, setHandleY] = useState(0); 
  const [isDragging, setIsDragging] = useState(false);
  const [isClient, setIsClient] = useState(false);
  
  const chatEndRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);
  const rightColumnRef = useRef<HTMLDivElement>(null);
  
  // A mock user object. In a real app, this would come from an auth context.
  const mockUser = {
    name: 'User',
    avatar: 'https://i.pravatar.cc/40?u=alphane-user'
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    // Only set initial position for the mobile draggable view
    if (!isDesktop) {
      const INPUT_BAR_HEIGHT = 80;
      const containerHeight = window.innerHeight;
      
      if (!containerHeight) return;

      const chatAreaHeight = containerHeight * 0.45;
      const initialBottomEdge = containerHeight - INPUT_BAR_HEIGHT;
      setHandleY(initialBottomEdge - (chatAreaHeight / 2));
    }
  }, [isDesktop, isClient]);

  const handleDragStart = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    setIsDragging(true);
    document.body.style.cursor = 'row-resize';
  }, []);

  const handleDragMove = useCallback((e: MouseEvent | TouchEvent) => {
    if (!isDragging) return;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    const containerTop = rightColumnRef.current?.getBoundingClientRect().top || 0;
    const relativeY = clientY - containerTop;

    const containerHeight = isDesktop ? rightColumnRef.current?.offsetHeight : window.innerHeight;
    if (!containerHeight) return;

    const chatAreaHeight = containerHeight * 0.45;
    const halfChatHeight = chatAreaHeight / 2;
    const HEADER_HEIGHT = isDesktop ? 0 : 80; // Header is not in the right column on desktop
    const INPUT_BAR_HEIGHT = 96; // h-24

    const minHandleY = HEADER_HEIGHT + halfChatHeight;
    const maxHandleY = containerHeight - INPUT_BAR_HEIGHT - halfChatHeight;

    const clampedY = Math.max(minHandleY, Math.min(relativeY, maxHandleY));
    setHandleY(clampedY);
  }, [isDragging, isDesktop]);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
    document.body.style.cursor = 'default';
  }, []);

  useEffect(() => {
    // Drag event listeners setup
    if (isDragging) {
      window.addEventListener('mousemove', handleDragMove);
      window.addEventListener('touchmove', handleDragMove, { passive: false });
      window.addEventListener('mouseup', handleDragEnd);
      window.addEventListener('touchend', handleDragEnd);
    } else {
      window.removeEventListener('mousemove', handleDragMove);
      window.removeEventListener('touchmove', handleDragMove);
      window.removeEventListener('mouseup', handleDragEnd);
      window.removeEventListener('touchend', handleDragEnd);
    }
    return () => {
      // Cleanup
      window.removeEventListener('mousemove', handleDragMove);
      window.removeEventListener('touchmove', handleDragMove);
      window.removeEventListener('mouseup', handleDragEnd);
      window.removeEventListener('touchend', handleDragEnd);
    };
  }, [isDragging, handleDragMove, handleDragEnd]);
  
  useEffect(() => {
    if (isClient) {
      chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isClient]);
  
  if (!isClient) {
    return null;
  }
  
  const containerHeight = (isDesktop ? rightColumnRef.current?.offsetHeight : window.innerHeight) || 0;
  const chatAreaHeight = containerHeight * 0.45;
  const chatAreaTop = handleY > 0 ? handleY - chatAreaHeight / 2 : -chatAreaHeight;

  // Mobile immersive view
  if (!isDesktop) {
    return (
      <div className="h-screen w-screen overflow-hidden relative bg-black select-none">
        {/* Mobile-specific components and layout */}
        <div className="absolute inset-0 z-0">
          {momentData.moment.image && (
            <Image
              src={momentData.moment.image.replace(/'/g, "")}
              alt={momentData.moment.title}
              fill
              className="object-cover"
              unoptimized
              priority
            />
          )}
          <div className="absolute inset-0 bg-black/30" />
        </div>
        <PageHeader momentData={momentData} />
        <div 
          className="absolute left-0 right-0 z-30 flex flex-col bg-transparent"
          style={{ 
            top: `${chatAreaTop}px`,
            height: `${chatAreaHeight}px`,
            maskImage: 'linear-gradient(to top, black 85%, transparent 100%)',
          }}
        >
          <MessageList messages={messages} momentData={momentData} chatEndRef={chatEndRef} user={mockUser} />
        </div>
        <div 
          ref={handleRef}
          onMouseDown={handleDragStart}
          onTouchStart={handleDragStart}
          className="absolute right-0 w-8 h-16 flex items-center justify-center cursor-row-resize z-50 group touch-none"
          style={{ 
            top: handleY > 0 ? `${handleY - 32}px` : '-100px',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            borderRadius: '8px 0 0 8px',
          }}
        >
          <GripVertical className="w-5 h-8 text-white/70" />
        </div>
        <div className="absolute bottom-0 left-0 right-0 z-40">
          <ChatInputBar 
            value={inputValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputValue(e.target.value)}
            onKeyPress={(e: React.KeyboardEvent) => e.key === 'Enter' && handleSendMessage()}
            onClick={handleSendMessage}
          />
        </div>
      </div>
    );
  }

  // Desktop 62.5% / 37.5% split view (fixed layout, no drag)
  return (
    <div className="h-screen w-screen bg-black flex text-white select-none">
      {/* Left side: Background Image */}
      <div className="w-[62.5%] h-full relative">
        <Image
          src={momentData.moment.image.replace(/'/g, "")}
          alt={momentData.moment.title}
          fill
          className="object-cover"
          unoptimized
          priority
        />
        <PageHeader momentData={momentData} />
      </div>

      {/* Right side: Chat Area with blurred background */}
      <div ref={rightColumnRef} className="w-[37.5%] h-full relative flex flex-col">
        {/* Blurred background */}
        <div className="absolute inset-0 z-0">
          <Image
            src={momentData.moment.image.replace(/'/g, "")}
            alt=""
            fill
            className="object-cover"
            unoptimized
          />
          <div className="absolute inset-0 bg-black/50 backdrop-blur-lg" />
        </div>

        {/* Chat Area - uses flexbox to fill space above input */}
        <div 
          className="relative z-10 flex-1 flex flex-col min-h-0"
          style={{
            maskImage: 'linear-gradient(to top, black 85%, transparent 100%)',
          }}
        >
          <MessageList messages={messages} momentData={momentData} chatEndRef={chatEndRef} user={mockUser} />
        </div>
        
        {/* Input Bar - at the bottom of the right column */}
        <div className="relative z-10 flex-shrink-0">
          <ChatInputBar 
            value={inputValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputValue(e.target.value)}
            onKeyPress={(e: React.KeyboardEvent) => e.key === 'Enter' && handleSendMessage()}
            onClick={handleSendMessage}
          />
        </div>
      </div>
    </div>
  );
}

export default MomentClientPage; 