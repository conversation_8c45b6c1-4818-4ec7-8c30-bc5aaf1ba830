'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { Bell, Star, Calendar, Shield, Users, ExternalLink, CheckCheck, Package, MessageSquare, Settings, UserPlus, User, CreditCard } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';

// Sample notification data
const sampleNotifications = {
  all: [
    {
      id: 'sys1',
      type: 'system',
      icon: <Settings className='w-5 h-5 text-primary' />,
      title: 'System Update v2.4.1 Available',
      description: 'New features and security improvements are now available. Update recommended.',
      timestamp: '2 hours ago',
      isUnread: true,
      category: 'System',
      link: '/system/updates',
    },
    {
      id: 'soc1',
      type: 'social',
      icon: <UserPlus className='w-5 h-5 text-primary' />,
      title: 'You have 5 new followers this week',
      description: 'Your recent prompts gained attention from @sarahchen, @alexkim, and 3 others.',
      timestamp: '1 day ago',
      isUnread: true,
      category: 'Social',
      link: '/profile/followers',
    },
    {
      id: 'prof1',
      type: 'profile',
      icon: <User className='w-5 h-5 text-primary' />,
      title: 'Profile Updated Successfully',
      description: 'Your bio and specialties have been updated and are now visible to other users.',
      timestamp: '3 days ago',
      isUnread: false,
      category: 'Profile',
      link: '/profile/edit',
    },
    {
      id: 'sub1',
      type: 'subscription',
      icon: <CreditCard className='w-5 h-5 text-primary' />,
      title: 'Pro subscription expires in 7 days',
      description: 'Your Pro plan will expire on December 22, 2024. Renew now to avoid interruption.',
      timestamp: '1 hour ago',
      isUnread: true,
      category: 'Subscription',
      link: '/subscription',
    },
  ],
  system: [
    {
      id: 'sys1',
      type: 'system',
      icon: <Settings className='w-5 h-5 text-primary' />,
      title: 'System Update v2.4.1 Available',
      description: 'New features and security improvements are now available. Update recommended.',
      timestamp: '2 hours ago',
      isUnread: true,
      category: 'System',
      link: '/system/updates',
    },
    {
      id: 'sys2',
      type: 'system',
      icon: <Settings className='w-5 h-5 text-primary' />,
      title: 'Scheduled Maintenance Complete',
      description: 'System maintenance has been completed. All services are now fully operational.',
      timestamp: '2 days ago',
      isUnread: false,
      category: 'System',
      link: '#',
    },
    {
      id: 'sys3',
      type: 'system',
      icon: <Settings className='w-5 h-5 text-primary' />,
      title: 'Security Update Applied',
      description: 'Enhanced security measures have been implemented to protect your account.',
      timestamp: '1 week ago',
      isUnread: false,
      category: 'System',
      link: '/security',
    },
  ],
  social: [
    {
      id: 'soc1',
      type: 'social',
      icon: <UserPlus className='w-5 h-5 text-primary' />,
      title: 'You have 5 new followers this week',
      description: 'Your recent prompts gained attention from @sarahchen, @alexkim, and 3 others.',
      timestamp: '1 day ago',
      isUnread: true,
      category: 'Social',
      link: '/profile/followers',
    },
    {
      id: 'soc2',
      type: 'social',
      icon: <UserPlus className='w-5 h-5 text-primary' />,
      title: 'Dr. Sarah Chen started following you',
      description: 'A verified AI researcher with 12.5k followers is now following your work.',
      timestamp: '2 days ago',
      isUnread: false,
      category: 'Social',
      link: '/users/sarahchen',
    },
    {
      id: 'soc3',
      type: 'social',
      icon: <Users className='w-5 h-5 text-primary' />,
      title: 'Weekly Follow Summary',
      description: 'You gained 12 new followers and followed 3 new experts this week.',
      timestamp: '3 days ago',
      isUnread: false,
      category: 'Social',
      link: '/profile/activity',
    },
  ],
  profile: [
    {
      id: 'prof1',
      type: 'profile',
      icon: <User className='w-5 h-5 text-primary' />,
      title: 'Profile Updated Successfully',
      description: 'Your bio and specialties have been updated and are now visible to other users.',
      timestamp: '3 days ago',
      isUnread: false,
      category: 'Profile',
      link: '/profile/edit',
    },
    {
      id: 'prof2',
      type: 'profile',
      icon: <User className='w-5 h-5 text-primary' />,
      title: 'Avatar Changed',
      description: 'Your profile picture has been successfully updated across the platform.',
      timestamp: '1 week ago',
      isUnread: false,
      category: 'Profile',
      link: '/profile/edit',
    },
    {
      id: 'prof3',
      type: 'profile',
      icon: <User className='w-5 h-5 text-primary' />,
      title: 'Verification Badge Added',
      description: 'Congratulations! Your account has been verified as an expert contributor.',
      timestamp: '2 weeks ago',
      isUnread: false,
      category: 'Profile',
      link: '/profile',
    },
  ],
  subscription: [
    {
      id: 'sub1',
      type: 'subscription',
      icon: <CreditCard className='w-5 h-5 text-primary' />,
      title: 'Pro subscription expires in 7 days',
      description: 'Your Pro plan will expire on December 22, 2024. Renew now to avoid interruption.',
      timestamp: '1 hour ago',
      isUnread: true,
      category: 'Subscription',
      link: '/subscription',
    },
    {
      id: 'sub2',
      type: 'subscription',
      icon: <CreditCard className='w-5 h-5 text-primary' />,
      title: 'Payment Method Updated',
      description: 'Your payment method has been successfully updated for automatic renewal.',
      timestamp: '1 week ago',
      isUnread: false,
      category: 'Subscription',
      link: '/subscription/billing',
    },
    {
      id: 'sub3',
      type: 'subscription',
      icon: <CreditCard className='w-5 h-5 text-primary' />,
      title: 'Subscription Renewed',
      description: 'Your Pro subscription has been automatically renewed for another month.',
      timestamp: '1 month ago',
      isUnread: false,
      category: 'Subscription',
      link: '/subscription',
    },
  ],
};

export default function NotificationsPage() {
  const { lang } = useParams() as { lang: string };
  const { t } = useTranslation(lang, 'translation');
  const [activeTab, setActiveTab] = useState<'all' | 'system' | 'social' | 'profile' | 'subscription'>('all');
  const [notifications, setNotifications] = useState(sampleNotifications);

  const TabButton = ({ tab, label, isActive, onClick, count, icon: IconComponent }: { 
    tab: string; 
    label: string; 
    isActive: boolean; 
    onClick: () => void;
    count?: number;
    icon: React.ComponentType<any>;
  }) => (
    <button
      onClick={onClick}
      className={`px-3 sm:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center gap-2 whitespace-nowrap relative ${
        isActive
          ? 'bg-primary text-primary-foreground shadow-sm'
          : 'text-foreground hover:text-foreground'
      }`}
    >
      <IconComponent 
        size={16} 
        className={isActive ? 'text-primary-foreground' : 'text-foreground'} 
      />
      <span className={`hidden sm:inline ${isActive ? 'text-primary-foreground' : 'text-foreground'}`}>
        {label}
      </span>
      {count !== undefined && count > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center text-[10px]">
          {count}
        </span>
      )}
    </button>
  );

  const NotificationItem = ({ notification }: { notification: any }) => (
    <div className="bg-card text-card-foreground rounded-lg p-4 border border-border hover:border-card-foreground transition-all duration-200 theme-transition">
      <div className="flex items-start space-x-3">
        <div className={`p-2 rounded-lg mt-0.5 ${notification.isUnread ? 'bg-primary/20' : 'bg-card-foreground/10'}`}>
          {notification.icon}
        </div>
        <div className="flex-grow">
          <div className="flex items-start justify-between">
            <div className="flex-grow">
              <h3 className={`text-sm font-medium ${notification.isUnread ? 'text-card-foreground' : 'text-card-foreground/80'}`}>
                {notification.title}
              </h3>
              <p className="text-xs text-slate-600 dark:text-slate-400 mt-1">{notification.description}</p>
              <div className="flex items-center justify-between mt-2">
                <p className="text-xs text-slate-500 dark:text-slate-500">{notification.timestamp}</p>
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {notification.category}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2 ml-3">
              {notification.isUnread && (
                <div className="w-2 h-2 bg-primary rounded-full" title="Unread"></div>
              )}
              {notification.link !== '#' && (
                <ExternalLink size={14} className="text-card-foreground/40 hover:text-card-foreground transition-colors cursor-pointer" />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const markAllAsRead = () => {
    setNotifications(prev => ({
      ...prev,
      [activeTab]: prev[activeTab].map(n => ({ ...n, isUnread: false }))
    }));
  };

  const currentNotifications = notifications[activeTab] || [];
  const unreadCount = currentNotifications.filter(n => n.isUnread).length;
  const totalUnreadCount = Object.values(notifications).flat().filter(n => n.isUnread).length;

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-background theme-transition">
        <div className="max-w-4xl mx-auto p-4 sm:p-6 space-y-6">
          <div className="text-center">
            <div className="flex justify-center items-center space-x-4 mb-2">
              <Bell className="text-logo" size={48} />
              <h1 className="text-4xl md:text-5xl font-bold text-foreground leading-tight">
                {t('notifications.title')}
              </h1>
            </div>
            <p className="text-lg text-foreground/70 max-w-3xl mx-auto leading-relaxed">
              {t('notifications.description')}
            </p>
          </div>

          <div className="flex bg-card border border-border rounded-lg p-1 theme-transition w-fit mx-auto">
            <TabButton
              tab="all"
              label={t('notifications.all')}
              isActive={activeTab === 'all'}
              onClick={() => setActiveTab('all')}
              count={Object.values(notifications).flat().filter(n => n.isUnread).length}
              icon={Package}
            />
            <TabButton
              tab="system"
              label={t('notifications.system')}
              isActive={activeTab === 'system'}
              onClick={() => setActiveTab('system')}
              count={notifications.system.filter(n => n.isUnread).length || undefined}
              icon={Settings}
            />
            <TabButton
              tab="social"
              label={t('notifications.social')}
              isActive={activeTab === 'social'}
              onClick={() => setActiveTab('social')}
              count={notifications.social.filter(n => n.isUnread).length}
              icon={UserPlus}
            />
            <TabButton
              tab="profile"
              label={t('notifications.profile')}
              isActive={activeTab === 'profile'}
              onClick={() => setActiveTab('profile')}
              count={notifications.profile.filter(n => n.isUnread).length}
              icon={User}
            />
            <TabButton
              tab="subscription"
              label={t('notifications.subscription')}
              isActive={activeTab === 'subscription'}
              onClick={() => setActiveTab('subscription')}
              count={notifications.subscription.filter(n => n.isUnread).length || undefined}
              icon={CreditCard}
            />
          </div>

          {unreadCount > 0 && (
            <div className="flex justify-between items-center bg-card border border-border rounded-lg p-4 theme-transition">
              <div className="text-card-foreground">
                {t('notifications.unreadCount', { count: unreadCount })}{' '}
                {t('notifications.inThisCategory')}
              </div>
              <button
                onClick={markAllAsRead}
                className="flex items-center text-primary hover:text-primary/80 font-medium text-sm transition-colors"
              >
                <CheckCheck size={16} className="mr-1" />
                {t('notifications.markAllAsRead')}
              </button>
            </div>
          )}

          <div className="space-y-4">
            {currentNotifications.length > 0 ? (
              currentNotifications.map((notification) => (
                <NotificationItem key={notification.id} notification={notification} />
              ))
            ) : (
              <div className="text-center py-16 space-y-4">
                <div className="text-6xl text-foreground/30">
                  {activeTab === 'all' && '📭'}
                  {activeTab === 'system' && '📢'}
                  {activeTab === 'social' && '👥'}
                  {activeTab === 'profile' && '👤'}
                  {activeTab === 'subscription' && '💳'}
                </div>
                <h3 className="text-xl font-semibold text-foreground">
                  {t('notifications.emptyState.title', {
                    category: activeTab === 'all' ? '' : t(`notifications.${activeTab}`),
                  })}
                </h3>
                <p className="text-foreground/70 max-w-md mx-auto">
                  {t(`notifications.emptyState.${activeTab}`)}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
}
