'use client';

import { useState } from 'react';
import MomentCard from '@/components/MomentCard';
import CharacterCard from '@/components/CharacterCard';
import { Character } from '@/lib/mock-data';
import Image from 'next/image';

interface ProfileClientPageProps {
  lang: string;
  moments: any[];
  liked: any[];
  friendsCards: {
    character: Character;
    stats: { likes: number; friends: number; shares: number };
    aspectRatio: number;
  }[];
}

const TABS = ['moments', 'likes', 'friends'] as const;

const ProfileClientPage: React.FC<ProfileClientPageProps> = ({ lang, moments, liked, friendsCards }) => {
  const [activeTab, setActiveTab] = useState<typeof TABS[number]>('moments');

  return (
    <>
      {/* Top profile cover */}
      <section className="relative w-full h-[640px] bg-gradient-to-br from-indigo-200 to-pink-200 dark:from-indigo-800 dark:to-pink-900 overflow-hidden flex items-end">
        {/* placeholder background image */}
        <Image src="https://picsum.photos/1200/800?blur=3" alt="cover" fill className="object-cover" unoptimized />
        <div className="absolute inset-0 bg-black/40" />

        <div className="relative z-10 p-6 flex items-center gap-6">
          <Image src="https://i.pravatar.cc/160?u=profile" alt="avatar" width={160} height={160} className="w-40 h-40 rounded-full border-4 border-white object-cover" unoptimized />
          <div className="text-white">
            <h2 className="text-3xl font-bold mb-2">Moonlight Wanderer</h2>
            <p className="max-w-sm text-sm opacity-90 mb-4 line-clamp-3">An adventurer who loves exploring AI realms and crafting new characters with unique personalities.</p>
            <div className="flex items-center gap-6 text-sm">
              <span>Followers 1.2k</span>
              <span>Following 256</span>
              <span>Moments 87</span>
            </div>
          </div>
        </div>
      </section>

      {/* Sticky tab bar */}
      <div className="sticky top-0 z-20 bg-background border-b border-border">
        <div className="flex justify-around">
          {TABS.map((tab) => (
            <button
              key={tab}
              className={`flex-1 py-3 font-semibold capitalize ${activeTab === tab ? 'text-indigo-600 border-b-2 border-indigo-600' : 'text-gray-500'}`}
              onClick={() => setActiveTab(tab)}
            >
              {tab === 'moments' && 'Moments'}
              {tab === 'likes' && 'Likes'}
              {tab === 'friends' && 'Friends'}
            </button>
          ))}
        </div>
      </div>

      {/* Feed */}
      <div className="bg-background p-4 sm:p-6 lg:p-8">
        <div className="columns-2 md:columns-3 lg:columns-4 xl:columns-5 2xl:columns-5 3xl:columns-5 gap-6 space-y-6">
          {activeTab === 'moments' && moments.map((m, idx) => (
            <div key={`mom-${idx}`} className="break-inside-avoid">
              <MomentCard
                lang={lang}
                character={m.character}
                moment={m.moment}
                stats={m.stats}
                aspectRatio={m.aspectRatio}
                publishedAt={m.publishedAt}
                storyTemplateId={m.storyTemplateId}
              />
            </div>
          ))}
          {activeTab === 'likes' && liked.map((m, idx) => (
            <div key={`like-${idx}`} className="break-inside-avoid">
              <MomentCard
                lang={lang}
                character={m.character}
                moment={m.moment}
                stats={m.stats}
                aspectRatio={m.aspectRatio}
                publishedAt={m.publishedAt}
                storyTemplateId={m.storyTemplateId}
              />
            </div>
          ))}
          {activeTab === 'friends' && friendsCards.map(({character, stats, aspectRatio}) => (
            <div key={`friend-${character.id}`} className="break-inside-avoid">
              <CharacterCard lang={lang} character={character} stats={stats} aspectRatio={aspectRatio} />
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default ProfileClientPage; 