import ProfileClientPage from './ProfileClientPage';
import { generateAllSampleMoments, characters, generateRandomAspectRatio } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';

export default async function ProfilePage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const moments = generateAllSampleMoments(12);
  const liked = generateAllSampleMoments(10);
  const friendsCards = characters.map((c) => ({
    character: c,
    stats: {
      likes: 0,
      friends: Math.floor(Math.random()*500)+10,
      shares: 0,
    },
    aspectRatio: generateRandomAspectRatio(),
  }));

  return (
    <MainAppLayout lang={lang}>
      <ProfileClientPage lang={lang} moments={moments} liked={liked} friendsCards={friendsCards} />
    </MainAppLayout>
  );
} 