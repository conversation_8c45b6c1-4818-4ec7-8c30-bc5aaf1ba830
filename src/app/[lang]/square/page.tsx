import { characters } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';
import Memory, { MemoryMessage } from '@/components/Memory';

// Mock data for memories, this would typically come from a service
const mockMemoryMessages: MemoryMessage[] = [
  {
    id: 1, side: 'left', avatar: 'https://i.pravatar.cc/40?u=aiko', name: '<PERSON><PERSON>',
    text: 'Hey, have you read that new sci-fi novel? It blew my mind 🤯',
    timestamp: '2 hours ago',
  },
  {
    id: 2, side: 'right', avatar: 'https://i.pravatar.cc/40?u=user', name: 'Me',
    text: "Not yet! What's it about?",
    timestamp: '2 hours ago',
  },
  {
    id: 3, side: 'left', avatar: 'https://i.pravatar.cc/40?u=aiko', name: '<PERSON><PERSON>',
    text: 'It explores parallel universes and quantum entanglement—super cool stuff!',
    timestamp: '2 hours ago',
  },
];

const mockMemories = [
  { id: 'mem1', messages: mockMemoryMessages },
  { id: 'mem2', messages: mockMemoryMessages.slice(0, 2) },
  { id: 'mem3', messages: mockMemoryMessages.slice(1) },
];

export default async function SquarePage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;

  // mock chats for left panel
  const chatListData = characters.map((character, index) => ({
    character,
    lastMessage: {
      text: index % 2 === 0 ? 'Let\'s catch up later!' : 'See you soon!',
      timestamp: `${(index + 1) * 3}m ago`,
    },
  }));

  return (
    <MainAppLayout lang={lang}>
      <div className="bg-background text-foreground min-h-screen">
        <div className="max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
          <h1 className="text-3xl font-bold mb-6">Shared Moments</h1>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockMemories.map((mem) => (
              <Memory key={mem.id} messages={mem.messages} />
            ))}
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
} 