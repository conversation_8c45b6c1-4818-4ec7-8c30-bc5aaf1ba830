'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { Upload, Link as LinkIcon, Tag, Eye, EyeOff, Zap, List, FileEdit, Globe, RefreshCw, Calendar, Clock, FileText, AlertCircle } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';

type SubmitMode = 'instant' | 'batch' | 'detailed' | 'sync';

export default function SubmitProductPage() {
  const { lang } = useParams() as { lang: string };
  const { t } = useTranslation(lang, 'translation');
  const [submitMode, setSubmitMode] = useState<SubmitMode>('instant');
  
  // Instant mode states
  const [instantUrl, setInstantUrl] = useState('');
  
  // Batch mode states
  const [batchProducts, setBatchProducts] = useState('');
  
  // Detailed mode states (existing form states)
  const [productName, setProductName] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [shortDescription, setShortDescription] = useState('');
  const [longDescription, setLongDescription] = useState('');
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState('');
  const [visibility, setVisibility] = useState('public');
  const [logoUrl, setLogoUrl] = useState('');

  // Sync mode states
  const [syncMethod, setSyncMethod] = useState<'file' | 'url'>('file');
  const [syncUrl, setSyncUrl] = useState('');
  const [syncFrequency, setSyncFrequency] = useState('weekly');
  const [syncDescription, setSyncDescription] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleInstantSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Instant submission:', { url: instantUrl });
    alert(`Instant submission received: ${instantUrl}`);
  };

  const handleBatchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const productNames = batchProducts
      .split('\n')
      .map(name => name.trim())
      .filter(name => name);
    console.log('Batch submission:', { products: productNames });
    alert(`Batch submission received: ${productNames.length} products`);
  };

  const handleDetailedSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log({
      productName,
      websiteUrl,
      shortDescription,
      longDescription,
      category,
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
      visibility,
      logoUrl,
    });
    alert('Detailed product submission form submitted (see console for data)!');
  };

  const handleSyncSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const syncData = {
      method: syncMethod,
      url: syncMethod === 'url' ? syncUrl : null,
      file: syncMethod === 'file' ? uploadedFile?.name : null,
      frequency: syncFrequency,
      description: syncDescription,
    };
    console.log('Sync submission:', syncData);
    alert(`Sync parsing setup submitted! Method: ${syncMethod}, Frequency: ${syncFrequency}`);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const inputClass = "w-full p-3 bg-background border border-border rounded-lg text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 theme-transition";
  const labelClass = "block text-sm font-medium text-foreground mb-2";
  const textareaClass = "w-full p-3 bg-background border border-border rounded-lg text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 resize-none theme-transition";

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 theme-transition">
        <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center mb-4">
              <Upload className="text-logo mr-3" size={48} />
              <h1 className="text-4xl md:text-5xl font-bold text-foreground leading-tight">
                {t('submit.title')}
              </h1>
            </div>
            <p className="text-lg text-foreground/70 max-w-3xl mx-auto leading-relaxed">
              {t('submit.description')}
            </p>
          </div>

          {/* Mode Selection */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-foreground">{t('submit.chooseMethod')}</h2>
            <div className="flex bg-card border border-border rounded-lg p-1 theme-transition w-fit mx-auto">
              <button
                onClick={() => setSubmitMode('instant')}
                className={`px-3 sm:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center gap-2 whitespace-nowrap ${
                  submitMode === 'instant'
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-foreground hover:text-foreground'
                }`}
              >
                <Zap size={16} className={submitMode === 'instant' ? 'text-primary-foreground' : 'text-foreground'} />
                <span className="hidden sm:inline">{t('submit.modes.instant')}</span>
              </button>
              <button
                onClick={() => setSubmitMode('batch')}
                className={`px-3 sm:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center gap-2 whitespace-nowrap ${
                  submitMode === 'batch'
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-foreground hover:text-foreground'
                }`}
              >
                <List size={16} className={submitMode === 'batch' ? 'text-primary-foreground' : 'text-foreground'} />
                <span className="hidden sm:inline">{t('submit.modes.batch')}</span>
              </button>
              <button
                onClick={() => setSubmitMode('detailed')}
                className={`px-3 sm:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center gap-2 whitespace-nowrap ${
                  submitMode === 'detailed'
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-foreground hover:text-foreground'
                }`}
              >
                <FileEdit size={16} className={submitMode === 'detailed' ? 'text-primary-foreground' : 'text-foreground'} />
                <span className="hidden sm:inline">{t('submit.modes.detailed')}</span>
              </button>
              <button
                onClick={() => setSubmitMode('sync')}
                className={`px-3 sm:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center gap-2 whitespace-nowrap ${
                  submitMode === 'sync'
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-foreground hover:text-foreground'
                }`}
              >
                <RefreshCw size={16} className={submitMode === 'sync' ? 'text-primary-foreground' : 'text-foreground'} />
                <span className="hidden sm:inline">{t('submit.modes.sync')}</span>
              </button>
            </div>
            <div className="text-center text-sm text-foreground/60">
              {submitMode === 'instant' && t('submit.modeDescriptions.instant')}
              {submitMode === 'batch' && t('submit.modeDescriptions.batch')}
              {submitMode === 'detailed' && t('submit.modeDescriptions.detailed')}
              {submitMode === 'sync' && t('submit.modeDescriptions.sync')}
            </div>
          </div>

          {/* Instant Mode */}
          {submitMode === 'instant' && (
            <div className="bg-card text-card-foreground rounded-xl p-6 md:p-8 border border-border shadow-lg theme-transition">
              <div className="flex items-center gap-3 mb-6">
                <Zap className="text-primary" size={24} />
                <h3 className="text-xl font-semibold text-card-foreground">{t('submit.instant.title')}</h3>
              </div>
              <form onSubmit={handleInstantSubmit} className="space-y-6">
                <div>
                  <label htmlFor="instantUrl" className={labelClass}>
                    <span className="flex items-center">
                      <Globe size={16} className="mr-2" />
                      {t('submit.instant.websiteUrl')}
                    </span>
                  </label>
                  <input
                    type="url"
                    id="instantUrl"
                    value={instantUrl}
                    onChange={(e) => setInstantUrl(e.target.value)}
                    className={inputClass}
                    placeholder={t('submit.instant.placeholder')}
                    required
                  />
                  <p className="text-xs text-card-foreground/60 mt-2">
                    {t('submit.instant.extractNote')}
                  </p>
                </div>

                <div className="bg-primary/10 rounded-lg p-4">
                  <h4 className="font-medium text-primary mb-2">{t('submit.instant.whatHappensNext')}</h4>
                  <ul className="text-sm text-card-foreground/70 space-y-1">
                    <li>• {t('submit.instant.steps.analyze')}</li>
                    <li>• {t('submit.instant.steps.generate')}</li>
                    <li>• {t('submit.instant.steps.email')}</li>
                    <li>• {t('submit.instant.steps.review')}</li>
                  </ul>
                </div>

                <button
                  type="submit"
                  className="w-full bg-primary text-primary-foreground hover:bg-primary/90 font-semibold py-3 px-4 rounded-lg text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background shadow-lg hover:shadow-xl hover:scale-[1.02]"
                >
                  {t('submit.instant.submitButton')}
                </button>
              </form>
            </div>
          )}

          {/* Batch Mode */}
          {submitMode === 'batch' && (
            <div className="bg-card text-card-foreground rounded-xl p-6 md:p-8 border border-border shadow-lg theme-transition">
              <div className="flex items-center gap-3 mb-6">
                <List className="text-primary" size={24} />
                <h3 className="text-xl font-semibold text-card-foreground">{t('submit.batch.title')}</h3>
              </div>
              <form onSubmit={handleBatchSubmit} className="space-y-6">
                <div>
                  <label htmlFor="batchProducts" className={labelClass}>
                    {t('submit.batch.label')}
                  </label>
                  <textarea
                    id="batchProducts"
                    value={batchProducts}
                    onChange={(e) => setBatchProducts(e.target.value)}
                    className={`${textareaClass} h-64`}
                    placeholder={t('submit.batch.placeholder')}
                    required
                  />
                  <div className="text-xs text-card-foreground/60 mt-1 flex justify-between">
                    <span>{t('submit.batch.helpText')}</span>
                    <span>{t('submit.batch.productsCount', { count: batchProducts.split('\n').filter(line => line.trim()).length })}</span>
                  </div>
                </div>

                <div className="bg-orange-100 dark:bg-orange-900/30 rounded-lg p-4">
                  <h4 className="font-medium text-orange-700 dark:text-orange-300 mb-2">{t('submit.batch.processingInfo')}</h4>
                  <ul className="text-sm text-orange-600 dark:text-orange-400 space-y-1">
                    <li>• {t('submit.batch.infoPoints.portfolio')}</li>
                    <li>• {t('submit.batch.infoPoints.research')}</li>
                    <li>• {t('submit.batch.infoPoints.ideal')}</li>
                    <li>• {t('submit.batch.infoPoints.time')}</li>
                    <li>• {t('submit.batch.infoPoints.summary')}</li>
                  </ul>
                </div>

                <button
                  type="submit"
                  className="w-full bg-primary text-primary-foreground hover:bg-primary/90 font-semibold py-3 px-4 rounded-lg text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background shadow-lg hover:shadow-xl hover:scale-[1.02]"
                >
                  {t('submit.batch.submitButton', { count: batchProducts.split('\n').filter(line => line.trim()).length })}
                </button>
              </form>
            </div>
          )}

          {/* Detailed Mode */}
          {submitMode === 'detailed' && (
            <div className="bg-card text-card-foreground rounded-xl p-6 md:p-8 border border-border shadow-lg theme-transition">
              <div className="flex items-center gap-3 mb-6">
                <FileEdit className="text-primary" size={24} />
                <h3 className="text-xl font-semibold text-card-foreground">{t('submit.detailed.title')}</h3>
              </div>
              <form onSubmit={handleDetailedSubmit} className="space-y-6">
                {/* Product Name */}
                <div>
                  <label htmlFor="productName" className={labelClass}>
                    <span className="flex items-center">
                      <Tag size={16} className="mr-2" />
                      {t('submit.detailed.productName')}
                    </span>
                  </label>
                  <input
                    type="text"
                    id="productName"
                    value={productName}
                    onChange={(e) => setProductName(e.target.value)}
                    className={inputClass}
                    placeholder={t('submit.detailed.productNamePlaceholder')}
                    required
                  />
                </div>

                {/* Website URL */}
                <div>
                  <label htmlFor="websiteUrl" className={labelClass}>
                    <span className="flex items-center">
                      <LinkIcon size={16} className="mr-2" />
                      {t('submit.detailed.websiteUrl')}
                    </span>
                  </label>
                  <input
                    type="url"
                    id="websiteUrl"
                    value={websiteUrl}
                    onChange={(e) => setWebsiteUrl(e.target.value)}
                    className={inputClass}
                    placeholder={t('submit.detailed.websiteUrlPlaceholder')}
                    required
                  />
                </div>

                {/* Logo URL */}
                <div>
                  <label htmlFor="logoUrl" className={labelClass}>{t('submit.detailed.logoUrl')}</label>
                  <input
                    type="url"
                    id="logoUrl"
                    value={logoUrl}
                    onChange={(e) => setLogoUrl(e.target.value)}
                    className={inputClass}
                    placeholder={t('submit.detailed.logoUrlPlaceholder')}
                  />
                  <p className="text-xs text-card-foreground/60 mt-2">
                    {t('submit.detailed.logoNote')}
                  </p>
                </div>

                {/* Short Description */}
                <div>
                  <label htmlFor="shortDescription" className={labelClass}>
                    {t('submit.detailed.shortDescription')}
                  </label>
                  <textarea
                    id="shortDescription"
                    value={shortDescription}
                    onChange={(e) => setShortDescription(e.target.value)}
                    className={`${textareaClass} h-24`}
                    placeholder={t('submit.detailed.shortDescriptionPlaceholder')}
                    maxLength={200}
                    required
                  />
                  <div className="text-xs text-card-foreground/60 mt-1 text-right">
                    {shortDescription.length}/200
                  </div>
                </div>

                {/* Detailed Description */}
                <div>
                  <label htmlFor="longDescription" className={labelClass}>
                    {t('submit.detailed.detailedDescription')}
                  </label>
                  <textarea
                    id="longDescription"
                    value={longDescription}
                    onChange={(e) => setLongDescription(e.target.value)}
                    className={`${textareaClass} h-40`}
                    placeholder={t('submit.detailed.detailedDescriptionPlaceholder')}
                    maxLength={2000}
                    required
                  />
                  <div className="text-xs text-card-foreground/60 mt-1 text-right">
                    {longDescription.length}/2000
                  </div>
                </div>

                {/* Category */}
                <div>
                  <label htmlFor="category" className={labelClass}>{t('submit.detailed.category')}</label>
                  <select
                    id="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className={inputClass}
                    required
                  >
                    <option value="">{t('submit.detailed.selectCategory')}</option>
                    <option value="ai-tool">{t('submit.detailed.categories.aiTool')}</option>
                    <option value="productivity">{t('submit.detailed.categories.productivity')}</option>
                    <option value="design">{t('submit.detailed.categories.design')}</option>
                    <option value="marketing">{t('submit.detailed.categories.marketing')}</option>
                    <option value="analytics">{t('submit.detailed.categories.analytics')}</option>
                    <option value="automation">{t('submit.detailed.categories.automation')}</option>
                    <option value="saas">{t('submit.detailed.categories.saas')}</option>
                    <option value="mobile-app">{t('submit.detailed.categories.mobileApp')}</option>
                    <option value="chrome-extension">{t('submit.detailed.categories.chromeExtension')}</option>
                    <option value="api">{t('submit.detailed.categories.api')}</option>
                    <option value="no-code">{t('submit.detailed.categories.noCode')}</option>
                    <option value="open-source">{t('submit.detailed.categories.openSource')}</option>
                  </select>
                </div>

                {/* Tags */}
                <div>
                  <label htmlFor="tags" className={labelClass}>{t('submit.detailed.tags')}</label>
                  <input
                    type="text"
                    id="tags"
                    value={tags}
                    onChange={(e) => setTags(e.target.value)}
                    className={inputClass}
                    placeholder={t('submit.detailed.tagsPlaceholder')}
                  />
                  <p className="text-xs text-card-foreground/60 mt-2">
                    {t('submit.detailed.tagsNote')}
                  </p>
                </div>

                {/* Visibility */}
                <div>
                  <label className={labelClass}>{t('submit.detailed.visibility')}</label>
                  <div className="flex items-center space-x-6">
                    <label className="flex items-center space-x-2 text-card-foreground cursor-pointer">
                      <input
                        type="radio"
                        name="visibility"
                        value="public"
                        checked={visibility === 'public'}
                        onChange={() => setVisibility('public')}
                        className="w-4 h-4 text-primary bg-background border-border focus:ring-primary"
                      />
                      <Eye size={16} className="text-card-foreground/70" />
                      <span>{t('submit.detailed.visibilityOptions.public')}</span>
                    </label>
                    <label className="flex items-center space-x-2 text-card-foreground cursor-pointer">
                      <input
                        type="radio"
                        name="visibility"
                        value="private"
                        checked={visibility === 'private'}
                        onChange={() => setVisibility('private')}
                        className="w-4 h-4 text-primary bg-background border-border focus:ring-primary"
                      />
                      <EyeOff size={16} className="text-card-foreground/70" />
                      <span>{t('submit.detailed.visibilityOptions.private')}</span>
                    </label>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <button
                    type="submit"
                    className="w-full bg-primary text-primary-foreground hover:bg-primary/90 font-semibold py-3 px-4 rounded-lg text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background shadow-lg hover:shadow-xl hover:scale-[1.02]"
                  >
                    {t('submit.detailed.submitButton')}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Sync Mode */}
          {submitMode === 'sync' && (
            <div className="space-y-6">
              {/* Usage Status */}
              <div className="bg-card text-card-foreground rounded-xl p-6 border border-border shadow-lg theme-transition">
                <div className="flex items-center gap-3 mb-4">
                  <Calendar size={20} className="text-primary" />
                  <h3 className="text-lg font-semibold text-card-foreground">{t('submit.sync.currentUsage')}</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="bg-background rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-card-foreground/70">{t('submit.sync.usage.syncsUsed')}</span>
                      <span className="font-medium text-card-foreground">3 / 5</span>
                    </div>
                    <div className="w-full bg-border rounded-full h-2">
                      <div className="bg-primary h-2 rounded-full" style={{ width: '60%' }}></div>
                    </div>
                  </div>
                  <div className="bg-background rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-card-foreground/70">{t('submit.sync.usage.productsParsed')}</span>
                      <span className="font-medium text-card-foreground">247 / 500</span>
                    </div>
                    <div className="w-full bg-border rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '49%' }}></div>
                    </div>
                  </div>
                  <div className="bg-background rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-card-foreground/70">{t('submit.sync.usage.plan')}</span>
                      <span className="font-medium text-card-foreground">{t('submit.sync.usage.free')}</span>
                    </div>
                    <button className="text-xs bg-primary text-primary-foreground px-3 py-1 rounded-full hover:bg-primary/90 transition-colors">
                      {t('submit.sync.usage.upgrade')}
                    </button>
                  </div>
                </div>
              </div>

              {/* Active Syncs */}
              <div className="bg-card text-card-foreground rounded-xl p-6 border border-border shadow-lg theme-transition">
                <div className="flex items-center gap-3 mb-4">
                  <Clock size={20} className="text-primary" />
                  <h3 className="text-lg font-semibold text-card-foreground">{t('submit.sync.activeSyncs', { count: 3 })}</h3>
                </div>
                <div className="space-y-3">
                  {/* Sync 1 */}
                  <div className="bg-background rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-card-foreground">Reddit r/SideProject</h4>
                        <p className="text-xs text-card-foreground/60">{t('submit.sync.syncDetails.url')} • {t('submit.sync.syncDetails.weekly')} • {t('submit.sync.syncDetails.lastRun', { time: '2 days ago' })}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-2 py-1 rounded-full">{t('submit.sync.syncStatuses.active')}</span>
                        <button className="text-xs text-card-foreground/60 hover:text-card-foreground">{t('submit.sync.syncStatuses.edit')}</button>
                      </div>
                    </div>
                    <p className="text-xs text-card-foreground/70">{t('submit.sync.syncDetails.nextRun', { time: 'In 5 days' })} • {t('submit.sync.syncDetails.productsFound', { count: 23 })}</p>
                  </div>
                  
                  {/* Sync 2 */}
                  <div className="bg-background rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-card-foreground">Discord Tech Chat Export</h4>
                        <p className="text-xs text-card-foreground/60">{t('submit.sync.syncDetails.file')} • {t('submit.sync.syncDetails.monthly')} • {t('submit.sync.syncDetails.lastRun', { time: '1 week ago' })}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-2 py-1 rounded-full">{t('submit.sync.syncStatuses.active')}</span>
                        <button className="text-xs text-card-foreground/60 hover:text-card-foreground">{t('submit.sync.syncStatuses.edit')}</button>
                      </div>
                    </div>
                    <p className="text-xs text-card-foreground/70">{t('submit.sync.syncDetails.nextRun', { time: 'In 3 weeks' })} • {t('submit.sync.syncDetails.productsFound', { count: 12 })}</p>
                  </div>
                  
                  {/* Sync 3 */}
                  <div className="bg-background rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-card-foreground">Product Hunt Daily Sync</h4>
                        <p className="text-xs text-card-foreground/60">{t('submit.sync.syncDetails.url')} • {t('submit.sync.syncDetails.daily')} • {t('submit.sync.syncDetails.lastRun', { time: '6 hours ago' })}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400 px-2 py-1 rounded-full">{t('submit.sync.syncStatuses.processing')}</span>
                        <button className="text-xs text-card-foreground/60 hover:text-card-foreground">{t('submit.sync.syncStatuses.edit')}</button>
                      </div>
                    </div>
                    <p className="text-xs text-card-foreground/70">{t('submit.sync.syncDetails.nextRun', { time: 'In 18 hours' })} • {t('submit.sync.syncDetails.productsFound', { count: 8 })}</p>
                  </div>
                </div>
              </div>

              {/* Sync Setup Form */}
              <div className="bg-card text-card-foreground rounded-xl p-6 md:p-8 border border-border shadow-lg theme-transition">
                <div className="flex items-center gap-3 mb-6">
                  <RefreshCw size={24} className="text-primary" />
                  <h3 className="text-xl font-semibold text-card-foreground">{t('submit.sync.setupNew')}</h3>
                </div>
                <form onSubmit={handleSyncSubmit} className="space-y-6">
                  {/* Sync Method */}
                  <div>
                    <label className={labelClass}>{t('submit.sync.syncMethod')}</label>
                    <div className="flex items-center space-x-6">
                      <label className="flex items-center space-x-2 text-card-foreground cursor-pointer">
                        <input
                          type="radio"
                          name="syncMethod"
                          value="file"
                          checked={syncMethod === 'file'}
                          onChange={(e) => setSyncMethod(e.target.value as 'file')}
                          className="w-4 h-4 text-primary bg-background border-border focus:ring-primary"
                        />
                        <FileText size={16} className="text-card-foreground/70" />
                        <span>{t('submit.sync.methods.file')}</span>
                      </label>
                      <label className="flex items-center space-x-2 text-card-foreground cursor-pointer">
                        <input
                          type="radio"
                          name="syncMethod"
                          value="url"
                          checked={syncMethod === 'url'}
                          onChange={(e) => setSyncMethod(e.target.value as 'url')}
                          className="w-4 h-4 text-primary bg-background border-border focus:ring-primary"
                        />
                        <LinkIcon size={16} className="text-card-foreground/70" />
                        <span>{t('submit.sync.methods.url')}</span>
                      </label>
                    </div>
                  </div>

                  {/* Sync URL or File Upload */}
                  {syncMethod === 'url' && (
                    <div>
                      <label htmlFor="syncUrl" className={labelClass}>{t('submit.sync.syncUrl')}</label>
                      <input
                        type="url"
                        id="syncUrl"
                        value={syncUrl}
                        onChange={(e) => setSyncUrl(e.target.value)}
                        className={inputClass}
                        placeholder={t('submit.sync.syncUrlPlaceholder')}
                      />
                    </div>
                  )}

                  {syncMethod === 'file' && (
                    <div>
                      <label className={labelClass}>{t('submit.sync.uploadTextFile')}</label>
                      <div 
                        className="relative border-2 border-dashed border-border rounded-lg p-6 text-center hover:border-primary/50 transition-colors cursor-pointer bg-background"
                        onClick={() => document.getElementById('uploadedFile')?.click()}
                        onDragOver={(e) => {
                          e.preventDefault();
                          e.currentTarget.classList.add('border-primary');
                        }}
                        onDragLeave={(e) => {
                          e.preventDefault();
                          e.currentTarget.classList.remove('border-primary');
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          e.currentTarget.classList.remove('border-primary');
                          const files = e.dataTransfer.files;
                          if (files && files[0]) {
                            setUploadedFile(files[0]);
                          }
                        }}
                      >
                        <input
                          type="file"
                          id="uploadedFile"
                          accept=".txt,.json"
                          onChange={handleFileUpload}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                        <div className="space-y-2">
                          <FileText size={24} className="mx-auto text-card-foreground/60" />
                          {uploadedFile ? (
                            <div>
                              <p className="font-medium text-card-foreground">{uploadedFile.name}</p>
                              <p className="text-xs text-card-foreground/60">
                                {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </div>
                          ) : (
                            <div>
                              <p className="text-card-foreground font-medium">
                                {t('submit.sync.fileUpload.chooseFile')}
                              </p>
                              <p className="text-xs text-card-foreground/60">
                                {t('submit.sync.fileUpload.fileTypes')}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                      <p className="text-xs text-card-foreground/60 mt-2">
                        {t('submit.sync.fileUpload.uploadNote')}
                      </p>
                    </div>
                  )}

                  {/* Sync Frequency */}
                  <div>
                    <label htmlFor="syncFrequency" className={labelClass}>{t('submit.sync.syncFrequency')}</label>
                    <select
                      id="syncFrequency"
                      value={syncFrequency}
                      onChange={(e) => setSyncFrequency(e.target.value)}
                      className={inputClass}
                      required
                    >
                      <option value="daily">{t('submit.sync.frequencies.daily')}</option>
                      <option value="weekly">{t('submit.sync.frequencies.weekly')}</option>
                      <option value="monthly">{t('submit.sync.frequencies.monthly')}</option>
                    </select>
                  </div>

                  {/* Sync Description */}
                  <div>
                    <label htmlFor="syncDescription" className={labelClass}>{t('submit.sync.syncDescription')}</label>
                    <textarea
                      id="syncDescription"
                      value={syncDescription}
                      onChange={(e) => setSyncDescription(e.target.value)}
                      className={`${textareaClass} h-24`}
                      placeholder={t('submit.sync.syncDescriptionPlaceholder')}
                      maxLength={500}
                    />
                    <div className="text-xs text-card-foreground/60 mt-1 text-right">
                      {syncDescription.length}/500
                    </div>
                  </div>

                  {/* Monthly Limits Warning */}
                  <div className="bg-orange-100 dark:bg-orange-900/30 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle size={20} className="text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-orange-700 dark:text-orange-300 mb-2">{t('submit.sync.monthlyLimits')}</h4>
                        <ul className="text-sm text-orange-600 dark:text-orange-400 space-y-1">
                          <li>• <strong>{t('submit.sync.limitWarnings.free')}</strong></li>
                          <li>• <strong>{t('submit.sync.limitWarnings.pro')}</strong></li>
                          <li>• <strong>{t('submit.sync.limitWarnings.enterprise')}</strong></li>
                          <li>• {t('submit.sync.limitWarnings.processing')}</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Sync Method Details */}
                  <div className="bg-primary/10 rounded-lg p-4">
                    <h4 className="font-medium text-primary mb-3">{t('submit.sync.supportedTypes')}</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="font-medium text-sm text-card-foreground mb-1">{t('submit.sync.fileUploadDetails.title')}</p>
                        <ul className="text-sm text-card-foreground/70 space-y-1 ml-4">
                          <li>• {t('submit.sync.fileUploadDetails.chatExports')}</li>
                          <li>• {t('submit.sync.fileUploadDetails.productLists')}</li>
                          <li>• {t('submit.sync.fileUploadDetails.maxSize')}</li>
                          <li>• {t('submit.sync.fileUploadDetails.aiExtract')}</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium text-sm text-card-foreground mb-1">{t('submit.sync.urlSourceDetails.title')}</p>
                        <ul className="text-sm text-card-foreground/70 space-y-1 ml-4">
                          <li>• {t('submit.sync.urlSourceDetails.reddit')}</li>
                          <li>• {t('submit.sync.urlSourceDetails.rss')}</li>
                          <li>• {t('submit.sync.urlSourceDetails.productHunt')}</li>
                          <li>• {t('submit.sync.urlSourceDetails.customJson')}</li>
                          <li>• {t('submit.sync.urlSourceDetails.mcp')}</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-4">
                    <button
                      type="submit"
                      className="w-full bg-primary text-primary-foreground hover:bg-primary/90 font-semibold py-3 px-4 rounded-lg text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background shadow-lg hover:shadow-xl hover:scale-[1.02]"
                    >
                      {t('submit.sync.submitButton')}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainAppLayout>
  );
} 