'use client';

import { useState } from 'react';
import { Zap, Crown, Sparkles } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';

export default async function SubscriptionPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'annually'>('monthly');
  
  // 定价配置
  const deductRatio = 1.0; // 100% off
  const basePrices = {
    standard: 0,
    pro: 20,
    studio: 60,
    community: 2000
  };

  // 价格计算函数
  const calculatePrice = (basePriceMonthly: number, period: 'monthly' | 'annually') => {
    if (period === 'monthly') {
      const discountedPrice = basePriceMonthly * (1 - deductRatio);
      return discountedPrice === 0 ? 'Free' : `$${discountedPrice}`;
    } else {
      const yearlyPrice = basePriceMonthly * 9; // 9个月的基础月费
      return yearlyPrice === 0 ? 'Free' : `$${yearlyPrice}`;
    }
  };

  const getOriginalPrice = (basePriceMonthly: number, period: 'monthly' | 'annually') => {
    if (basePriceMonthly === 0) return undefined;
    if (period === 'monthly') {
      return `$${basePriceMonthly}/month`;
    } else {
      return `$${basePriceMonthly * 12}/year`;
    }
  };

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 theme-transition">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center mb-4">
              <Crown className="text-logo mr-3" size={48} />
              <h1 className="text-4xl md:text-5xl font-bold text-foreground leading-tight">
                Subscription
              </h1>
            </div>
            <div className="mt-6 inline-flex items-center bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
              <Zap size={16} className="mr-2" />
              100% off - Limited Time Offer!
            </div>
          </div>

          {/* Billing Period Toggle */}
          <div className="flex justify-center">
            <div className="bg-card border border-border rounded-full p-1 theme-transition relative">
              <div className="flex">
                <button
                  onClick={() => setBillingPeriod('monthly')}
                  className={`px-6 py-3 text-sm font-medium rounded-full transition-all duration-200 relative ${
                    billingPeriod === 'monthly'
                      ? 'bg-primary text-primary-foreground shadow-sm'
                      : 'text-card-foreground hover:text-card-foreground/80'
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setBillingPeriod('annually')}
                  className={`px-6 py-3 text-sm font-medium rounded-full transition-all duration-200 relative ${
                    billingPeriod === 'annually'
                      ? 'bg-primary text-primary-foreground shadow-sm'
                      : 'text-card-foreground hover:text-card-foreground/80'
                  }`}
                >
                  Annually
                  {/* Silver metallic -25% badge */}
                  <div className="absolute -top-2 -right-2">
                    <div className="bg-gradient-to-r from-slate-300 via-slate-200 to-slate-300 text-slate-700 px-2 py-1 rounded-full text-xs font-bold shadow-lg border border-slate-400/30 flex items-center">
                      <Sparkles size={10} className="mr-1 text-slate-500" />
                      -25%
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Current Plan Status */}
          <div className="bg-card text-card-foreground rounded-xl p-6 border border-border theme-transition">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Crown className="text-primary" size={24} />
                </div>
                <div>
                  <h3 className="font-semibold text-card-foreground">Current Plan: Pro</h3>
                  <p className="text-sm text-card-foreground/60">Next billing: December 15, 2024 • $10/month</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <button className="text-primary hover:text-primary/80 text-sm font-medium transition-colors">
                  Manage Billing
                </button>
                <button className="bg-card-foreground/10 text-card-foreground hover:bg-card-foreground/20 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                  Download Invoice
                </button>
              </div>
            </div>
          </div>

          {/* Plans Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <PlanCard
              name="Standard"
              price={calculatePrice(basePrices.standard, billingPeriod)}
              period={billingPeriod === 'monthly' ? 'forever' : 'year'}
              originalPrice={getOriginalPrice(basePrices.standard, billingPeriod)}
              discount={basePrices.standard > 0 && billingPeriod === 'monthly' ? '100% off' : undefined}
              seats="1 seat"
              buttonType="cancel"
              features={[
                "Browse unlimited products",
                "Basic product submission",
                "Community discussions",
                "Standard support",
                "Basic search functionality"
              ]}
            />
            
            <PlanCard
              name="Pro"
              price={calculatePrice(basePrices.pro, billingPeriod)}
              period={billingPeriod === 'monthly' ? 'month' : 'year'}
              originalPrice={getOriginalPrice(basePrices.pro, billingPeriod)}
              discount={billingPeriod === 'monthly' ? '100% off' : undefined}
              seats="1 seat"
              isPopular={true}
              isCurrent={true}
              buttonType="current"
              features={[
                "Everything in Standard",
                "Advanced analytics dashboard",
                "Priority product review (24h)",
                "Custom profile page hosting",
                "API access (10,000 calls/month)",
                "Priority email support",
                "Advanced search & filters"
              ]}
            />
            
            <PlanCard
              name="Studio"
              price={calculatePrice(basePrices.studio, billingPeriod)}
              period={billingPeriod === 'monthly' ? 'month' : 'year'}
              originalPrice={getOriginalPrice(basePrices.studio, billingPeriod)}
              discount={billingPeriod === 'monthly' ? '100% off' : undefined}
              seats="3 seats"
              buttonType="upgrade"
              features={[
                "Everything in Pro",
                "Team collaboration workspace",
                "Advanced team analytics",
                "Custom branding options",
                "Enhanced API access (50K calls/month)",
                "Team management tools",
                "Priority support"
              ]}
            />

            <PlanCard
              name="Community"
              price={calculatePrice(basePrices.community, billingPeriod)}
              period={billingPeriod === 'monthly' ? 'month' : 'year'}
              originalPrice={getOriginalPrice(basePrices.community, billingPeriod)}
              discount={billingPeriod === 'monthly' ? '100% off' : undefined}
              seats="300 seats"
              buttonType="upgrade"
              features={[
                "Everything in Studio",
                "White-label solutions",
                "Custom integrations",
                "Unlimited API access",
                "Dedicated account manager",
                "24/7 priority support",
                "Early access to new features",
                "Community management tools"
              ]}
            />
          </div>

          <div className="mt-12 grid gap-8 lg:grid-cols-2">
            {/* {plans.map((plan) => (
              <PlanCard
                key={plan.name}
                plan={plan}
                isMonthly={billingPeriod === 'monthly'}
                isPopular={plan.popular}
              />
            ))} */}
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
} 