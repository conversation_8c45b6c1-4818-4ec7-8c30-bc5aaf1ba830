'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { Trophy, Award, Compass, Users, TrendingUp, Construction } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';

export default function TrophiesPage() {
  const { lang } = useParams() as { lang: string };
  const { t } = useTranslation(lang, 'translation');
  const [activeCategory, setActiveCategory] = useState<'award' | 'exploration' | 'social' | 'ranking'>('award');

  const CategoryButton = ({ category, label, icon, isActive, onClick }: {
    category: string;
    label: string;
    icon: React.ReactNode;
    isActive: boolean;
    onClick: () => void;
  }) => {
    return (
      <button
        onClick={onClick}
        className={`px-3 sm:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center gap-2 whitespace-nowrap relative ${
          isActive
            ? 'bg-primary text-primary-foreground shadow-sm'
            : 'text-foreground hover:text-foreground'
        }`}
      >
        {icon}
        <span className={isActive ? 'text-primary-foreground' : 'text-foreground'}>
          {label} (0/0)
        </span>
      </button>
    );
  };

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-background theme-transition">
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          {/* Hero Section */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center mb-4">
              <Trophy className="text-logo mr-3" size={48} />
              <h1 className="text-4xl md:text-5xl font-bold text-foreground leading-tight">
                {t('trophies.title')}
              </h1>
            </div>
          </div>

          {/* Category Navigation */}
          <div className="flex bg-card border border-border rounded-lg p-1 theme-transition overflow-x-auto max-w-fit mx-auto">
            <CategoryButton
              category="award"
              label={t('trophies.categories.award')}
              icon={<Award size={16} className={activeCategory === 'award' ? 'text-primary-foreground' : 'text-foreground'} />}
              isActive={activeCategory === 'award'}
              onClick={() => setActiveCategory('award')}
            />
            <CategoryButton
              category="exploration"
              label={t('trophies.categories.exploration')}
              icon={<Compass size={16} className={activeCategory === 'exploration' ? 'text-primary-foreground' : 'text-foreground'} />}
              isActive={activeCategory === 'exploration'}
              onClick={() => setActiveCategory('exploration')}
            />
            <CategoryButton
              category="social"
              label={t('trophies.categories.social')}
              icon={<Users size={16} className={activeCategory === 'social' ? 'text-primary-foreground' : 'text-foreground'} />}
              isActive={activeCategory === 'social'}
              onClick={() => setActiveCategory('social')}
            />
            <CategoryButton
              category="ranking"
              label={t('trophies.categories.ranking')}
              icon={<TrendingUp size={16} className={activeCategory === 'ranking' ? 'text-primary-foreground' : 'text-foreground'} />}
              isActive={activeCategory === 'ranking'}
              onClick={() => setActiveCategory('ranking')}
            />
          </div>

          {/* Under Construction Section */}
          <div className="flex flex-col items-center justify-center py-16 space-y-8">
            {/* Construction Logo */}
            <div className="relative">
              <div className="w-48 h-48 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center animate-pulse">
                <Construction className="w-24 h-24 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
            
            {/* Construction Message */}
            <div className="text-center space-y-4">
              <h2 className="text-3xl font-bold text-foreground">
                {t('trophies.underConstruction.title')}
              </h2>
              <p className="text-lg text-muted-foreground max-w-md mx-auto">
                {t('trophies.underConstruction.description')}
              </p>
            </div>
            
            {/* Construction Elements */}
            <div className="flex items-center space-x-8 mt-8">
              <div className="w-4 h-4 bg-yellow-500 rounded-full animate-bounce"></div>
              <div className="w-4 h-4 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-4 h-4 bg-red-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
}