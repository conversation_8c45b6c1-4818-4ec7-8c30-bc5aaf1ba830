@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors - using consistent RGB format */
    --background: 255 255 255;
    --foreground: 30 58 138;
    --card: 255 255 255;
    --card-foreground: 30 58 138;
    --popover: 255 255 255;
    --popover-foreground: 30 58 138;
    --primary: 30 58 138;
    --primary-foreground: 255 255 255;
    --secondary: 240 244 248;
    --secondary-foreground: 30 58 138;
    --muted: 240 244 248;
    --muted-foreground: 100 116 139;
    --accent: 30 58 138;
    --accent-foreground: 255 255 255;
    --destructive: 220 38 38;
    --destructive-foreground: 255 255 255;
    --border: 226 232 240;
    --input: 255 255 255;
    --ring: 30 58 138;
    --logo: 30 58 138;
    --icon: 30 58 138;
    --radius: 0.5rem;
    --header-foreground: 30 58 138;
  }

  .dark {
    /* Dark theme colors - using consistent RGB format */
    --background: 0 0 0;
    --foreground: 255 235 59;
    --card: 0 0 0;
    --card-foreground: 255 235 59;
    --popover: 0 0 0;
    --popover-foreground: 255 235 59;
    --primary: 255 235 59;
    --primary-foreground: 0 0 0;
    --secondary: 23 23 23;
    --secondary-foreground: 255 235 59;
    --muted: 23 23 23;
    --muted-foreground: 163 163 163;
    --accent: 255 235 59;
    --accent-foreground: 0 0 0;
    --destructive: 244 67 54;
    --destructive-foreground: 255 255 255;
    --border: 64 64 64;
    --input: 0 0 0;
    --ring: 255 241 118;
    --logo: 255 235 59;
    --icon: 255 255 255;
    --header-foreground: 255 241 118;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  html {
    transition: all 0.3s ease;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgb(var(--foreground) / 0.2);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgb(var(--foreground) / 0.3);
  }

  /* Enhanced specificity for darker text for descriptions */
  .dark [class*="text-foreground/"], 
  .dark [class*="text-card-foreground/"] {
    color: rgb(255 255 255 / 0.7) !important;
  }
}

/* Theme transition utility */
@layer utilities {
  .theme-transition {
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Override hardcoded colors to use system colors */
@layer utilities {
  /* Light mode overrides */
  html:not(.dark) .text-blue-600,
  html:not(.dark) .text-blue-700 {
    color: rgb(var(--primary)) !important;
  }
  
  html:not(.dark) .hover\:text-blue-600:hover,
  html:not(.dark) .hover\:text-blue-700:hover {
    color: rgb(var(--primary)) !important;
  }
  
  html:not(.dark) .bg-blue-600,
  html:not(.dark) .bg-blue-700 {
    background-color: rgb(var(--primary)) !important;
  }
  
  html:not(.dark) .text-white {
    color: rgb(var(--primary-foreground)) !important;
  }
  
  /* Dark mode overrides */
  .dark .text-blue-600,
  .dark .text-blue-700,
  .dark .hover\:text-blue-600:hover,
  .dark .hover\:text-blue-700:hover {
    color: rgb(var(--primary)) !important;
  }
  
  .dark .bg-blue-600,
  .dark .bg-blue-700 {
    background-color: rgb(var(--primary)) !important;
  }
  
  .dark .text-white {
    color: rgb(var(--icon)) !important;
  }
  
  /* System color utilities for future use */
  .text-system {
    color: rgb(var(--foreground));
  }
  
  .text-system-muted {
    color: rgb(var(--muted-foreground));
  }
  
  .text-system-primary {
    color: rgb(var(--primary));
  }
  
  .bg-system {
    background-color: rgb(var(--background));
  }
  
  .bg-system-card {
    background-color: rgb(var(--card));
  }
  
  .bg-system-primary {
    background-color: rgb(var(--primary));
  }
  
  .border-system {
    border-color: rgb(var(--border));
  }
} 