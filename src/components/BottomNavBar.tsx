'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { LayoutGrid, MessageSquare, Compass, Square, User } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { TFunction } from 'i18next';

const mobileNavLinks = (lang: string, t: TFunction) => [
  { href: `/${lang}`, label: t('nav.home', 'Home'), icon: LayoutGrid },
  { href: `/${lang}/chats`, label: t('nav.chats', 'Chats'), icon: MessageSquare },
  { href: `/${lang}/discover`, label: t('nav.discover', 'Discover'), icon: Compass },
  { href: `/${lang}/square`, label: t('nav.square', 'Square'), icon: Square },
  { href: `/${lang}/profile`, label: t('nav.profile', 'Profile'), icon: User },
];

function classNames(...classes: (string | boolean)[]) {
  return classes.filter(Boolean).join(' ');
}

export default function BottomNavBar({ lang }: { lang: string }) {
  const pathname = usePathname();
  const { t } = useTranslation(lang, 'translation');
  const navLinks = mobileNavLinks(lang, t);

  const isCurrentPage = (href: string) => {
    if (href === `/${lang}`) {
      return pathname === `/${lang}`;
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className="md:hidden fixed bottom-0 left-0 right-0 z-40 bg-white/80 dark:bg-black/80 backdrop-blur-sm border-t border-gray-200 dark:border-gray-800">
      <div className="flex justify-around items-center h-20">
        {navLinks.map((link) => (
          <Link
            href={link.href}
            key={link.label}
            className={classNames(
              'flex flex-col items-center justify-center w-full text-xs transition-colors h-full',
              isCurrentPage(link.href)
                ? 'text-indigo-600 dark:text-indigo-400'
                : 'text-gray-500 hover:text-indigo-600 dark:hover:text-indigo-400'
            )}
          >
            <link.icon className="h-6 w-6 mb-1" />
            <span className="font-medium">{link.label}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
} 