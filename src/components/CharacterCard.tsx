import React from 'react';
import Image from 'next/image';
import { Heart, Users, Share2, <PERSON>r<PERSON><PERSON>, BadgeCheck } from 'lucide-react';
import Link from 'next/link';
import { Character } from '@/lib/mock-data';

interface CharacterCardProps {
  lang: string;
  character: Character;
  stats: {
    likes: number;
    friends: number;
    shares: number;
  };
  aspectRatio: number;
}

const CharacterCard: React.FC<CharacterCardProps> = ({ lang, character, stats, aspectRatio }) => {
  return (
    <Link href={`/${lang}/chats/${character.id}`} className="block group w-full">
      <div className="relative overflow-hidden rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
        {/* Placeholder to maintain aspect ratio */}
        <div style={{ paddingBottom: `${(1 / aspectRatio) * 100}%` }} />

        {/* Background */}
        {character.character_bg_image && (
          <Image
            src={character.character_bg_image}
            alt="bg"
            fill
            className="absolute top-0 left-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            unoptimized
          />
        )}

        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />

        {/* Bottom content */}
        <div className="absolute inset-0 p-4 flex items-end">
          <div className="flex items-center gap-3 w-full">
            <Image
              src={character.character_avatar}
              alt={character.name}
              width={56}
              height={56}
              className="w-14 h-14 rounded-full border-2 border-white/60 flex-shrink-0 object-cover"
              unoptimized
            />
            <div className="flex flex-col flex-1 min-w-0 text-white">
              <div className="flex items-center justify-between">
                <p className="font-bold truncate text-sm sm:text-base">{character.name}</p>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <BadgeCheck size={16} className="text-yellow-300" />
                  <QrCode size={16} />
                </div>
              </div>
              <div className="flex items-center gap-3 mt-1 text-white/90 text-xs">
                <span className="flex items-center gap-0.5"><Users size={14} />{stats.friends}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CharacterCard; 