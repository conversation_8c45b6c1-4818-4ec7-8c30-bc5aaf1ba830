'use client';

import { FC } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface ChatRowProps {
  lang: string;
  character: {
    id: string;
    name: string;
    character_avatar: string;
    character_bg_image?: string; // Optional background for subtle effect
  };
  lastMessage: {
    text: string;
    timestamp: string;
  };
}

const ChatRow: FC<ChatRowProps> = ({ lang, character, lastMessage }) => {
  return (
    <Link href={`/${lang}/chats/${character.id}`} className="block w-full h-24 bg-white dark:bg-gray-800 transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-gray-700">
      <div className="flex items-center h-full p-2 sm:p-4 relative overflow-hidden">
        {/* Subtle background image */}
        {character.character_bg_image && (
          <Image
            src={character.character_bg_image}
            alt=""
            fill
            className="object-cover opacity-10 dark:opacity-5"
            unoptimized
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-r from-white via-white/80 to-transparent dark:from-gray-800 dark:via-gray-800/80 dark:to-transparent" />

        {/* Content */}
        <div className="flex items-center w-full gap-2 sm:gap-4 z-10">
          <div className="flex-shrink-0">
            <Image
              src={character.character_avatar}
              alt={character.name}
              width={64}
              height={64}
              className="rounded-full object-cover w-12 h-12 sm:w-16 sm:h-16 border-4 border-white dark:border-gray-700"
              unoptimized
            />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-baseline">
              <p className="text-base sm:text-lg font-bold text-gray-900 dark:text-white truncate min-w-0">{character.name}</p>
              <p className="text-xs text-gray-400 dark:text-gray-500 ml-2 flex-shrink-0">{lastMessage.timestamp}</p>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">{lastMessage.text}</p>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ChatRow; 