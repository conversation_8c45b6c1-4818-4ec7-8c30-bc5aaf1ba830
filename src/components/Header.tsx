'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { Settings, Search, Gift, Square, Bell, SunMoon } from 'lucide-react';
import UserInfoBar from './UserInfoBar';
import ThemeToggle from './ThemeToggle';

const SettingsDropdown = ({ lang }: { lang: string }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [dropdownRef]);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 text-gray-500 hover:text-indigo-600 transition-colors"
      >
        <Settings />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-60 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 animate-fade-in-down">
          <div className="p-2">
            <Link href={`/${lang}/square`} className="flex items-center gap-3 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
              <Square size={18} />
              <span>Square</span>
            </Link>
            <Link href={`/${lang}/gift`} className="flex items-center gap-3 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
              <Gift size={18} />
              <span>Gift</span>
            </Link>
            <Link href={`/${lang}/notifications`} className="flex items-center gap-3 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                <Bell size={18} />
                <span>Notifications</span>
                <span className="absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500" />
            </Link>
            <div className="border-t border-gray-100 dark:border-gray-700 my-1"></div>
            <div className="flex items-center justify-between w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md">
                <div className="flex items-center gap-3">
                    <SunMoon size={18} />
                    <span>Theme</span>
                </div>
                <ThemeToggle />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

interface HeaderProps {
  lang: string;
  onToggleSidebar: () => void;
  onSearchClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ lang, onToggleSidebar, onSearchClick }) => {
  return (
    <header className="sticky top-0 z-20 min-h-20 bg-white/80 dark:bg-black/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-800">
      <div className="flex items-center justify-between h-full px-4 lg:px-6">
        <div className="flex items-center gap-2 h-full">
          <UserInfoBar onLevelClick={onToggleSidebar} />
        </div>

        {/* Center placeholder / title */}
        <div className="flex-1 max-w-md mx-4 hidden lg:flex">
          <h1 className="text-xl font-semibold" />
        </div>

        {/* Header right-side actions */}
        <div className="flex items-center gap-2 ml-auto">
          <button
            onClick={onSearchClick}
            className="p-2 text-gray-500 hover:text-indigo-600 transition-colors"
          >
            <Search />
          </button>
          <SettingsDropdown lang={lang} />
        </div>
      </div>
    </header>
  );
};

export default Header; 