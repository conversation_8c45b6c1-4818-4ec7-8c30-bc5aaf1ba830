'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Home, User, Settings, UserPlus, ChevronLeft, ChevronRight, X, Globe, ChevronDown, Check, Trophy, PlusCircle, ClipboardList, Shield, MessageSquare, Flame, Search, Gift, LayoutGrid, Users, Compass, Gem, Puzzle, Droplet, Square, SunMoon } from 'lucide-react';
import Tooltip from './Tooltip';
import { usePathname, useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { i18n } from '@/i18n-config';
import SearchOverlay from './SearchOverlay';
import BottomNavBar from './BottomNavBar';
import Header from './Header';

interface MainAppLayoutProps {
  children: React.ReactNode;
  lang: string;
}

function classNames(...classes: (string | boolean)[]) {
  return classes.filter(Boolean).join(' ');
}

const MainAppLayout: React.FC<MainAppLayoutProps> = ({ children, lang }) => {
  const pathname = usePathname();
  const router = useRouter();

  const { t } = useTranslation(lang, 'translation');

  const navLinks = [
    { href: `/${lang}`, label: t('nav.home'), icon: Home },
    { href: `/${lang}/create-character`, label: t('nav.createCharacter'), icon: PlusCircle },
    { href: `/${lang}/daily-tasks`, label: t('nav.dailyTasks'), icon: ClipboardList, badge: 3 },
    { href: `/${lang}/battle-pass`, label: t('nav.battlePass'), icon: Shield, status: 'Lv.12' },
  ];

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  
  const languages = i18n.locales.map(loc => ({
    code: loc,
    name: loc === 'en' ? 'English' : loc === 'zh' ? '中文' : '日本語',
    flag: loc === 'en' ? '🇺🇸' : loc === 'zh' ? '🇨🇳' : '🇯🇵',
  }));
  const currentLanguage = languages.find(l => l.code === lang) || languages[0];

  React.useEffect(() => {
    setIsSidebarOpen(window.innerWidth >= 1024);
  }, []);

  React.useEffect(() => {
    if (window.innerWidth < 1024) {
      setIsSidebarOpen(false);
    }
  }, [pathname]);

  const isCurrentPage = (href: string) => {
    const currentPath = pathname;
    if (href === `/${lang}`) {
      return currentPath === `/${lang}`;
    }
    return currentPath.startsWith(href);
  };
  
  const handleLanguageChange = (code: string) => {
    const newPath = pathname.replace(/^\/[^/]+/, `/${code}`);
    router.push(newPath);
  };

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Brand Header */}
      <div className="flex items-center justify-start gap-3 px-6 h-20 border-b border-gray-100 dark:border-gray-800">
          <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <span className="text-white text-xl font-bold">A</span>
          </div>
          <div>
              <h1 className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-pink-500">Alphane.ai</h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">{t('common.tagline')}</p>
          </div>
      </div>

      {/* User Status */}
      <div className="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 border-b border-gray-100 dark:border-gray-800">
          <div className="grid grid-cols-2 gap-2 mb-4">
              {/* Tokens */}
              <div className="bg-white/70 dark:bg-black/20 backdrop-blur-sm border border-white/20 rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                      <span className="text-orange-500 text-sm"><Flame size={14}/></span>
                      <span className="text-xs font-medium text-gray-600 dark:text-gray-300">{t('tokens.token1')}</span>
                  </div>
                  <p className="text-sm font-bold text-gray-800 dark:text-gray-100">1,250</p>
              </div>
              <div className="bg-white/70 dark:bg-black/20 backdrop-blur-sm border border-white/20 rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                      <span className="text-blue-500 text-sm"><Gem size={14}/></span>
                      <span className="text-xs font-medium text-gray-600 dark:text-gray-300">{t('tokens.token2')}</span>
                  </div>
                  <p className="text-sm font-bold text-gray-800 dark:text-gray-100">89</p>
              </div>
              <div className="bg-white/70 dark:bg-black/20 backdrop-blur-sm border border-white/20 rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                      <span className="text-purple-500 text-sm"><Puzzle size={14}/></span>
                      <span className="text-xs font-medium text-gray-600 dark:text-gray-300">{t('tokens.token3')}</span>
                  </div>
                  <p className="text-sm font-bold text-gray-800 dark:text-gray-100">23</p>
              </div>
              <div className="bg-white/70 dark:bg-black/20 backdrop-blur-sm border border-white/20 rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                      <span className="text-pink-500 text-sm"><Droplet size={14}/></span>
                      <span className="text-xs font-medium text-gray-600 dark:text-gray-300">{t('tokens.token4')}</span>
                  </div>
                  <p className="text-sm font-bold text-gray-800 dark:text-gray-100">157</p>
              </div>
          </div>

          {/* Streak */}
          <div className="bg-white dark:bg-black/20 rounded-lg p-3 shadow-sm">
              <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                      <Flame className="text-orange-500 text-lg" />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-200">{t('common.streak')}</span>
                  </div>
                  <span className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-pink-500">15 {t('common.days')}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-gradient-to-r from-orange-400 to-red-500 h-2 rounded-full" style={{width: "75%"}}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{t('common.streakMilestone', { days: 6 })}</p>
          </div>
      </div>

      {/* Nav Menu */}
      <nav className="flex-1 overflow-y-auto p-4">
          <div className="space-y-2 mb-6">
              {navLinks.map(link => (
                  <Link href={link.href} key={link.label} className="flex items-center gap-3 p-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/50 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
                      <link.icon className="text-xl" />
                      <span className="font-medium">{link.label}</span>
                      {link.badge && <span className="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">{link.badge}</span>}
                      {link.status && <span className="ml-auto text-xs text-indigo-600 dark:text-indigo-400 font-medium">{link.status}</span>}
                  </Link>
              ))}
          </div>

          {/* Recent Chats */}
          <div className="mb-6">
              <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3 px-2">{t('common.recentChats')}</h3>
              <div className="space-y-1">
                  {/* Chat Item 1 */}
                  <Link href="#" className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <Image src="https://picsum.photos/32/32?random=1" alt="Sophie" width={32} height={32} className="w-8 h-8 rounded-full border-2 border-pink-200"/>
                      <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-200 truncate">温柔的Sophie</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">想和你分享今天的心情...</p>
                      </div>
                      <span className="text-xs text-pink-500 bg-pink-50 dark:bg-pink-900/50 dark:text-pink-400 px-2 py-1 rounded-full">❤️Lv.3</span>
                  </Link>
                   {/* Chat Item 2 */}
                  <Link href="#" className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <Image src="https://picsum.photos/32/32?random=2" alt="Alex" width={32} height={32} className="w-8 h-8 rounded-full border-2 border-blue-200"/>
                      <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-200 truncate">冒险家Alex</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">准备好下一次探险了吗？</p>
                      </div>
                      <span className="text-xs text-blue-500 bg-blue-50 dark:bg-blue-900/50 dark:text-blue-400 px-2 py-1 rounded-full">🎯Lv.2</span>
                  </Link>
              </div>
              <Link href="#" className="flex items-center justify-center gap-2 p-3 mt-2 text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
                  <MessageSquare size={16} />
                  <span className="text-sm">{t('common.viewAllChats')}</span>
              </Link>
          </div>

          {/* Membership Promo */}
          <div className="bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl p-4 text-white">
              <h4 className="font-bold mb-1">{t('common.promoTitle')}</h4>
              <p className="text-xs opacity-90 mb-3">{t('common.promoSubtitle')}</p>
              <button className="w-full bg-white text-purple-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                  {t('common.upgradeNow')}
              </button>
          </div>
      </nav>
      
      {/* Bottom Section */}
      <div>
        {/* User Info */}
        <div className="border-t border-gray-200 dark:border-gray-700 px-4 h-20">
            <div className="flex items-center gap-3 h-full">
                <Image src="https://picsum.photos/40/40?random=user" alt="User" width={40} height={40} className="w-10 h-10 rounded-full border-2 border-indigo-200"/>
                <div className="flex-1">
                    <p className="font-medium text-gray-800 dark:text-gray-100">月光下的旅人</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Alphane Pass {t('common.member')}</p>
                </div>
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <Settings size={20} />
                </button>
            </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-50">
      <div className="flex-1 flex overflow-hidden">
        {/* Desktop Sidebar */}
        <aside className="hidden lg:flex w-80 flex-col bg-white dark:bg-black border-r border-gray-200 dark:border-gray-800">
          <SidebarContent />
        </aside>
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-y-auto pb-20 md:pb-0">
          <Header lang={lang} onToggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} onSearchClick={() => setIsSearchOpen(true)} />
          
          <main>
            {children}
          </main>
        </div>

        {/* Mobile Sidebar overlay */}
        {isSidebarOpen && (
            <div className="lg:hidden fixed inset-0 z-40 bg-black/30" onClick={() => setIsSidebarOpen(false)}></div>
        )}
        <aside
          className={classNames(
            "lg:hidden fixed top-0 left-0 z-50 h-full w-80 bg-white dark:bg-black shadow-lg transition-transform duration-300 ease-in-out",
            isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
          )}
        >
          <SidebarContent />
        </aside>
      </div>

      {/* Bottom Nav Bar (Mobile) */}
      <BottomNavBar lang={lang} />
      
      {isSearchOpen && <SearchOverlay isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />}
    </div>
  );
};

export default MainAppLayout;
