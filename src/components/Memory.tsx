import React from 'react';
import Image from 'next/image';

interface Message {
  id: number;
  side: 'left' | 'right';
  avatar: string;
  name: string;
  text: string;
  timestamp: string; // Can be relative or absolute
}

interface MemoryProps {
    messages: Message[];
}

const Memory: React.FC<MemoryProps> = ({ messages }) => {
  if (!messages || messages.length === 0) {
    return null;
  }

  return (
    <div className="w-full bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 space-y-4">
      {messages.map((msg) => (
        <div key={msg.id} className={`flex ${msg.side === 'right' ? 'flex-row-reverse' : ''} items-start gap-2`}>
          <Image
            src={msg.avatar}
            alt={msg.name}
            width={40}
            height={40}
            className="w-10 h-10 rounded-full flex-shrink-0"
            unoptimized
          />
          <div className={`max-w-[75%] rounded-2xl px-4 py-2 text-sm whitespace-pre-wrap ${msg.side === 'right' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-bl-none'}`}>
            {msg.text}
          </div>
        </div>
      ))}
      {/* Timestamp of the last message */}
      <p className="text-xs text-gray-400 text-right mt-2">{messages[messages.length - 1].timestamp}</p>
    </div>
  );
};

export default Memory;
export type { Message as MemoryMessage }; 