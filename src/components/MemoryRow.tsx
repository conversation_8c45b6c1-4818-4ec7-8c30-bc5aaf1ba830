'use client';

import React from 'react';
import Image from 'next/image';
import { Clock } from 'lucide-react';

interface MemoryRowProps {
  character: {
    id: string;
    name: string;
    character_avatar: string;
  };
  memory: {
    id: string;
    text: string;
    timestamp: string;
  };
  onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const MemoryRow: React.FC<MemoryRowProps> = ({ character, memory, onClick }) => {
  return (
    <div
      onClick={onClick}
      className="memory-capsule p-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg cursor-pointer"
    >
      <div className="flex items-start gap-4">
        {/* Avatar */}
        <Image
          src={character.character_avatar}
          alt={character.name}
          width={40}
          height={40}
          className="w-10 h-10 rounded-full mt-1 flex-shrink-0"
          unoptimized
        />

        {/* Content */}
        <div className="flex-1">
          <p className="font-semibold text-slate-800 dark:text-slate-100 mb-2 line-clamp-2">
            {memory.text}
          </p>
          <div className="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
            <span className="font-medium">{character.name}</span>
            <div className="flex items-center gap-1">
              <Clock size={12} />
              <span>{memory.timestamp}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemoryRow; 