'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Heart, MessageCircle, Share2, Di<PERSON>, X } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Character, SampleMoment } from '@/lib/mock-data';

interface MomentCardProps {
  lang: string;
  character: Character;
  moment: SampleMoment['moment'];
  stats: SampleMoment['stats'];
  aspectRatio: number;
  publishedAt: string;
  storyTemplateId: string;
}

const ShareBottomSheet = ({ open, onClose, onShare }: { open: boolean, onClose: () => void, onShare: (platform: string) => void }) => {
  if (!open) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/50 z-50 flex justify-center items-end"
      onClick={onClose}
    >
      <div 
        className="bg-background w-full max-w-md rounded-t-2xl p-4 shadow-lg"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Share Moment</h3>
          <button onClick={onClose} className="p-1 rounded-full hover:bg-muted">
            <X size={20} />
          </button>
        </div>
        <div className="grid grid-cols-3 gap-4 text-center">
          <button onClick={() => onShare('twitter')} className="p-2 rounded-lg hover:bg-muted">Twitter</button>
          <button onClick={() => onShare('facebook')} className="p-2 rounded-lg hover:bg-muted">Facebook</button>
          <button onClick={() => onShare('copy')} className="p-2 rounded-lg hover:bg-muted">Copy Link</button>
        </div>
      </div>
    </div>
  );
};

const MomentCard: React.FC<MomentCardProps> = ({ lang, character, moment, stats, aspectRatio, publishedAt, storyTemplateId }) => {
  const router = useRouter();
  const [momentImgSrc, setMomentImgSrc] = useState(moment.image);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(stats.likes);
  const [isShareSheetOpen, setIsShareSheetOpen] = useState(false);

  const getAspectRatioClass = () => {
    // Using a small tolerance for float comparison
    if (Math.abs(aspectRatio - (5/10)) < 0.01) return 'aspect-[5/10]';
    if (Math.abs(aspectRatio - (5/8)) < 0.01) return 'aspect-[5/8]';
    return 'aspect-[5/6]'; // For ~0.833 and as a fallback
  };

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
  };

  const handleComment = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    router.push(`/${lang}/moments/${moment.id}#comments`);
  };
  
  const handleShare = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    e.preventDefault();
    setIsShareSheetOpen(true);
  };
  
  const handleSharePlatform = (platform: string) => {
    console.log(`Sharing to ${platform}`);
    setIsShareSheetOpen(false);
  };

  return (
    <>
      <Link href={`/${lang}/moments/${moment.id}`} className="block group">
        <div className="relative overflow-hidden rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
          <div style={{ paddingBottom: `${1 / aspectRatio * 100}%` }} />
          <div className="absolute top-0 left-0 w-full h-full">
            <Image
              src={momentImgSrc}
              alt={moment.title}
              fill
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              className="object-cover transition-transform duration-500 group-hover:scale-105"
              onError={() => setMomentImgSrc('https://picsum.photos/800/1200')}
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
          <div className="absolute inset-0 p-4 flex flex-col justify-end text-white">
            <p className="font-semibold text-lg leading-tight drop-shadow-md mb-3">{moment.title}</p>
            <div className="flex items-center gap-3">
              <Image src={character.character_avatar} alt={character.name} width={40} height={40} className="w-10 h-10 rounded-full border-2 border-white/50 flex-shrink-0" />
              <div className="flex flex-col flex-1 min-w-0">
                 <p className="text-sm font-medium drop-shadow-sm truncate">{character.name}</p>
                 <div className="flex items-center gap-2 mt-1 text-white/90">
                    <button onClick={handleLike} className="flex items-center gap-0.5 hover:text-white transition-colors">
                      <Heart size={16} className={isLiked ? 'text-red-500 fill-current' : ''} />
                      <span className="text-xs font-medium">{likeCount.toLocaleString()}</span>
                    </button>
                    <button onClick={handleComment} className="flex items-center gap-0.5 hover:text-white transition-colors">
                      <MessageCircle size={16} />
                      <span className="text-xs font-medium">{stats.shares.toLocaleString()}</span>
                    </button>
                    <button onClick={handleShare} className="flex items-center gap-0.5 hover:text-white transition-colors">
                      <Share2 size={16} />
                      <span className="text-xs font-medium">{stats.shares.toLocaleString()}</span>
                    </button>
                 </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
      <ShareBottomSheet 
        open={isShareSheetOpen}
        onClose={() => setIsShareSheetOpen(false)}
        onShare={handleSharePlatform}
      />
    </>
  );
};

export default MomentCard; 