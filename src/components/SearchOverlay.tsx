'use client';

import { Search, X } from 'lucide-react';
import React from 'react';

interface SearchOverlayProps {
  isOpen: boolean;
  onClose: () => void;
}

const SearchOverlay: React.FC<SearchOverlayProps> = ({ isOpen, onClose }) => {
  return (
    <div 
      className={`fixed inset-0 z-50 transition-opacity duration-300 ease-in-out ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
      onClick={onClose}
    >
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
      <div 
        className={`absolute top-0 right-0 h-full w-full bg-white dark:bg-gray-900 shadow-2xl transition-transform duration-300 ease-in-out ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
      >
        <div className="relative flex items-center border-b border-gray-200 dark:border-gray-700">
          <Search className="absolute left-4 text-gray-400" size={24} />
          <input
            type="search"
            placeholder="Search..."
            className="w-full bg-transparent text-lg pl-14 pr-12 py-5 focus:outline-none"
            autoFocus
          />
          <button
            onClick={onClose}
            className="absolute right-4 text-gray-500 hover:text-gray-800 dark:hover:text-white"
          >
            <X size={28} />
          </button>
        </div>
        <div className="p-6">
          <p className="text-center text-gray-500">
            Search for characters, stories, and more.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SearchOverlay; 