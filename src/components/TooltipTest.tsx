'use client';

import React from 'react';
import Tooltip from './Tooltip';
import { <PERSON>, <PERSON>, Settings, User } from 'lucide-react';

const TooltipTest: React.FC = () => {
  return (
    <div className="p-8 bg-background min-h-screen">
      <h1 className="text-2xl font-bold mb-8 text-foreground">Tooltip 测试</h1>
      
      <div className="space-y-8">
        {/* 右侧提示 */}
        <div className="flex items-center gap-4">
          <span className="text-foreground min-w-24">右侧提示:</span>
          <Tooltip content="这是脑力风暴功能" side="right">
            <button className="p-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
              <Brain size={20} />
            </button>
          </Tooltip>
        </div>

        {/* 左侧提示 */}
        <div className="flex items-center gap-4">
          <span className="text-foreground min-w-24">左侧提示:</span>
          <div className="ml-32">
            <Tooltip content="添加到收藏" side="left">
              <button className="p-3 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
                <Star size={20} />
              </button>
            </Tooltip>
          </div>
        </div>

        {/* 上方提示 */}
        <div className="flex items-center gap-4">
          <span className="text-foreground min-w-24">上方提示:</span>
          <div className="mt-16">
            <Tooltip content="系统设置" side="top">
              <button className="p-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <Settings size={20} />
              </button>
            </Tooltip>
          </div>
        </div>

        {/* 下方提示 */}
        <div className="flex items-center gap-4">
          <span className="text-foreground min-w-24">下方提示:</span>
          <Tooltip content="用户资料" side="bottom">
            <button className="p-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <User size={20} />
            </button>
          </Tooltip>
        </div>

        {/* 仿侧边栏折叠状态 */}
        <div className="flex items-center gap-4">
          <span className="text-foreground min-w-24">侧边栏模拟:</span>
          <div className="bg-white dark:bg-black border border-border rounded-lg p-2 space-y-2">
            <Tooltip content="Mindstorm" side="right">
              <button className="w-12 h-12 flex items-center justify-center rounded-lg text-sidebar-foreground hover:bg-primary hover:text-primary-foreground transition-colors">
                <Brain size={20} />
              </button>
            </Tooltip>
            <Tooltip content="Favorites" side="right">
              <button className="w-12 h-12 flex items-center justify-center rounded-lg text-sidebar-foreground hover:bg-primary hover:text-primary-foreground transition-colors">
                <Star size={20} />
              </button>
            </Tooltip>
            <Tooltip content="Settings" side="right">
              <button className="w-12 h-12 flex items-center justify-center rounded-lg text-sidebar-foreground hover:bg-primary hover:text-primary-foreground transition-colors">
                <Settings size={20} />
              </button>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TooltipTest; 