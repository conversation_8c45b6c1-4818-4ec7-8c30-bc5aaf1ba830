'use client';

import { FC } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { RefreshCw, Sparkles } from 'lucide-react';

interface CharBubbleProps {
  message: {
    text: string;
  };
  character: {
    id?: string;
    character_avatar?: string;
    name?: string;
  };
}

const CharBubble: FC<CharBubbleProps> = ({ message, character }) => {
  const params = useParams() as { lang?: string };
  const lang = params?.lang ?? 'en';
  return (
    <div className="flex flex-col items-start w-full">
      <div className="flex items-end gap-2 justify-start">
        {/* Avatar */}
        {character.character_avatar ? (
          <Link href={`/${lang}/character/${character.id ?? ''}`} prefetch={false}>
            <Image 
              src={character.character_avatar.replace(/'/g, "")} 
              alt={character.name || 'avatar'} 
              width={40} 
              height={40} 
              className="rounded-full w-10 h-10" 
              unoptimized
            />
          </Link>
        ) : (
          <div className="w-10 h-10 rounded-full bg-gray-700" />
        )}
        
        {/* Main Bubble Content */}
        <div className="max-w-xs md:max-w-md rounded-2xl shadow-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-none flex flex-col">
          {/* Message Text */}
          <div className="px-4 py-3">
            <p className="text-sm">{message.text}</p>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-200 dark:border-gray-700 mx-3"></div>
          
          {/* Internal Elements */}
          <div className="p-3">
            {/* Suggested Replies */}
            <div className="flex items-center gap-2">
              <button className="text-xs text-blue-500 border border-blue-500 rounded-full px-3 py-1 hover:bg-blue-500 hover:text-white transition-colors">
                Reply 1
              </button>
              <button className="text-xs text-blue-500 border border-blue-500 rounded-full px-3 py-1 hover:bg-blue-500 hover:text-white transition-colors">
                Reply 2
              </button>
            </div>

            {/* Rewards Info */}
            <div className="mt-3 text-xs text-gray-600 dark:text-gray-400">
              <span>💎 +3</span>
              <span className="ml-4">✨ +20</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Metadata Row (Below Bubble) */}
      <div className="flex items-center gap-3 mt-1.5 ml-12 text-white">
        <span className="text-xs">A few seconds ago</span>
        <button title="Regenerate" className="hover:opacity-80 transition-opacity">
          <RefreshCw size={14} className="text-white" />
        </button>
        <button title="New Topic" className="hover:opacity-80 transition-opacity">
          <Sparkles size={14} className="text-white fill-white" />
        </button>
      </div>
    </div>
  );
};

export default CharBubble; 