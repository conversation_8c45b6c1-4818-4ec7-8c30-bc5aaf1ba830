export type SampleMoment = {
  storyTemplateId: string;
  character: Character;
  moment: {
    id: string;
    title: string;
    image: string;
  };
  stats: {
    likes: number;
    shares: number;
  };
  aspectRatio: number;
  publishedAt: string;
};

export type Character = {
  id: string;
  name: string;
  character_avatar: string;
  character_bg_image: string;
};

// --- Single Source of Truth for Characters ---
export const characters: Character[] = [
  {
    id: 'seraphina-ember',
    name: '<PERSON><PERSON><PERSON>',
    // Avatar: Close-up, alluring gaze
    character_avatar: 'https://i.pinimg.com/564x/e7/7a/74/e77a7405232d2c1c107629577a83a48e.jpg',
    // BG: Full body, elegant, mysterious
    character_bg_image: 'https://i.pinimg.com/originals/a3/8b/13/a38b13025682e185058f8b8941014382.jpg'
  },
  {
    id: 'kaelen-shadow',
    name: '<PERSON><PERSON><PERSON>',
    // Avatar: Handsome, smirking
    character_avatar: 'https://i.pinimg.com/564x/1a/f1/b5/1af1b543787751996de45f3c5553b8f2.jpg',
    // BG: Full body, cool, leaning
    character_bg_image: 'https://i.pinimg.com/originals/f0/a6/a1/f0a6a1b184a441b444b4b4554c2a715f.jpg'
  },
  {
    id: 'lyra-starlight',
    name: 'Lyra',
    // Avatar: Playful, looking back
    character_avatar: 'https://i.pinimg.com/564x/4b/c9/2c/4bc92c803f2a7e78083e58c2132e65c9.jpg',
    // BG: Dynamic, magical
    character_bg_image: 'https://i.pinimg.com/originals/af/79/f1/af79f12185244585f95567b51206b162.jpg'
  },
  {
    id: 'riven-nightblade',
    name: 'Riven',
    // Avatar: Sharp, intense gaze
    character_avatar: 'https://i.pinimg.com/564x/a6/87/1c/a6871c5a932598345166b6e742876615.jpg',
    // BG: Action pose, warrior
    character_bg_image: 'https://i.pinimg.com/originals/d7/a7/0a/d7a70a48d8d35e72d2f78891d90f2bf0.jpg'
  },
  {
    id: 'elara-moonfire',
    name: 'Elara',
    // Avatar: Gentle, smiling
    character_avatar: 'https://i.pinimg.com/564x/72/0e/4f/720e4f8d6f1a8c46f199b422e0322b62.jpg',
    // BG: Serene, nature
    character_bg_image: 'https://i.pinimg.com/originals/0c/3b/a4/0c3ba441584288b56a31969242d99d8d.jpg'
  },
  {
    id: 'zane-cybernexus',
    name: 'Zane',
    // Avatar: Cyberpunk, cool
    character_avatar: 'https://i.pinimg.com/564x/3b/22/d7/3b22d71556a30155169ab51edc65a085.jpg',
    // BG: Futuristic city, techwear
    character_bg_image: 'https://i.pinimg.com/originals/d7/d8/9e/d7d89e5a190011855a8282e414c7d0d0.jpg'
  }
];


const sampleTitles = [
  "They told me the lost city was a myth. They were wrong.",
  "The last transmission from Titan base was just... static.",
  "My reflection in the chrome started talking back to me.",
  "Found this glowing mushroom in the Whispering Woods.",
  "The antique clock I bought ticked backwards. And then, it chimed thirteen.",
  "AIs dream of electric sheep. I dream of saving them.",
  "The dragon agreed to a truce, but its eyes still hold a burning grudge.",
  "Woke up with a tattoo I don't remember getting. It seems to be a map."
];

const usedTitles = new Set<string>();

function getRandomUniqueTitle(): string {
  if (usedTitles.size >= sampleTitles.length) {
    usedTitles.clear(); // Reset if all titles are used
  }
  let title;
  do {
    title = sampleTitles[Math.floor(Math.random() * sampleTitles.length)];
  } while (usedTitles.has(title));
  usedTitles.add(title);
  return title;
}

// Generates portrait aspect ratios (width / height)
export function generateRandomAspectRatio() {
  const ratios = [5/10, 5/8, 5/6];
  return ratios[Math.floor(Math.random()*ratios.length)];
}

export function generateAllSampleMoments(count: number = 8): SampleMoment[] {
  const allMoments: SampleMoment[] = [];

  for (let i = 0; i < count; i++) {
    const title = getRandomUniqueTitle();
    // Pick a random character from the centralized list
    const character = characters[i % characters.length];
    const aspectRatio = generateRandomAspectRatio();
    
    const moment: SampleMoment = {
      storyTemplateId: `story-template-${i}`,
      character: character,
      moment: {
        id: `moment-${i}`, // Simple, unique ID
        title: title,
        image: `https://picsum.photos/seed/${title.substring(0, 10)}/500/${Math.round(500 / aspectRatio)}`
      },
      stats: {
        likes: Math.floor(Math.random() * 5000) + 100,
        shares: Math.floor(Math.random() * 500) + 10,
      },
      aspectRatio: aspectRatio,
      publishedAt: `${Math.floor(Math.random() * 10) + 1}h ago`,
    };
    allMoments.push(moment);
  }

  return allMoments;
} 